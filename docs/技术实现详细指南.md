# 🔧 智慧养鹅项目技术实现详细指南

## 📋 概述

本指南提供智慧养鹅全栈项目各阶段任务的具体技术实现方案，包括代码示例、API设计、数据库操作和测试方法。

---

## 🚀 第一阶段：短期优化技术实现

### 任务1.1：修复模板文件数据绑定问题

#### 🔍 问题识别
需要修复的文件列表：
- `pages/price/price-detail/price-detail.js` - 价格详情页面
- `pages/task/detail/detail.js` - 任务详情页面  
- `pages/vaccination/task-detail/task-detail.js` - 防疫任务详情
- `backend/controllers/tenant.controller.js` - 租户控制器

#### 💻 技术实现方案

**1. 价格详情页面修复**

```javascript
// pages/price/price-detail/price-detail.js
const { unifiedApiClient } = require('../../../utils/unified-api-client.js');
const { apiResponseHandler } = require('../../../utils/api-response-handler.js');

Page({
  data: {
    loading: true,
    priceList: [],
    selectedBreed: '',
    activeType: 'live'
  },

  onLoad(options) {
    this.setData({
      activeType: options.type || 'live',
      selectedBreed: options.breed || ''
    });
    this.loadPriceData();
  },

  // 替换模拟数据为真实API调用
  async loadPriceData() {
    try {
      this.setData({ loading: true });
      
      const response = await unifiedApiClient.get('/api/v1/price/list', {
        type: this.data.activeType,
        breed: this.data.selectedBreed,
        page: 1,
        limit: 20
      });
      
      const result = apiResponseHandler.handle(response);
      if (result.success) {
        this.setData({
          priceList: result.data.list || [],
          totalCount: result.data.total || 0,
          loading: false
        });
        
        // 绘制价格趋势图
        this.drawPriceChart(result.data.chartData);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('加载价格数据失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'error'
      });
      this.setData({ loading: false });
    }
  },

  // 品种详细数据加载
  async loadBreedDetail(breed) {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const response = await unifiedApiClient.get(`/api/v1/price/breed/${breed}`, {
        days: 30 // 获取30天数据
      });
      
      const result = apiResponseHandler.handle(response);
      if (result.success) {
        this.setData({
          breedDetail: result.data,
          priceHistory: result.data.history || [],
          marketAnalysis: result.data.analysis || {}
        });
        
        this.drawPriceChart();
      }
      
      wx.hideLoading();
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '加载品种详情失败',
        icon: 'error'
      });
    }
  }
});
```

**2. 后端API端点创建**

```javascript
// backend/controllers/price.controller.js
const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');
const PriceService = require('../services/price.service');

/**
 * 获取价格列表
 */
const getPriceList = async (req, res) => {
  try {
    const { type, breed, page = 1, limit = 20 } = req.query;
    const tenantId = req.user.tenantId;
    
    const result = await PriceService.getPriceList({
      tenantId,
      type,
      breed,
      page: parseInt(page),
      limit: parseInt(limit)
    });
    
    res.json(generateSuccessResponse(result, '获取价格列表成功'));
  } catch (error) {
    console.error('获取价格列表失败:', error);
    res.status(500).json(generateErrorResponse('获取价格列表失败', error.message));
  }
};

/**
 * 获取品种详细信息
 */
const getBreedDetail = async (req, res) => {
  try {
    const { breed } = req.params;
    const { days = 30 } = req.query;
    const tenantId = req.user.tenantId;
    
    const result = await PriceService.getBreedDetail({
      tenantId,
      breed,
      days: parseInt(days)
    });
    
    res.json(generateSuccessResponse(result, '获取品种详情成功'));
  } catch (error) {
    console.error('获取品种详情失败:', error);
    res.status(500).json(generateErrorResponse('获取品种详情失败', error.message));
  }
};

module.exports = {
  getPriceList,
  getBreedDetail
};
```

**3. 价格服务层实现**

```javascript
// backend/services/price.service.js
const db = require('../config/database');

class PriceService {
  /**
   * 获取价格列表
   */
  static async getPriceList({ tenantId, type, breed, page, limit }) {
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE tenant_id = ?';
    let params = [tenantId];
    
    if (type) {
      whereClause += ' AND type = ?';
      params.push(type);
    }
    
    if (breed) {
      whereClause += ' AND breed = ?';
      params.push(breed);
    }
    
    // 获取总数
    const [countResult] = await db.execute(
      `SELECT COUNT(*) as total FROM price_records ${whereClause}`,
      params
    );
    
    // 获取列表数据
    const [rows] = await db.execute(`
      SELECT 
        id, breed, type, price, unit, market_name, 
        record_date, created_at, updated_at
      FROM price_records 
      ${whereClause}
      ORDER BY record_date DESC, created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, limit, offset]);
    
    // 获取图表数据
    const chartData = await this.getPriceChartData({ tenantId, type, breed });
    
    return {
      list: rows,
      total: countResult[0].total,
      chartData,
      pagination: {
        page,
        limit,
        total: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit)
      }
    };
  }
  
  /**
   * 获取品种详细信息
   */
  static async getBreedDetail({ tenantId, breed, days }) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    // 获取历史价格数据
    const [historyRows] = await db.execute(`
      SELECT 
        price, record_date, market_name
      FROM price_records 
      WHERE tenant_id = ? AND breed = ? AND record_date >= ?
      ORDER BY record_date ASC
    `, [tenantId, breed, startDate.toISOString().split('T')[0]]);
    
    // 计算价格分析
    const analysis = await this.calculatePriceAnalysis(historyRows);
    
    return {
      breed,
      history: historyRows,
      analysis,
      summary: {
        totalRecords: historyRows.length,
        dateRange: {
          start: startDate.toISOString().split('T')[0],
          end: new Date().toISOString().split('T')[0]
        }
      }
    };
  }
  
  /**
   * 获取价格图表数据
   */
  static async getPriceChartData({ tenantId, type, breed }) {
    let whereClause = 'WHERE tenant_id = ?';
    let params = [tenantId];
    
    if (type) {
      whereClause += ' AND type = ?';
      params.push(type);
    }
    
    if (breed) {
      whereClause += ' AND breed = ?';
      params.push(breed);
    }
    
    const [rows] = await db.execute(`
      SELECT 
        DATE(record_date) as date,
        AVG(price) as avgPrice,
        MIN(price) as minPrice,
        MAX(price) as maxPrice,
        COUNT(*) as recordCount
      FROM price_records 
      ${whereClause}
      AND record_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      GROUP BY DATE(record_date)
      ORDER BY date ASC
    `, params);
    
    return {
      dates: rows.map(row => row.date),
      avgPrices: rows.map(row => parseFloat(row.avgPrice)),
      minPrices: rows.map(row => parseFloat(row.minPrice)),
      maxPrices: rows.map(row => parseFloat(row.maxPrice))
    };
  }
  
  /**
   * 计算价格分析
   */
  static async calculatePriceAnalysis(historyData) {
    if (historyData.length === 0) {
      return {
        trend: 'stable',
        avgPrice: 0,
        priceChange: 0,
        volatility: 0
      };
    }
    
    const prices = historyData.map(item => parseFloat(item.price));
    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    
    const firstPrice = prices[0];
    const lastPrice = prices[prices.length - 1];
    const priceChange = ((lastPrice - firstPrice) / firstPrice * 100).toFixed(2);
    
    // 计算波动率
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - avgPrice, 2), 0) / prices.length;
    const volatility = Math.sqrt(variance);
    
    let trend = 'stable';
    if (priceChange > 5) trend = 'rising';
    else if (priceChange < -5) trend = 'falling';
    
    return {
      trend,
      avgPrice: parseFloat(avgPrice.toFixed(2)),
      priceChange: parseFloat(priceChange),
      volatility: parseFloat(volatility.toFixed(2)),
      recommendation: this.generatePriceRecommendation(trend, priceChange)
    };
  }
  
  /**
   * 生成价格建议
   */
  static generatePriceRecommendation(trend, priceChange) {
    if (trend === 'rising' && priceChange > 10) {
      return '价格上涨明显，建议适时出售';
    } else if (trend === 'falling' && priceChange < -10) {
      return '价格下跌较多，建议观望或补栏';
    } else {
      return '价格相对稳定，可按计划进行';
    }
  }
}

module.exports = PriceService;
```

### 任务1.2：完善CRUD操作功能

#### 🔍 需要完善的控制器
- `backend/controllers/tenant.controller.js`
- `backend/controllers/knowledge.controller.js`
- `backend/controllers/shop.controller.js`

#### 💻 租户控制器完善

```javascript
// backend/controllers/tenant.controller.js
const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');
const TenantService = require('../services/tenant-management.service');

/**
 * 获取租户列表
 */
const getTenants = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, plan, search } = req.query;
    
    const result = await TenantService.getTenantList({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      plan,
      search
    });
    
    res.json(generateSuccessResponse(result, '获取租户列表成功'));
  } catch (error) {
    console.error('获取租户列表失败:', error);
    res.status(500).json(generateErrorResponse('获取租户列表失败', error.message));
  }
};

/**
 * 创建租户
 */
const createTenant = async (req, res) => {
  try {
    const tenantData = req.body;
    
    // 数据验证
    const validation = validateTenantData(tenantData);
    if (!validation.isValid) {
      return res.status(400).json(generateErrorResponse('数据验证失败', validation.errors));
    }
    
    const result = await TenantService.createTenant(tenantData);
    
    res.status(201).json(generateSuccessResponse(result, '创建租户成功'));
  } catch (error) {
    console.error('创建租户失败:', error);
    res.status(500).json(generateErrorResponse('创建租户失败', error.message));
  }
};

/**
 * 更新租户
 */
const updateTenant = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // 数据验证
    const validation = validateTenantUpdateData(updateData);
    if (!validation.isValid) {
      return res.status(400).json(generateErrorResponse('数据验证失败', validation.errors));
    }
    
    const result = await TenantService.updateTenant(id, updateData);
    
    res.json(generateSuccessResponse(result, '更新租户成功'));
  } catch (error) {
    console.error('更新租户失败:', error);
    res.status(500).json(generateErrorResponse('更新租户失败', error.message));
  }
};

/**
 * 删除租户
 */
const deleteTenant = async (req, res) => {
  try {
    const { id } = req.params;
    
    await TenantService.deleteTenant(id);
    
    res.json(generateSuccessResponse(null, '删除租户成功'));
  } catch (error) {
    console.error('删除租户失败:', error);
    res.status(500).json(generateErrorResponse('删除租户失败', error.message));
  }
};

/**
 * 获取租户详情
 */
const getTenantDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await TenantService.getTenantById(id);
    
    if (!result) {
      return res.status(404).json(generateErrorResponse('租户不存在'));
    }
    
    res.json(generateSuccessResponse(result, '获取租户详情成功'));
  } catch (error) {
    console.error('获取租户详情失败:', error);
    res.status(500).json(generateErrorResponse('获取租户详情失败', error.message));
  }
};

/**
 * 租户数据验证
 */
function validateTenantData(data) {
  const errors = [];
  
  if (!data.tenantCode || data.tenantCode.trim() === '') {
    errors.push('租户代码不能为空');
  }
  
  if (!data.companyName || data.companyName.trim() === '') {
    errors.push('公司名称不能为空');
  }
  
  if (!data.contactName || data.contactName.trim() === '') {
    errors.push('联系人姓名不能为空');
  }
  
  if (!data.contactEmail || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.contactEmail)) {
    errors.push('请输入有效的邮箱地址');
  }
  
  if (!data.contactPhone || !/^1[3-9]\d{9}$/.test(data.contactPhone)) {
    errors.push('请输入有效的手机号码');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 租户更新数据验证
 */
function validateTenantUpdateData(data) {
  const errors = [];
  
  if (data.contactEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.contactEmail)) {
    errors.push('请输入有效的邮箱地址');
  }
  
  if (data.contactPhone && !/^1[3-9]\d{9}$/.test(data.contactPhone)) {
    errors.push('请输入有效的手机号码');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

module.exports = {
  getTenants,
  createTenant,
  updateTenant,
  deleteTenant,
  getTenantDetail
};
```

---

## 📊 数据库设计和优化

### 价格记录表优化

```sql
-- 价格记录表结构优化
CREATE TABLE IF NOT EXISTS price_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT NOT NULL,
  breed VARCHAR(50) NOT NULL COMMENT '品种',
  type ENUM('live', 'processed', 'egg') NOT NULL COMMENT '类型：活鹅、加工品、鹅蛋',
  price DECIMAL(10,2) NOT NULL COMMENT '价格',
  unit VARCHAR(20) NOT NULL DEFAULT '元/只' COMMENT '单位',
  market_name VARCHAR(100) COMMENT '市场名称',
  record_date DATE NOT NULL COMMENT '记录日期',
  source VARCHAR(50) COMMENT '数据来源',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_tenant_breed_date (tenant_id, breed, record_date),
  INDEX idx_tenant_type_date (tenant_id, type, record_date),
  INDEX idx_record_date (record_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格记录表';
```

### 性能优化索引

```sql
-- 添加复合索引优化查询性能
ALTER TABLE price_records ADD INDEX idx_tenant_breed_type_date (tenant_id, breed, type, record_date);
ALTER TABLE price_records ADD INDEX idx_created_at (created_at);

-- 租户表索引优化
ALTER TABLE tenants ADD INDEX idx_status_plan (status, subscription_plan);
ALTER TABLE tenants ADD INDEX idx_created_at (created_at);
```

---

## 🧪 测试方案

### 单元测试示例

```javascript
// tests/services/price.service.test.js
const PriceService = require('../../backend/services/price.service');
const db = require('../../backend/config/database');

describe('PriceService', () => {
  beforeEach(async () => {
    // 清理测试数据
    await db.execute('DELETE FROM price_records WHERE tenant_id = 999');
  });
  
  afterEach(async () => {
    // 清理测试数据
    await db.execute('DELETE FROM price_records WHERE tenant_id = 999');
  });
  
  describe('getPriceList', () => {
    it('应该返回正确的价格列表', async () => {
      // 插入测试数据
      await db.execute(`
        INSERT INTO price_records (tenant_id, breed, type, price, unit, record_date)
        VALUES (999, '白鹅', 'live', 25.50, '元/只', '2025-08-27')
      `);
      
      const result = await PriceService.getPriceList({
        tenantId: 999,
        type: 'live',
        page: 1,
        limit: 10
      });
      
      expect(result.list).toHaveLength(1);
      expect(result.list[0].breed).toBe('白鹅');
      expect(result.list[0].price).toBe('25.50');
      expect(result.total).toBe(1);
    });
  });
  
  describe('calculatePriceAnalysis', () => {
    it('应该正确计算价格分析', async () => {
      const historyData = [
        { price: '20.00' },
        { price: '22.00' },
        { price: '25.00' },
        { price: '24.00' }
      ];
      
      const analysis = await PriceService.calculatePriceAnalysis(historyData);
      
      expect(analysis.trend).toBe('rising');
      expect(analysis.avgPrice).toBe(22.75);
      expect(analysis.priceChange).toBe('20.00');
    });
  });
});
```

---

**文档版本**: v1.0  
**创建时间**: 2025年8月27日  
**适用阶段**: 第一阶段短期优化
