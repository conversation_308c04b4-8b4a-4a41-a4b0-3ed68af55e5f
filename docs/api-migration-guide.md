# 🔄 API架构统一迁移指南

## 📋 概述

本指南介绍如何从现有的多个API客户端迁移到统一的API架构，解决API调用分散、版本不一致、缺乏统一规范等问题。

## 🏗️ 迁移架构对比

### 迁移前 (现状)

```
┌─────────────────────────────────────────────────┐
│              分散的API客户端                     │
├─────────────────────────────────────────────────┤
│  utils/api.js          │  基础API接口定义        │
│  utils/request.js      │  HTTP请求封装          │
│  utils/api-client.js   │  类型安全客户端        │
│  utils/request-v2.js   │  新版请求工具          │
└─────────────────────────────────────────────────┘
                    ↓ 问题
┌─────────────────────────────────────────────────┐
│ ❌ 接口定义分散，难以维护                        │
│ ❌ 请求方式不统一，版本兼容性差                   │
│ ❌ 错误处理不一致                               │
│ ❌ 缺乏权限控制和缓存机制                        │
│ ❌ 租户隔离支持不完善                           │
└─────────────────────────────────────────────────┘
```

### 迁移后 (目标)

```
┌─────────────────────────────────────────────────┐
│              统一API架构                        │
├─────────────────────────────────────────────────┤
│  UnifiedApiClient     │  统一API客户端          │
│  ApiEndpoints         │  规范化接口定义        │
│  Permission Control   │  权限控制集成          │
│  Tenant Support       │  多租户支持            │
│  Caching System       │  智能缓存系统          │
│  Error Handling       │  统一错误处理          │
└─────────────────────────────────────────────────┘
                    ↓ 优势
┌─────────────────────────────────────────────────┐
│ ✅ 统一的API调用方式                            │
│ ✅ 自动权限验证                                 │
│ ✅ 智能缓存和错误重试                           │
│ ✅ 完整的SAAS多租户支持                         │
│ ✅ 类型安全和版本兼容                           │
└─────────────────────────────────────────────────┘
```

## 🔧 迁移步骤

### 第一步：引入统一API客户端

#### 1.1 在页面中引入新客户端

```javascript
// 旧方式 - 分散引入
const { get, post } = require('../../utils/request.js');
const api = require('../../utils/api.js');

// 新方式 - 统一引入
const { api } = require('../../utils/unified-api-client.js');
const { endpoints } = require('../../utils/api-endpoints.js');
```

#### 1.2 更新API调用方式

```javascript
// 旧方式 - 直接使用硬编码URL
const response = await get('/api/v1/flocks');

// 新方式 - 使用规范化端点
const response = await api.get(endpoints.build('FLOCKS', 'LIST').fullPath);

// 或者使用更简洁的方式
const flockEndpoint = endpoints.get('FLOCKS', 'LIST');
const response = await api.get(flockEndpoint.fullPath, {
  cache: flockEndpoint.cache,
  showLoading: true
});
```

### 第二步：权限集成

#### 2.1 带权限验证的API调用

```javascript
// 旧方式 - 手动权限检查
const { hasPermission } = require('../../utils/auth-helper.js');
if (hasPermission('flock_create')) {
  const response = await post('/api/v1/flocks', data);
}

// 新方式 - 自动权限验证
const { PERMISSIONS } = require('../../utils/unified-permission.js');
try {
  const response = await api.callWithPermission(
    'POST',
    endpoints.build('FLOCKS', 'CREATE').fullPath,
    [PERMISSIONS.FLOCK_CREATE],
    { data: flockData, showLoading: true }
  );
} catch (error) {
  if (error.type === 'permission_error') {
    // 自动处理权限错误
    console.log('权限不足:', error.message);
  }
}
```

#### 2.2 管理员专用API调用

```javascript
// 新方式 - 管理员API调用
try {
  const response = await api.adminCall(
    'GET',
    endpoints.build('USERS', 'LIST').fullPath,
    { showLoading: true }
  );
} catch (error) {
  if (error.type === 'permission_error') {
    wx.showToast({ title: '需要管理员权限', icon: 'none' });
  }
}
```

### 第三步：批量替换现有调用

#### 3.1 创建替换脚本

```bash
#!/bin/bash
# 批量替换脚本：replace-api-calls.sh

# 替换基础请求方法
find . -name "*.js" -exec sed -i 's/require.*request\.js.*//g' {} \;
find . -name "*.js" -exec sed -i 's/const.*get.*post.*=.*//g' {} \;

# 添加统一API客户端引入
find . -name "*.js" -exec sed -i '1i\const { api } = require("../../utils/unified-api-client.js");' {} \;
find . -name "*.js" -exec sed -i '2i\const { endpoints } = require("../../utils/api-endpoints.js");' {} \;

# 替换常见API调用模式
find . -name "*.js" -exec sed -i 's/get(\s*[\'"]\(.*\)[\'"]\s*)/api.get(endpoints.build(...).fullPath)/g' {} \;
find . -name "*.js" -exec sed -i 's/post(\s*[\'"]\(.*\)[\'"]\s*,\s*\(.*\))/api.post(endpoints.build(...).fullPath, \2)/g' {} \;
```

#### 3.2 页面级迁移示例

```javascript
// ===============================
// 原页面代码 (pages/flocks/flocks.js)
// ===============================
const { get, post, put, del } = require('../../utils/request.js');
const api = require('../../utils/api.js');

Page({
  data: {
    flocks: []
  },

  async onLoad() {
    await this.loadFlocks();
  },

  async loadFlocks() {
    try {
      wx.showLoading({ title: '加载中...' });
      const response = await get('/api/v1/flocks');
      this.setData({ flocks: response.data || [] });
    } catch (error) {
      wx.showToast({ title: '加载失败', icon: 'none' });
    } finally {
      wx.hideLoading();
    }
  },

  async createFlock() {
    // 手动权限检查
    const user = wx.getStorageSync('userInfo');
    if (!user || user.role !== 'admin') {
      wx.showToast({ title: '权限不足', icon: 'none' });
      return;
    }

    try {
      const data = { name: '新鹅群', count: 100 };
      await post('/api/v1/flocks', data);
      await this.loadFlocks();
      wx.showToast({ title: '创建成功' });
    } catch (error) {
      wx.showToast({ title: '创建失败', icon: 'none' });
    }
  }
});

// ===============================
// 迁移后代码 (pages/flocks/flocks.js)
// ===============================
const { api } = require('../../utils/unified-api-client.js');
const { endpoints } = require('../../utils/api-endpoints.js');
const { PERMISSIONS } = require('../../utils/unified-permission.js');

Page({
  data: {
    flocks: []
  },

  async onLoad() {
    await this.loadFlocks();
  },

  async loadFlocks() {
    try {
      const flocksEndpoint = endpoints.get('FLOCKS', 'LIST');
      const response = await api.get(flocksEndpoint.fullPath, {
        showLoading: true,
        loadingText: '加载鹅群中...',
        cache: flocksEndpoint.cache
      });
      this.setData({ flocks: response || [] });
    } catch (error) {
      // 错误已由统一客户端处理
      console.error('加载鹅群失败:', error);
    }
  },

  async createFlock() {
    try {
      const createEndpoint = endpoints.get('FLOCKS', 'CREATE');
      const data = { name: '新鹅群', count: 100 };
      
      await api.callWithPermission(
        createEndpoint.method,
        createEndpoint.fullPath,
        createEndpoint.permissions,
        { 
          data: data,
          showLoading: true,
          loadingText: '创建中...'
        }
      );
      
      await this.loadFlocks();
      wx.showToast({ title: '创建成功', icon: 'success' });
    } catch (error) {
      // 权限错误已自动处理
      console.error('创建鹅群失败:', error);
    }
  }
});
```

### 第四步：组件级迁移

#### 4.1 自定义组件迁移

```javascript
// ===============================
// 原组件代码 (components/flock-list/flock-list.js)
// ===============================
const { get } = require('../../utils/request.js');

Component({
  data: {
    flocks: []
  },

  lifetimes: {
    attached() {
      this.loadData();
    }
  },

  methods: {
    async loadData() {
      try {
        const response = await get('/api/v1/flocks?status=active');
        this.setData({ flocks: response.data });
      } catch (error) {
        console.error('加载失败:', error);
      }
    }
  }
});

// ===============================
// 迁移后代码 (components/flock-list/flock-list.js)
// ===============================
const { api } = require('../../utils/unified-api-client.js');
const { endpoints } = require('../../utils/api-endpoints.js');

Component({
  data: {
    flocks: []
  },

  lifetimes: {
    attached() {
      this.loadData();
    }
  },

  methods: {
    async loadData() {
      try {
        const listEndpoint = endpoints.get('FLOCKS', 'LIST');
        const response = await api.get(listEndpoint.fullPath, {
          query: { status: 'active' },
          cache: listEndpoint.cache,
          showError: false // 组件内部不显示全局错误
        });
        this.setData({ flocks: response });
      } catch (error) {
        // 组件级错误处理
        this.triggerEvent('loadError', { error });
      }
    }
  }
});
```

### 第五步：错误处理统一

#### 5.1 全局错误处理

```javascript
// app.js - 应用级错误处理
const { api } = require('./utils/unified-api-client.js');

App({
  onLaunch() {
    // 添加全局响应拦截器
    api.addResponseInterceptor(
      (response) => {
        // 全局成功处理
        return response;
      },
      (error) => {
        // 全局错误处理
        if (error.type === 'network_error') {
          // 网络错误处理
          this.handleNetworkError(error);
        } else if (error.statusCode === 401) {
          // 认证失效处理
          this.handleAuthExpired();
        }
        return error;
      }
    );
  },

  handleNetworkError(error) {
    wx.showModal({
      title: '网络连接失败',
      content: '请检查网络设置后重试',
      showCancel: false
    });
  },

  handleAuthExpired() {
    wx.clearStorageSync();
    wx.reLaunch({ url: '/pages/login/login' });
  }
});
```

### 第六步：性能优化配置

#### 6.1 缓存策略配置

```javascript
// 页面级缓存配置
Page({
  onLoad() {
    // 启用页面级缓存
    this.initApiCache();
  },

  initApiCache() {
    // 为当前页面创建专用API客户端
    const { UnifiedApiClient } = require('../../utils/unified-api-client.js');
    this.apiClient = new UnifiedApiClient({
      cache: true,
      cacheTTL: 300000, // 5分钟缓存
      timeout: 10000    // 10秒超时
    });
  },

  async loadData() {
    // 使用带缓存的客户端
    const response = await this.apiClient.get('/api/v1/flocks', {
      cache: true,
      showLoading: true
    });
    this.setData({ flocks: response });
  }
});
```

## 📝 迁移检查清单

### 代码迁移检查

- [ ] 移除旧的API客户端引入
- [ ] 引入统一API客户端和端点定义
- [ ] 替换所有硬编码API路径
- [ ] 添加权限验证
- [ ] 更新错误处理逻辑
- [ ] 配置缓存策略
- [ ] 测试所有API调用

### 功能验证检查

- [ ] 用户认证流程正常
- [ ] 权限控制生效
- [ ] 缓存机制工作
- [ ] 错误处理正确
- [ ] 多租户隔离有效
- [ ] 文件上传/下载功能
- [ ] 请求重试机制

### 性能测试检查

- [ ] API响应时间正常
- [ ] 缓存命中率合理
- [ ] 内存使用稳定
- [ ] 网络错误恢复
- [ ] 并发请求处理

## 🚨 常见问题与解决方案

### 问题1：权限检查失败

```javascript
// 问题：权限检查不通过
// 解决：检查权限定义和用户角色

// 调试权限问题
const { UnifiedPermissionManager, PERMISSIONS } = require('../../utils/unified-permission.js');

console.log('当前用户:', UnifiedPermissionManager.getCurrentUser());
console.log('用户权限:', UnifiedPermissionManager.getUserPermissions());
console.log('需要权限:', PERMISSIONS.FLOCK_CREATE);
console.log('权限检查结果:', UnifiedPermissionManager.hasPermission(PERMISSIONS.FLOCK_CREATE));
```

### 问题2：缓存数据过期

```javascript
// 问题：缓存数据不更新
// 解决：手动清除缓存或设置合适的TTL

// 清除特定缓存
api.clearCache();

// 强制刷新数据
const response = await api.get(endpoint.fullPath, {
  cache: false, // 跳过缓存
  showLoading: true
});
```

### 问题3：租户隔离失效

```javascript
// 问题：访问了其他租户的数据
// 解决：检查租户配置和请求头

const tenantConfig = require('../../utils/tenant-config.js');
console.log('当前租户配置:', tenantConfig.getTenantConfig());

// 手动设置租户信息
tenantConfig.setTenantConfig({
  tenantCode: 'DEMO_TENANT',
  tenantId: 1
});
```

### 问题4：API版本兼容性

```javascript
// 问题：新旧API响应格式不兼容
// 解决：使用统一的响应处理

// 自定义响应处理
api.addResponseInterceptor((response) => {
  // 处理特定格式的响应
  if (response.data && response.data.legacy_format) {
    return {
      success: true,
      data: response.data.result
    };
  }
  return response;
});
```

## 📚 相关文档

- [统一权限管理系统使用指南](./unified-permission-guide.md)
- [API接口规范文档](./api-specification.md)
- [SAAS多租户开发指南](./saas-development-guide.md)
- [性能优化最佳实践](./performance-optimization.md)

## 🔄 迁移时间表

### 第1周：准备阶段
- 创建统一API客户端
- 定义API接口规范
- 编写迁移脚本

### 第2周：核心功能迁移
- 迁移认证相关接口
- 迁移核心业务接口
- 更新权限控制

### 第3周：扩展功能迁移
- 迁移OA系统接口
- 迁移商城管理接口
- 迁移AI服务接口

### 第4周：测试和优化
- 全面功能测试
- 性能优化调整
- 文档更新完善

## ✅ 迁移完成标志

当满足以下条件时，API架构统一迁移完成：

1. **代码统一**：所有页面和组件都使用统一API客户端
2. **权限集成**：所有API调用都通过权限验证
3. **错误统一**：错误处理逻辑统一且完善
4. **性能优化**：缓存机制有效，响应速度良好
5. **测试通过**：所有功能测试和性能测试通过
6. **文档完整**：API使用文档和开发规范完善

🎉 完成迁移后，项目将拥有统一、规范、高效的API架构！