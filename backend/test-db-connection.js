const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'smart_goose_saas_platform',
    charset: 'utf8mb4'
};

async function testDatabaseConnection() {
    console.log('🔍 测试数据库连接...');
    console.log('配置信息:', {
        host: dbConfig.host,
        user: dbConfig.user,
        database: dbConfig.database,
        password: dbConfig.password ? '***' : '(空)'
    });

    try {
        // 尝试连接数据库
        const connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');

        // 检查数据库是否存在
        const [databases] = await connection.execute('SHOW DATABASES');
        console.log('📋 可用数据库:', databases.map(db => Object.values(db)[0]));

        // 检查目标数据库是否存在
        const targetDbExists = databases.some(db => Object.values(db)[0] === dbConfig.database);
        if (!targetDbExists) {
            console.log(`❌ 目标数据库 '${dbConfig.database}' 不存在`);
            console.log('💡 请先创建数据库:');
            console.log(`   CREATE DATABASE ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`);
            await connection.end();
            return false;
        }

        console.log(`✅ 目标数据库 '${dbConfig.database}' 存在`);

        // 检查关键表是否存在
        const [tables] = await connection.execute('SHOW TABLES');
        console.log('📋 数据库表:', tables.map(table => Object.values(table)[0]));

        const requiredTables = ['tenants', 'users', 'flocks'];
        const missingTables = [];

        for (const table of requiredTables) {
            const tableExists = tables.some(t => Object.values(t)[0] === table);
            if (!tableExists) {
                missingTables.push(table);
            }
        }

        if (missingTables.length > 0) {
            console.log('❌ 缺少必要的表:', missingTables);
            console.log('💡 请运行数据库迁移脚本创建表结构');
        } else {
            console.log('✅ 所有必要的表都存在');
        }

        await connection.end();
        return true;

    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('💡 请检查数据库用户名和密码');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('💡 请确保MySQL服务正在运行');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.log('💡 数据库不存在，请先创建数据库');
        }
        
        return false;
    }
}

// 运行测试
if (require.main === module) {
    testDatabaseConnection().then(success => {
        if (success) {
            console.log('🎉 数据库连接测试完成');
        } else {
            console.log('❌ 数据库连接测试失败');
        }
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testDatabaseConnection };
