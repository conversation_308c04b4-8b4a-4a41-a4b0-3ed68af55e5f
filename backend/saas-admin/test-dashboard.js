const express = require('express');
const path = require('path');
require('dotenv').config();

// 导入dashboard路由
const dashboardRouter = require('./routes/dashboard');

const app = express();

// 设置视图引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 使用dashboard路由
app.use('/', dashboardRouter);

// 错误处理
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({
        error: 'Internal Server Error',
        message: err.message
    });
});

const PORT = 4001;

app.listen(PORT, () => {
    console.log(`测试服务器运行在 http://localhost:${PORT}`);
    console.log('请访问 http://localhost:4001 来测试dashboard');
});
