/**
 * 智慧养鹅SAAS管理后台 - 统一路由架构
 * 
 * 业务逻辑架构：
 * 1. 平台级管理功能 - 影响所有租户，需要超级管理员权限
 *    - 今日鹅价管理
 *    - 平台公告管理  
 *    - 知识库管理
 *    - 商城模块管理
 *    - 租户管理
 *    - AI大模型配置
 *    - 系统设置
 * 
 * 2. 租户级管理功能 - 数据完全隔离，每个租户只能看到自己的数据
 *    - 鹅群管理
 *    - 生产物料管理
 *    - 健康记录管理
 *    - 财务管理
 */

const express = require('express');
const bcrypt = require('bcrypt');
const TenantManagementService = require('../../services/tenant-management');

const router = express.Router();
const tenantService = new TenantManagementService();

// ==================== 认证中间件 ====================

// 验证登录状态
const requireAuth = (req, res, next) => {
  console.log('登录状态检查:', req.session?.user ? '已登录' : '未登录');
  if (!req.session || !req.session.user) {
    if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
      return res.status(401).json({ success: false, message: '请先登录' });
    }
    return res.redirect('/login');
  }
  next();
};

// 验证平台管理员权限（超级管理员）
const requirePlatformAdmin = (req, res, next) => {
  if (!req.session.user || !['super_admin', 'platform_admin'].includes(req.session.user.role)) {
    if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
      return res.status(403).json({ success: false, message: '需要平台管理员权限' });
    }
    return res.status(403).render('error', { 
      title: '权限不足', 
      message: '需要平台管理员权限才能访问此页面',
      layout: 'layouts/main'
    });
  }
  next();
};

// 验证租户管理员权限
const requireTenantAdmin = (req, res, next) => {
  if (!req.session.user || !['super_admin', 'platform_admin', 'tenant_admin'].includes(req.session.user.role)) {
    if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
      return res.status(403).json({ success: false, message: '需要租户管理员权限' });
    }
    return res.status(403).render('error', { 
      title: '权限不足', 
      message: '需要租户管理员权限才能访问此页面',
      layout: 'layouts/main' 
    });
  }
  next();
};

// ==================== 认证路由 ====================

// 登录页面
router.get('/login', (req, res) => {
  if (req.session && req.session.user) {
    return res.redirect('/dashboard');
  }
  
  res.render('auth/login', {
    title: '登录 - 智慧养鹅SAAS管理平台',
    error: req.query.error,
    layout: false // 登录页面不使用主布局
  });
});

// 登录处理
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('登录尝试:', { username, password: '***' });
    
    if (!username || !password) {
      return res.redirect('/login?error=' + encodeURIComponent('用户名和密码不能为空'));
    }

    // 查询用户（支持用户名或邮箱登录）
    const db = req.app.locals.db;
    console.log('[DB Query] SELECT * FROM platform_admins WHERE (username = ? OR email = ?) AND status = ?', [username, username, 'active']);
    
    const users = await db.execute(
      'SELECT * FROM platform_admins WHERE (username = ? OR email = ?) AND status = ?',
      [username, username, 'active']
    );
    
    console.log('查询结果:', users.length, '个用户');
    
    if (users.length === 0) {
      return res.redirect('/login?error=' + encodeURIComponent('用户名或密码错误'));
    }

    const user = users[0];
    
    // bcrypt密码验证
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.redirect('/login?error=' + encodeURIComponent('用户名或密码错误'));
    }

    // 更新最后登录时间
    console.log('[DB Query] UPDATE platform_admins SET lastLoginAt = NOW() WHERE id = ?', [user.id]);
    await db.execute('UPDATE platform_admins SET lastLoginAt = NOW() WHERE id = ?', [user.id]);

    // 设置session
    req.session.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      tenant_id: user.tenant_id
    };

    console.log('用户登录成功:', req.session.user);
    res.redirect('/dashboard');
    
  } catch (error) {
    console.error('登录错误:', error);
    res.redirect('/login?error=' + encodeURIComponent('登录失败，请重试'));
  }
});

// 兼容性路由 - 支持 /auth/login 路径 (AJAX调用)
router.post('/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('登录尝试 (auth路径):', { username, password: '***' });
    
    if (!username || !password) {
      return res.status(400).json({ success: false, message: '用户名和密码不能为空' });
    }

    // 查询用户（支持用户名或邮箱登录）
    const db = req.app.locals.db;
    console.log('[DB Query] SELECT * FROM platform_admins WHERE (username = ? OR email = ?) AND status = ?', [username, username, 'active']);
    
    const users = await db.execute(
      'SELECT * FROM platform_admins WHERE (username = ? OR email = ?) AND status = ?',
      [username, username, 'active']
    );
    
    console.log('查询结果:', users.length, '个用户');
    
    if (users.length === 0) {
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }

    const user = users[0];
    
    // bcrypt密码验证
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, message: '用户名或密码错误' });
    }

    // 更新最后登录时间
    console.log('[DB Query] UPDATE platform_admins SET lastLoginAt = NOW() WHERE id = ?', [user.id]);
    await db.execute('UPDATE platform_admins SET lastLoginAt = NOW() WHERE id = ?', [user.id]);

    // 设置session
    req.session.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      tenant_id: user.tenant_id
    };

    console.log('用户登录成功 (auth路径):', req.session.user);
    return res.json({ success: true, message: '登录成功', redirect: '/dashboard' });
    
  } catch (error) {
    console.error('登录错误 (auth路径):', error);
    return res.status(500).json({ success: false, message: '登录失败，请稍后重试' });
  }
});

// 登出
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('登出错误:', err);
    }
    res.redirect('/login');
  });
});

// ==================== 首页和仪表板 ====================

// 首页重定向
router.get('/', (req, res) => {
  if (req.session?.user) {
    res.redirect('/dashboard');
  } else {
    res.redirect('/login');
  }
});

// 仪表板 - 根据用户角色显示不同内容
router.get('/dashboard', requireAuth, async (req, res) => {
  try {
    console.log('加载仪表板...');
    const user = req.session.user;
    const db = req.app.locals.db;
    
    // 根据用户角色加载不同的仪表板数据
    let dashboardData = {
      user,
      isPlatformAdmin: ['super_admin', 'platform_admin'].includes(user.role),
      isTenantAdmin: ['super_admin', 'platform_admin', 'tenant_admin'].includes(user.role)
    };
    
    if (dashboardData.isPlatformAdmin) {
      // 平台管理员看到平台级数据
      try {
        const tenantResult = await tenantService.getTenants({ page: 1, limit: 10 });
        dashboardData.tenantStats = {
          total: tenantResult.pagination?.total || 0,
          active: tenantResult.data?.filter(t => t.status === 'active').length || 0,
          recent: tenantResult.data?.slice(0, 5) || []
        };
      } catch (error) {
        console.error('租户列表加载错误:', error);
        dashboardData.tenantStats = { total: 0, active: 0, recent: [] };
      }
      
      try {
        const [platformUserResult] = await db.execute('SELECT COUNT(*) as count FROM platform_admins WHERE status = ?', ['active']);
        dashboardData.platformUserCount = platformUserResult[0]?.count || 0;
      } catch (error) {
        console.error('平台用户列表加载错误:', error);
        dashboardData.platformUserCount = 0;
      }
      
    } else if (user.tenant_id) {
      // 租户管理员看到租户内数据
      dashboardData.tenantData = {
        tenant_id: user.tenant_id,
        // 这里可以加载租户特定的统计数据
      };
    }
    
    // 创建stats对象，用于模板渲染
    const stats = {
      // 基本统计
      totalTenants: dashboardData.tenantStats?.total || 0,
      activeTenants: dashboardData.tenantStats?.active || 0,
      totalUsers: dashboardData.platformUserCount || 0,
      monthlyRevenue: 0, // 需要从订单系统获取
      
      // 订阅计划统计
      basicPlan: 0,
      standardPlan: 0,
      premiumPlan: 0,
      enterprisePlan: 0,
      
      // 业务数据统计
      totalFlocks: 0,
      totalGeese: 0,
      todayRecords: 0,
      todayEggs: 0,
      activeProducts: 0,
      publishedPrices: 0,
      
      // 用户活跃度统计
      weeklyActiveUsers: 0,
      monthlyActiveUsers: 0,
      activeUsers: 0,
      publishedArticles: 0,
      helpArticles: 0,
      activeAnnouncements: 0
    };
    
    // 根据租户数据计算订阅计划统计
    if (dashboardData.tenantStats?.recent) {
      dashboardData.tenantStats.recent.forEach(tenant => {
        switch(tenant.subscription_plan) {
          case 'basic': stats.basicPlan++; break;
          case 'standard': stats.standardPlan++; break;
          case 'premium': stats.premiumPlan++; break;
          case 'enterprise': stats.enterprisePlan++; break;
        }
      });
    }
    
    // 系统健康状态
    const systemHealth = {
      database: { status: 'healthy', responseTime: 50 },
      expiringSubs: 0,
      pendingOrders: 0,
      apiEndpoints: { active: 25, total: 30 },
      systemLoad: { cpu: 25, memory: 45, disk: 60 },
      uptime: process.uptime(),
      nodeVersion: process.version
    };

    console.log('渲染仪表板页面...');
    res.render('dashboard/index', {
      title: '控制台 - 智慧养鹅SAAS管理平台',
      currentPage: 'dashboard',
      stats,
      systemHealth,
      recentTenants: dashboardData.tenantStats?.recent || [],
      recentOrders: [],
      ...dashboardData
    });
    
  } catch (error) {
    console.error('仪表板加载错误:', error);
    res.status(500).render('error', {
      title: '系统错误',
      message: '仪表板加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// ==================== 平台级管理功能路由 ====================

// 1. 租户管理
router.get('/tenants', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const plan = req.query.plan || '';

    const result = await tenantService.getTenants({
      page, limit, search, status, subscriptionPlan: plan
    });

    // 获取订阅计划列表
    const db = req.app.locals.db;
    const [plans] = await db.execute(
      'SELECT plan_code, display_name FROM subscription_plans WHERE is_active = true ORDER BY sort_order'
    );

    res.render('tenants/index', {
      title: '租户管理 - 智慧养鹅SAAS管理平台',
      currentPage: 'tenants',
      user: req.session.user,
      tenants: result.data,
      pagination: result.pagination,
      filters: { search, status, plan },
      plans
    });
  } catch (error) {
    console.error('租户列表加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '租户列表加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// 租户详情 - 进入租户级管理界面
router.get('/tenants/:id', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    const tenantId = parseInt(req.params.id);
    const tenant = await tenantService.getTenantById(tenantId);
    
    if (!tenant) {
      return res.status(404).render('error', {
        title: '租户不存在',
        message: '未找到指定的租户',
        layout: 'layouts/main'
      });
    }
    
    // 切换到租户上下文，设置临时租户ID
    req.session.viewingTenantId = tenantId;
    
    res.render('tenants/details', {
      title: `${tenant.name} - 租户详情`,
      currentPage: 'tenant-detail',
      user: req.session.user,
      tenant,
      viewingTenantId: tenantId
    });
  } catch (error) {
    console.error('租户详情加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '租户详情加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// 2. 今日鹅价管理 
router.get('/goose-prices', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('goose-prices/index', {
    title: '今日鹅价管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'goose-prices',
    user: req.session.user
  });
});

// 3. 平台公告管理
router.get('/announcements', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('announcements/index', {
    title: '平台公告管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'announcements',
    user: req.session.user
  });
});

// 4. 知识库管理
router.get('/knowledge', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('knowledge/index', {
    title: '知识库管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'knowledge',
    user: req.session.user
  });
});

// 5. 商城模块管理
router.get('/mall', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('mall/index', {
    title: '商城模块管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'mall',
    user: req.session.user
  });
});

// 6. AI大模型配置
router.get('/ai-config', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('ai-config/index', {
    title: 'AI大模型配置 - 智慧养鹅SAAS管理平台',
    currentPage: 'ai-config',
    user: req.session.user
  });
});

// 7. 系统设置
router.get('/settings', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('settings/index', {
    title: '系统设置 - 智慧养鹅SAAS管理平台',
    currentPage: 'settings',
    user: req.session.user
  });
});

// 8. 平台用户管理
router.get('/platform-users', requireAuth, requirePlatformAdmin, async (req, res) => {
  try {
    const db = req.app.locals.db;
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    const [countResult] = await db.execute('SELECT COUNT(*) as total FROM platform_admins');
    const total = countResult[0].total;

    const [users] = await db.execute(`
      SELECT id, username, email, name, role, status, last_login, created_at
      FROM platform_admins 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `, [limit, offset]);

    res.render('platform-users/index', {
      title: '平台用户管理 - 智慧养鹅SAAS管理平台',
      currentPage: 'platform-users',
      user: req.session.user,
      users: users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('平台用户列表加载错误:', error);
    res.status(500).render('error', {
      title: '加载失败',
      message: '平台用户列表加载失败，请重试',
      layout: 'layouts/main'
    });
  }
});

// ==================== 租户级管理功能路由 ====================

// 检查租户上下文的中间件
const ensureTenantContext = (req, res, next) => {
  const user = req.session.user;
  let tenantId = null;
  
  // 平台管理员可能在查看特定租户
  if (['super_admin', 'platform_admin'].includes(user.role)) {
    tenantId = req.session.viewingTenantId || null;
  } else if (user.tenant_id) {
    // 租户用户只能访问自己的租户数据
    tenantId = user.tenant_id;
  }
  
  if (!tenantId) {
    return res.status(403).render('error', {
      title: '权限不足',
      message: '请先选择要管理的租户',
      layout: 'layouts/main'
    });
  }
  
  req.tenantId = tenantId;
  next();
};

// 1. 鹅群管理（租户级）
router.get('/tenant/flocks', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('flocks/index', {
    title: '鹅群管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-flocks',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// 2. 生产物料管理（租户级）
router.get('/tenant/inventory', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('inventory/index', {
    title: '生产物料管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-inventory',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// 3. 健康记录管理（租户级）
router.get('/tenant/health', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('health/index', {
    title: '健康记录管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-health',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// 4. 财务管理（租户级）
router.get('/tenant/finance', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('finance/index', {
    title: '财务管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-finance',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// 5. 生产记录管理（租户级）
router.get('/tenant/production', requireAuth, requireTenantAdmin, ensureTenantContext, (req, res) => {
  res.render('production/index', {
    title: '生产记录管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'tenant-production',
    user: req.session.user,
    tenantId: req.tenantId,
    isViewingTenant: !!req.session.viewingTenantId
  });
});

// ==================== 其他功能路由 ====================

// 监控页面
router.get('/monitoring', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('monitoring/index', {
    title: '性能监控 - 智慧养鹅SAAS管理平台',
    currentPage: 'monitoring',
    user: req.session.user
  });
});

// 系统日志
router.get('/logs', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('system/logs', {
    title: '系统日志 - 智慧养鹅SAAS管理平台',
    currentPage: 'logs',
    user: req.session.user
  });
});

// 报表管理
router.get('/reports', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('reports/index', {
    title: '报表管理 - 智慧养鹅SAAS管理平台',
    currentPage: 'reports',
    user: req.session.user
  });
});

// ==================== API端点路由 ====================

// API文档
router.get('/api-docs', requireAuth, requirePlatformAdmin, (req, res) => {
  res.render('api-endpoints/index', {
    title: 'API文档 - 智慧养鹅SAAS管理平台',
    currentPage: 'api-docs',
    user: req.session.user
  });
});

// ==================== 错误处理 ====================

// 404处理
router.use('*', (req, res) => {
  res.status(404).render('error', {
    title: '页面未找到',
    message: '您访问的页面不存在',
    layout: 'layouts/main'
  });
});

module.exports = router;