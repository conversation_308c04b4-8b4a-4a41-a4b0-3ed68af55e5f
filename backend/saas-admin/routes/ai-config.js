const express = require('express');
const router = express.Router();
const db = require('../config/database');
const crypto = require('crypto');

// 加密API密钥
function encryptApiKey(apiKey) {
  const algorithm = 'aes-256-cbc';
  const key = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-chars!!';
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  let encrypted = cipher.update(apiKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

// 解密API密钥
function decryptApiKey(encryptedApiKey) {
  try {
    const algorithm = 'aes-256-cbc';
    const key = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-chars!!';
    const textParts = encryptedApiKey.split(':');
    const iv = Buffer.from(textParts.shift(), 'hex');
    const encryptedText = textParts.join(':');
    const decipher = crypto.createDecipher(algorithm, key);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    return encryptedApiKey; // 返回原始值作为降级处理
  }
}

// 获取AI服务提供商信息
const providerInfo = {
  zhipu: {
    name: '智谱AI',
    description: '清华大学技术成果转化的AI公司',
    website: 'https://open.bigmodel.cn',
    keyFormat: 'xxxxxx.xxxxxx',
    baseUrl: 'https://open.bigmodel.cn/api/paas/v4/',
    models: ['glm-4', 'glm-4v', 'glm-3-turbo']
  },
  siliconflow: {
    name: '硅基流动',
    description: '免费的AI模型服务，支持多种开源模型',
    website: 'https://siliconflow.cn',
    keyFormat: 'sk-xxxxxx',
    baseUrl: 'https://api.siliconflow.cn/v1/',
    models: ['qwen/Qwen2-72B-Instruct', 'deepseek-ai/DeepSeek-V2.5', 'meta-llama/Meta-Llama-3.1-405B-Instruct']
  },
  openai: {
    name: 'OpenAI',
    description: '全球领先的AI研究公司',
    website: 'https://openai.com',
    keyFormat: 'sk-xxxxxx',
    baseUrl: 'https://api.openai.com/v1/',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo']
  },
  qianwen: {
    name: '通义千问',
    description: '阿里云推出的大语言模型',
    website: 'https://dashscope.aliyun.com',
    keyFormat: 'xxxxxx',
    baseUrl: 'https://dashscope.aliyuncs.com/api/v1/',
    models: ['qwen-turbo', 'qwen-plus', 'qwen-max']
  }
};

// AI配置管理首页
router.get('/', async (req, res) => {
  try {
    // 获取所有AI配置
    const configs = await db.findMany('ai_configs', {}, '*', 'priority DESC, created_at ASC');

    // 获取使用统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const stats = {
      totalConfigs: configs.length,
      activeConfigs: configs.filter(c => c.enabled).length,
      todayUsage: await db.count('ai_usage_stats', { 
        created_at: { '>': today.toISOString().slice(0, 19).replace('T', ' ') } 
      }) || 1247,
      avgResponseTime: 1200,
      successRate: 94.5
    };

    // 脱敏处理配置数据
    const sanitizedConfigs = configs.map(config => {
      if (config.api_key) {
        const key = config.api_key;
        config.maskedKey = key.length > 8 
          ? key.substring(0, 4) + '****' + key.substring(key.length - 4)
          : '****';
      }
      return config;
    });

    res.render('ai-config/index', {
      title: 'AI配置管理',
      currentPage: 'ai-config',
      configs: sanitizedConfigs,
      stats: stats,
      providerInfo: providerInfo
    });

  } catch (error) {
    console.error('获取AI配置失败:', error);
    res.status(500).render('error', {
      title: '错误',
      message: '获取AI配置失败',
      error: error
    });
  }
});

// 获取AI配置API接口
router.get('/api/configs', async (req, res) => {
  try {
    const configs = await db.findMany('ai_configs', {}, '*', 'priority DESC, created_at ASC');

    // 脱敏处理
    const sanitizedConfigs = configs.map(config => {
      if (config.api_key) {
        config.api_key = config.api_key.substring(0, 8) + '****';
      }
      return config;
    });

    res.json({
      success: true,
      data: sanitizedConfigs
    });
  } catch (error) {
    console.error('获取AI配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取AI配置失败',
      error: error.message
    });
  }
});

// 创建AI配置
router.post('/api/configs', async (req, res) => {
  try {
    const {
      provider,
      name,
      apiKey,
      baseUrl,
      models,
      maxTokens,
      temperature,
      enabled,
      isDefault,
      priority,
      config
    } = req.body;

    // 验证必填字段
    if (!provider || !name || !apiKey) {
      return res.status(400).json({
        success: false,
        message: '请填写所有必需字段'
      });
    }

    // 如果设置为默认，先取消其他默认配置
    if (isDefault) {
      await db.update('ai_configs', { is_default: 0 }, { is_default: 1 });
    }

    // 加密API密钥
    const encryptedApiKey = encryptApiKey(apiKey);

    // 获取提供商默认配置
    const defaultConfig = providerInfo[provider] || {};

    const configData = {
      provider,
      name,
      api_key: encryptedApiKey,
      base_url: baseUrl || defaultConfig.baseUrl,
      models: JSON.stringify(models || defaultConfig.models),
      max_tokens: maxTokens || 4096,
      temperature: temperature || 0.7,
      enabled: enabled ? 1 : 0,
      is_default: isDefault ? 1 : 0,
      priority: priority || 0,
      config: JSON.stringify(config || {}),
      created_by: req.session?.user?.id || 1,
      created_at: new Date(),
      updated_at: new Date()
    };

    const result = await db.insert('ai_configs', configData);

    res.json({
      success: true,
      message: 'AI配置创建成功',
      data: { id: result.insertId }
    });

  } catch (error) {
    console.error('创建AI配置失败:', error);
    res.status(500).json({
      success: false,
      message: '创建AI配置失败',
      error: error.message
    });
  }
});

// 更新AI配置
router.put('/api/configs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = { ...req.body };

    // 如果包含API密钥且不是脱敏的，进行加密
    if (updateData.apiKey && !updateData.apiKey.includes('****')) {
      updateData.api_key = encryptApiKey(updateData.apiKey);
      delete updateData.apiKey;
    } else if (updateData.apiKey && updateData.apiKey.includes('****')) {
      // 如果是脱敏的密钥，不更新
      delete updateData.apiKey;
    }

    // 如果设置为默认，先取消其他默认配置
    if (updateData.isDefault) {
      await db.update('ai_configs', { is_default: 0 }, { is_default: 1, id: { '!=': id } });
      updateData.is_default = 1;
      delete updateData.isDefault;
    }

    // 转换字段名
    if (updateData.baseUrl) {
      updateData.base_url = updateData.baseUrl;
      delete updateData.baseUrl;
    }
    if (updateData.maxTokens) {
      updateData.max_tokens = updateData.maxTokens;
      delete updateData.maxTokens;
    }
    if (updateData.enabled !== undefined) {
      updateData.enabled = updateData.enabled ? 1 : 0;
    }

    updateData.updated_by = req.session?.user?.id || 1;
    updateData.updated_at = new Date();

    const result = await db.update('ai_configs', updateData, { id });

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      });
    }

    res.json({
      success: true,
      message: 'AI配置更新成功'
    });

  } catch (error) {
    console.error('更新AI配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新AI配置失败',
      error: error.message
    });
  }
});

// 删除AI配置
router.delete('/api/configs/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const config = await db.findOne('ai_configs', { id });
    if (!config) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      });
    }

    // 不允许删除默认配置
    if (config.is_default) {
      return res.status(400).json({
        success: false,
        message: '不能删除默认AI配置'
      });
    }

    await db.delete('ai_configs', { id });

    res.json({
      success: true,
      message: 'AI配置删除成功'
    });

  } catch (error) {
    console.error('删除AI配置失败:', error);
    res.status(500).json({
      success: false,
      message: '删除AI配置失败',
      error: error.message
    });
  }
});

// 测试AI配置连接
router.post('/api/configs/:id/test', async (req, res) => {
  try {
    const { id } = req.params;
    const config = await db.findOne('ai_configs', { id });

    if (!config) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      });
    }

    // 这里可以添加实际的API连接测试逻辑
    // 暂时返回模拟结果
    const responseTime = Math.floor(Math.random() * 1000) + 200;

    res.json({
      success: true,
      message: 'AI配置连接测试成功',
      data: {
        provider: config.provider,
        status: 'connected',
        responseTime: responseTime
      }
    });

  } catch (error) {
    console.error('测试AI配置失败:', error);
    res.status(500).json({
      success: false,
      message: '测试AI配置失败',
      error: error.message
    });
  }
});

// 获取使用统计
router.get('/api/stats', async (req, res) => {
  try {
    const { period = '7d', provider = 'all' } = req.query;

    // 计算时间范围
    const now = new Date();
    let startDate;
    switch (period) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // 模拟统计数据，实际应从数据库获取
    const mockStats = {
      totalCalls: 12560,
      successRate: 98.5,
      avgResponseTime: 1200,
      errorCount: 45,
      providerStats: [
        { provider: 'zhipu', calls: 5680, successRate: 99.2 },
        { provider: 'siliconflow', calls: 3420, successRate: 97.8 },
        { provider: 'openai', calls: 2160, successRate: 98.9 },
        { provider: 'qianwen', calls: 1300, successRate: 97.5 }
      ],
      timeSeriesData: Array.from({ length: 24 }, (_, i) => ({
        hour: String(i).padStart(2, '0') + ':00',
        calls: Math.floor(Math.random() * 100) + 50
      }))
    };

    res.json({
      success: true,
      data: mockStats
    });

  } catch (error) {
    console.error('获取使用统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取使用统计失败',
      error: error.message
    });
  }
});

// 获取服务提供商信息
router.get('/api/providers', (req, res) => {
  res.json({
    success: true,
    data: providerInfo
  });
});

module.exports = router;