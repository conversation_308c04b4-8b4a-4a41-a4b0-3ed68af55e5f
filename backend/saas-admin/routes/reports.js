const express = require('express');
const router = express.Router();
const db = require('../config/database');

// 统计报告主页
router.get('/', async (req, res) => {
    try {
        // 获取总体统计数据
        const overallStats = {
            totalTenants: 156,
            activeTenants: 142,
            totalUsers: 1250,
            activeUsers: 1180,
            totalRevenue: 125680.50,
            monthlyRevenue: 15680.00,
            totalApiCalls: 2580000,
            monthlyApiCalls: 350000
        };

        // 获取趋势数据
        const trendData = {
            tenantGrowth: [120, 125, 132, 138, 145, 150, 156],
            userGrowth: [980, 1020, 1080, 1150, 1200, 1230, 1250],
            revenueGrowth: [12000, 13500, 14200, 15100, 14800, 15200, 15680]
        };

        res.render('reports/index', {
            title: '统计报告',
            overallStats: overallStats,
            trendData: trendData
        });

    } catch (error) {
        console.error('获取统计报告失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取统计报告失败',
            error: error
        });
    }
});

// 租户统计报告
router.get('/tenants', async (req, res) => {
    try {
        const tenantStats = {
            byPlan: {
                trial: 45,
                basic: 32,
                standard: 48,
                premium: 25,
                enterprise: 6
            },
            byStatus: {
                active: 142,
                inactive: 8,
                suspended: 4,
                trial: 2
            },
            byRegion: {
                '华东': 58,
                '华南': 42,
                '华北': 35,
                '西南': 21
            },
            growth: {
                thisMonth: 12,
                lastMonth: 8,
                growthRate: 50
            }
        };

        res.render('reports/tenants', {
            title: '租户统计报告',
            tenantStats: tenantStats
        });

    } catch (error) {
        console.error('获取租户统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取租户统计失败',
            error: error
        });
    }
});

// 用户统计报告
router.get('/users', async (req, res) => {
    try {
        const userStats = {
            byRole: {
                admin: 25,
                manager: 180,
                user: 1045
            },
            byStatus: {
                active: 1180,
                inactive: 70
            },
            activity: {
                dailyActive: 850,
                weeklyActive: 1050,
                monthlyActive: 1180
            },
            registration: {
                thisMonth: 85,
                lastMonth: 72,
                growthRate: 18
            }
        };

        res.render('reports/users', {
            title: '用户统计报告',
            userStats: userStats
        });

    } catch (error) {
        console.error('获取用户统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取用户统计失败',
            error: error
        });
    }
});

// 财务统计报告
router.get('/finance', async (req, res) => {
    try {
        const financeStats = {
            revenue: {
                total: 125680.50,
                thisMonth: 15680.00,
                lastMonth: 14200.00,
                growthRate: 10.4
            },
            byPlan: {
                trial: 0,
                basic: 25600.00,
                standard: 57600.00,
                premium: 35200.00,
                enterprise: 7280.50
            },
            subscription: {
                newSubscriptions: 12,
                renewals: 45,
                cancellations: 3,
                churnRate: 2.1
            },
            forecast: {
                nextMonth: 16800.00,
                nextQuarter: 48500.00,
                confidence: 85
            }
        };

        res.render('reports/finance', {
            title: '财务统计报告',
            financeStats: financeStats
        });

    } catch (error) {
        console.error('获取财务统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取财务统计失败',
            error: error
        });
    }
});

// 平台统计报告
router.get('/platform', async (req, res) => {
    try {
        const platformStats = {
            totalTenants: 156,
            activeTenants: 142,
            totalUsers: 1250,
            activeUsers: 1180,
            totalApiCalls: 2580000,
            monthlyApiCalls: 350000,
            systemUptime: 99.95,
            avgResponseTime: 120,
            tenantGrowth: {
                thisMonth: 12,
                lastMonth: 8,
                growthRate: 50
            },
            userGrowth: {
                thisMonth: 85,
                lastMonth: 72,
                growthRate: 18
            },
            topTenants: [
                { name: '绿源农业', users: 45, apiCalls: 125000, plan: 'enterprise' },
                { name: '丰收养殖', users: 38, apiCalls: 98000, plan: 'premium' },
                { name: '康健农场', users: 32, apiCalls: 87000, plan: 'standard' },
                { name: '生态农业', users: 28, apiCalls: 76000, plan: 'standard' }
            ]
        };

        res.render('reports/platform', {
            title: '平台统计',
            platformStats: platformStats
        });

    } catch (error) {
        console.error('获取平台统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取平台统计失败',
            error: error
        });
    }
});

// 收入统计报告  
router.get('/revenue', async (req, res) => {
    try {
        const revenueStats = {
            totalRevenue: 125680.50,
            monthlyRevenue: 15680.00,
            yearlyRevenue: 98450.00,
            growth: {
                monthly: 10.4,
                yearly: 25.8
            },
            byPlan: {
                trial: 0,
                basic: 25600.00,
                standard: 57600.00,
                premium: 35200.00,
                enterprise: 7280.50
            },
            byMonth: [
                { month: '2025-01', revenue: 12000 },
                { month: '2025-02', revenue: 13500 },
                { month: '2025-03', revenue: 14200 },
                { month: '2025-04', revenue: 15100 },
                { month: '2025-05', revenue: 14800 },
                { month: '2025-06', revenue: 15200 },
                { month: '2025-07', revenue: 15680 }
            ],
            subscriptions: {
                new: 12,
                renewals: 45,
                cancellations: 3,
                churnRate: 2.1
            },
            forecast: {
                nextMonth: 16800.00,
                nextQuarter: 48500.00,
                confidence: 85
            }
        };

        res.render('reports/revenue', {
            title: '收入统计',
            revenueStats: revenueStats
        });

    } catch (error) {
        console.error('获取收入统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取收入统计失败',
            error: error
        });
    }
});

// 使用统计报告
router.get('/usage', async (req, res) => {
    try {
        const usageStats = {
            apiUsage: {
                totalCalls: 2580000,
                monthlyLimit: 5000000,
                usageRate: 51.6,
                avgCallsPerDay: 11586
            },
            storageUsage: {
                totalUsed: 8.5, // GB
                totalLimit: 100, // GB
                usageRate: 8.5,
                avgGrowthPerMonth: 0.8
            },
            userActivity: {
                dailyActiveUsers: 850,
                weeklyActiveUsers: 1050,
                monthlyActiveUsers: 1180,
                avgSessionDuration: 45 // minutes
            },
            featureUsage: [
                { feature: '鹅群管理', usage: 95.2 },
                { feature: '生产记录', usage: 87.8 },
                { feature: '健康监控', usage: 78.5 },
                { feature: '财务管理', usage: 65.3 },
                { feature: '库存管理', usage: 58.7 },
                { feature: 'AI分析', usage: 42.1 }
            ],
            peakUsageHours: [
                { hour: '09:00-10:00', calls: 15000 },
                { hour: '14:00-15:00', calls: 18000 },
                { hour: '20:00-21:00', calls: 12000 }
            ]
        };

        res.render('reports/usage', {
            title: '使用统计',
            usageStats: usageStats
        });

    } catch (error) {
        console.error('获取使用统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取使用统计失败',
            error: error
        });
    }
});

// API使用统计报告
router.get('/api-usage', async (req, res) => {
    try {
        const apiStats = {
            totalCalls: 2580000,
            monthlyLimit: 5000000,
            usageRate: 51.6,
            topEndpoints: [
                { endpoint: '/api/v1/auth/login', calls: 458000, percentage: 17.8 },
                { endpoint: '/api/v1/flocks', calls: 325000, percentage: 12.6 },
                { endpoint: '/api/v1/prices', calls: 280000, percentage: 10.9 }
            ],
            errorRate: 0.02,
            avgResponseTime: 120,
            peakHours: ['09:00-10:00', '14:00-15:00', '20:00-21:00']
        };

        res.render('reports/api-usage', {
            title: 'API使用统计报告',
            apiStats: apiStats
        });

    } catch (error) {
        console.error('获取API使用统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取API使用统计失败',
            error: error
        });
    }
});

// 系统性能报告
router.get('/performance', async (req, res) => {
    try {
        const performanceStats = {
            uptime: 99.95,
            avgResponseTime: 120,
            peakResponseTime: 450,
            errorRate: 0.02,
            throughput: 1250,
            memoryUsage: 68.5,
            cpuUsage: 45.2,
            diskUsage: 32.8,
            networkIO: {
                inbound: 125.6,
                outbound: 89.3
            }
        };

        res.render('reports/performance', {
            title: '系统性能报告',
            performanceStats: performanceStats
        });

    } catch (error) {
        console.error('获取系统性能统计失败:', error);
        res.status(500).render('error', {
            title: '错误',
            message: '获取系统性能统计失败',
            error: error
        });
    }
});

module.exports = router;
