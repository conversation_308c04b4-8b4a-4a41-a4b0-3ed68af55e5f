const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkDatabaseStructure() {
    let connection;
    
    try {
        // 连接到数据库
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'smart_goose_saas_platform',
            charset: 'utf8mb4'
        });
        
        console.log('✅ 连接到数据库成功');
        
        // 检查租户用户关联表结构
        console.log('\n📋 检查tenant_users表结构:');
        const [tenantUserColumns] = await connection.query('DESCRIBE tenant_users');
        console.table(tenantUserColumns);

        // 检查是否有数据
        console.log('\n📊 检查tenant_users表数据:');
        const [tenantUserData] = await connection.query('SELECT * FROM tenant_users LIMIT 5');
        if (tenantUserData.length > 0) {
            console.table(tenantUserData);
        } else {
            console.log('tenant_users表为空');
        }
        
        // 检查其他表是否存在
        console.log('\n📋 检查所有表:');
        const [tables] = await connection.query('SHOW TABLES');
        console.log('现有表:', tables.map(t => Object.values(t)[0]));
        
    } catch (error) {
        console.error('❌ 检查数据库结构失败:', error.message);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

checkDatabaseStructure();
