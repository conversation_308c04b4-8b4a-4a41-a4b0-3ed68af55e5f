const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    charset: 'utf8mb4'
};

const targetDatabase = process.env.DB_NAME || 'smart_goose_saas_platform';

async function initDatabase() {
    let connection;
    
    try {
        console.log('🔧 开始初始化数据库...');
        
        // 连接到MySQL服务器（不指定数据库）
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 连接到MySQL服务器成功');
        
        // 创建数据库（如果不存在）
        await connection.query(`CREATE DATABASE IF NOT EXISTS \`${targetDatabase}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
        console.log(`✅ 数据库 '${targetDatabase}' 已创建或已存在`);

        // 关闭当前连接，重新连接到目标数据库
        await connection.end();

        // 重新连接到目标数据库
        connection = await mysql.createConnection({
            ...dbConfig,
            database: targetDatabase
        });
        console.log(`✅ 已连接到数据库 '${targetDatabase}'`);

        // 创建基础表结构
        await createBasicTables(connection);
        
        // 插入一些测试数据
        await insertTestData(connection);
        
        console.log('🎉 数据库初始化完成！');
        
    } catch (error) {
        console.error('❌ 数据库初始化失败:', error.message);
        throw error;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

async function createBasicTables(connection) {
    console.log('📋 创建基础表结构...');
    
    // 租户表已存在，跳过创建
    console.log('✅ 租户表已存在，跳过创建');

    // 用户表已存在，跳过创建
    console.log('✅ 用户表已存在，跳过创建');
    
    // 创建鹅群表
    await connection.query(`
        CREATE TABLE IF NOT EXISTS flocks (
            id INT PRIMARY KEY AUTO_INCREMENT,
            tenant_id INT,
            name VARCHAR(255) NOT NULL,
            currentCount INT DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (tenant_id) REFERENCES tenants(id)
        )
    `);
    console.log('✅ 鹅群表已创建');

    // 创建商城产品表
    await connection.query(`
        CREATE TABLE IF NOT EXISTS mall_products (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            price DECIMAL(10,2) DEFAULT 0,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    `);
    console.log('✅ 商城产品表已创建');

    // 创建商城订单表
    await connection.query(`
        CREATE TABLE IF NOT EXISTS mall_orders (
            id INT PRIMARY KEY AUTO_INCREMENT,
            tenant_id INT,
            user_id INT,
            total_amount DECIMAL(10,2) DEFAULT 0,
            order_status ENUM('pending', 'paid', 'shipped', 'completed', 'cancelled') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (tenant_id) REFERENCES tenants(id),
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
    `);
    console.log('✅ 商城订单表已创建');
    
    // 创建鹅价表
    await connection.query(`
        CREATE TABLE IF NOT EXISTS goose_prices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            date DATE NOT NULL,
            price DECIMAL(10,2) DEFAULT 0,
            is_published BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    `);
    console.log('✅ 鹅价表已创建');

    // 创建知识库文章表
    await connection.query(`
        CREATE TABLE IF NOT EXISTS knowledge_articles (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            status ENUM('draft', 'published') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    `);
    console.log('✅ 知识库文章表已创建');

    // 创建公告表
    await connection.query(`
        CREATE TABLE IF NOT EXISTS announcements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            status ENUM('draft', 'published') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    `);
    console.log('✅ 公告表已创建');
}

async function insertTestData(connection) {
    console.log('📝 插入测试数据...');
    
    // 插入测试租户（使用正确的字段名）
    await connection.query(`
        INSERT IGNORE INTO tenants (id, tenant_code, name, contact_name, contact_email, status, subscription_plan) VALUES
        (4, 'TEST001', '测试养鹅场A', '张三', '<EMAIL>', 'active', 'premium'),
        (5, 'TEST002', '测试养鹅场B', '李四', '<EMAIL>', 'active', 'standard'),
        (6, 'TEST003', '测试养鹅场C', '王五', '<EMAIL>', 'inactive', 'basic')
    `);

    // 插入测试租户用户（使用tenant_users表）
    await connection.query(`
        INSERT IGNORE INTO tenant_users (id, tenant_id, username, email, name, role, status, last_login) VALUES
        (10, 4, 'zhangsan', '<EMAIL>', '张三', 'admin', 'active', NOW()),
        (11, 4, 'employee1', '<EMAIL>', '员工一', 'user', 'active', DATE_SUB(NOW(), INTERVAL 2 DAY)),
        (12, 5, 'lisi', '<EMAIL>', '李四', 'admin', 'active', DATE_SUB(NOW(), INTERVAL 5 DAY)),
        (13, 6, 'wangwu', '<EMAIL>', '王五', 'admin', 'inactive', DATE_SUB(NOW(), INTERVAL 30 DAY))
    `);

    // 插入测试鹅群
    await connection.query(`
        INSERT IGNORE INTO flocks (id, tenant_id, name, currentCount, status) VALUES
        (10, 4, '测试鹅群A-1', 500, 'active'),
        (11, 4, '测试鹅群A-2', 300, 'active'),
        (12, 5, '测试鹅群B-1', 800, 'active'),
        (13, 6, '测试鹅群C-1', 200, 'inactive')
    `);
    
    // 插入测试商品
    await connection.query(`
        INSERT IGNORE INTO mall_products (id, name, price, status) VALUES
        (1, '新鲜鹅蛋', 2.50, 'active'),
        (2, '优质鹅肉', 35.00, 'active'),
        (3, '鹅绒制品', 120.00, 'active')
    `);

    // 插入测试订单
    await connection.query(`
        INSERT IGNORE INTO mall_orders (id, tenant_id, user_id, total_amount, order_status) VALUES
        (10, 4, 10, 250.00, 'completed'),
        (11, 4, 11, 180.50, 'pending'),
        (12, 5, 12, 420.00, 'paid')
    `);

    // 插入今日鹅价
    await connection.query(`
        INSERT IGNORE INTO goose_prices (id, date, price, is_published) VALUES
        (1, CURDATE(), 28.50, TRUE),
        (2, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 28.00, TRUE)
    `);

    // 插入知识库文章
    await connection.query(`
        INSERT IGNORE INTO knowledge_articles (id, title, content, status) VALUES
        (1, '鹅的饲养管理技术', '详细介绍鹅的饲养管理技术...', 'published'),
        (2, '鹅病防治指南', '常见鹅病的预防和治疗方法...', 'published')
    `);

    // 插入公告
    await connection.query(`
        INSERT IGNORE INTO announcements (id, title, content, status) VALUES
        (1, '系统维护通知', '系统将于本周末进行维护...', 'published'),
        (2, '新功能上线', '平台新增AI诊断功能...', 'published')
    `);
    
    console.log('✅ 测试数据插入完成');
}

// 运行初始化
if (require.main === module) {
    initDatabase().then(() => {
        console.log('🏁 数据库初始化完成，可以启动服务器了');
        process.exit(0);
    }).catch(error => {
        console.error('初始化过程中出现错误:', error);
        process.exit(1);
    });
}

module.exports = { initDatabase };
