<!-- 用户管理页面 -->
<div class="container-fluid">
  <!-- 页面标题 -->
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
          <i class="fas fa-users me-2"></i>
          用户管理
        </h2>
        <div>
          <button class="btn btn-info me-2" onclick="showUserStats()">
            <i class="fas fa-chart-bar me-1"></i>
            用户统计
          </button>
          <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-plus me-1"></i>
            添加用户
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户统计卡片 -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-primary shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                总用户数
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= users ? users.length : 0 %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-users fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-success shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                活跃用户
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= users ? users.filter(u=> u.status === 'active').length : 0 %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-user-check fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-warning shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                本月新增
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= users ? users.filter(u=> new Date(u.created_at) > new Date(Date.now() - 30*24*60*60*1000)).length :
                  0 %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-user-plus fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
      <div class="card border-left-info shadow h-100 py-2">
        <div class="card-body">
          <div class="row no-gutters align-items-center">
            <div class="col mr-2">
              <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                管理员数
              </div>
              <div class="h5 mb-0 font-weight-bold text-gray-800">
                <%= users ? users.filter(u=> u.role === 'admin').length : 0 %>
              </div>
            </div>
            <div class="col-auto">
              <i class="fas fa-user-shield fa-2x text-gray-300"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户图表 -->
  <div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">用户增长趋势</h6>
        </div>
        <div class="card-body">
          <div class="chart-area">
            <canvas id="userGrowthChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-4 col-lg-5">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
          <h6 class="m-0 font-weight-bold text-primary">用户角色分布</h6>
        </div>
        <div class="card-body">
          <div class="chart-pie pt-4 pb-2">
            <canvas id="userRoleChart"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Search and Filters -->
    <div class="col-12">
      <div class="card shadow mb-4">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">用户列表</h6>
        </div>
        <div class="card-body">
          <!-- Search Form -->
          <form class="row g-3 mb-4" method="GET">
            <div class="col-md-4">
              <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="form-control" name="search" placeholder="搜索用户名、姓名、邮箱、养殖场..."
                  value="<%= filters.search %>">
              </div>
            </div>
            <div class="col-md-2">
              <select class="form-select" name="role">
                <option value="">全部角色</option>
                <option value="admin" <%=filters.role==='admin' ? 'selected' : '' %>>管理员</option>
                <option value="manager" <%=filters.role==='manager' ? 'selected' : '' %>>管理者</option>
                <option value="user" <%=filters.role==='user' ? 'selected' : '' %>>用户</option>
              </select>
            </div>
            <div class="col-md-2">
              <select class="form-select" name="status">
                <option value="">全部状态</option>
                <option value="active" <%=filters.status==='active' ? 'selected' : '' %>>正常</option>
                <option value="inactive" <%=filters.status==='inactive' ? 'selected' : '' %>>停用</option>
                <option value="suspended" <%=filters.status==='suspended' ? 'selected' : '' %>>暂停</option>
              </select>
            </div>
            <div class="col-md-2">
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> 搜索
              </button>
            </div>
            <div class="col-md-1">
              <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus"></i> 添加用户
              </button>
            </div>
            <div class="col-md-1">
              <button type="button" class="btn btn-info btn-export" onclick="exportUsers()">
                <i class="fas fa-download"></i> 导出
              </button>
            </div>
          </form>

          <!-- Users Table -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>用户名</th>
                  <th>姓名</th>
                  <th>邮箱</th>
                  <th>养殖场</th>
                  <th>角色</th>
                  <th>状态</th>
                  <th>最后登录</th>
                  <th>注册时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% if (users && users.length> 0) { %>
                  <% users.forEach(user=> { %>
                    <tr>
                      <td>
                        <%= user.id %>
                      </td>
                      <td>
                        <strong>
                          <%= user.username %>
                        </strong>
                      </td>
                      <td>
                        <%= user.name || '-' %>
                      </td>
                      <td>
                        <%= user.email %>
                      </td>
                      <td>
                        <%= user.farmName || '-' %>
                      </td>
                      <td>
                        <span
                          class="badge bg-<%= user.role === 'admin' ? 'danger' : (user.role === 'manager' ? 'warning' : 'primary') %>">
                          <%= user.role==='admin' ? '管理员' : (user.role==='manager' ? '管理者' : '用户' ) %>
                        </span>
                      </td>
                      <td>
                        <span class="badge status-<%= user.status %>">
                          <%= user.status==='active' ? '正常' : (user.status==='inactive' ? '停用' : '暂停' ) %>
                        </span>
                      </td>
                      <td>
                        <%= user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('zh-CN') : '从未登录' %>
                      </td>
                      <td>
                        <%= new Date(user.createdAt).toLocaleDateString('zh-CN') %>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button class="btn btn-outline-primary btn-edit" onclick="editUser(<%= user.id %>)">
                            <i class="fas fa-edit"></i>
                          </button>
                          <% if (user.role !=='admin' ) { %>
                            <button class="btn btn-outline-danger btn-delete" onclick="deleteUser(<%= user.id %>)">
                              <i class="fas fa-trash"></i>
                            </button>
                            <% } %>
                        </div>
                      </td>
                    </tr>
                    <% }) %>
                      <% } else { %>
                        <tr>
                          <td colspan="10" class="text-center text-muted py-4">
                            <i class="fas fa-users fa-3x mb-3 d-block"></i>
                            暂无用户数据
                          </td>
                        </tr>
                        <% } %>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <% if (pagination && pagination.totalPages> 1) { %>
            <nav class="mt-4">
              <ul class="pagination justify-content-center">
                <% if (pagination.hasPrev) { %>
                  <li class="page-item">
                    <a class="page-link"
                      href="?page=<%= pagination.page - 1 %>&search=<%= filters.search %>&role=<%= filters.role %>&status=<%= filters.status %>">
                      <i class="fas fa-chevron-left"></i>
                    </a>
                  </li>
                  <% } %>

                    <% for (let i=Math.max(1, pagination.page - 2); i <=Math.min(pagination.totalPages, pagination.page
                      + 2); i++) { %>
                      <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                        <a class="page-link"
                          href="?page=<%= i %>&search=<%= filters.search %>&role=<%= filters.role %>&status=<%= filters.status %>">
                          <%= i %>
                        </a>
                      </li>
                      <% } %>

                        <% if (pagination.hasNext) { %>
                          <li class="page-item">
                            <a class="page-link"
                              href="?page=<%= pagination.page + 1 %>&search=<%= filters.search %>&role=<%= filters.role %>&status=<%= filters.status %>">
                              <i class="fas fa-chevron-right"></i>
                            </a>
                          </li>
                          <% } %>
              </ul>
            </nav>
            <% } %>
        </div>
      </div>
    </div>
  </div>

  <!-- Add User Modal -->
  <div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">添加用户</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <form id="addUserForm">
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">用户名 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" name="username" required>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                <input type="email" class="form-control" name="email" required>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">姓名</label>
                <input type="text" class="form-control" name="name">
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">密码 <span class="text-danger">*</span></label>
                <input type="password" class="form-control" name="password" required>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">角色</label>
                <select class="form-select" name="role">
                  <option value="user">用户</option>
                  <option value="manager">管理者</option>
                  <option value="admin">管理员</option>
                </select>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label">养殖场名称</label>
                <input type="text" class="form-control" name="farmName">
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary">保存</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script>
    // 初始化图表
    document.addEventListener('DOMContentLoaded', function () {
      initUserCharts();
    });

    // 初始化用户图表
    function initUserCharts() {
      // 用户增长趋势图
      const growthCtx = document.getElementById('userGrowthChart').getContext('2d');
      new Chart(growthCtx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          datasets: [{
            label: '新增用户',
            data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 40, 38, 45],
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.1,
            fill: true
          }, {
            label: '活跃用户',
            data: [8, 15, 12, 20, 18, 25, 23, 30, 28, 35, 33, 40],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          },
          plugins: {
            legend: {
              display: true,
              position: 'top'
            }
          }
        }
      });

      // 用户角色分布饼图
      const roleCtx = document.getElementById('userRoleChart').getContext('2d');
      new Chart(roleCtx, {
        type: 'doughnut',
        data: {
          labels: ['管理员', '管理者', '普通用户'],
          datasets: [{
            data: [
              <%= users ? users.filter(u => u.role === 'admin').length : 5 %>,
              <%= users ? users.filter(u => u.role === 'manager').length : 15 %>,
              <%= users ? users.filter(u => u.role === 'user').length : 80 %>
            ],
            backgroundColor: [
              '#e74c3c',
              '#f39c12',
              '#3498db'
            ],
            hoverBackgroundColor: [
              '#c0392b',
              '#e67e22',
              '#2980b9'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'bottom'
            }
          }
        }
      });
    }

    // 显示用户统计
    function showUserStats() {
      const totalUsers = <%= users ?users.length: 0 %>;
      const activeUsers = <%= users ?users.filter(u => u.status === 'active').length : 0 %>;
      const newUsers = <%= users ?users.filter(u => new Date(u.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length : 0 %>;
      const adminUsers = <%= users ?users.filter(u => u.role === 'admin').length : 0 %>;

      const statsHtml = `
        <div class="alert alert-info">
          <h5><i class="fas fa-chart-bar me-2"></i>用户统计报告</h5>
          <div class="row">
            <div class="col-md-6">
              <strong>总用户数:</strong> ${totalUsers} 人
            </div>
            <div class="col-md-6">
              <strong>活跃用户:</strong> ${activeUsers} 人
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-6">
              <strong>本月新增:</strong> ${newUsers} 人
            </div>
            <div class="col-md-6">
              <strong>管理员数:</strong> ${adminUsers} 人
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-6">
              <strong>活跃率:</strong> ${totalUsers > 0 ? ((activeUsers / totalUsers) * 100).toFixed(1) : 0}%
            </div>
            <div class="col-md-6">
              <strong>增长率:</strong> ${totalUsers > 0 ? ((newUsers / totalUsers) * 100).toFixed(1) : 0}%
            </div>
          </div>
        </div>
      `;

      // 创建模态框显示统计结果
      const modal = document.createElement('div');
      modal.className = 'modal fade';
      modal.innerHTML = `
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">用户统计报告</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              ${statsHtml}
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
              <button type="button" class="btn btn-primary" onclick="exportUserStats()">导出报告</button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      const bootstrapModal = new bootstrap.Modal(modal);
      bootstrapModal.show();

      // 模态框关闭后移除DOM元素
      modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
      });
    }

    // 导出用户统计报告
    function exportUserStats() {
      const reportContent = `
用户统计报告
生成日期: ${new Date().toLocaleDateString()}

=== 用户概况 ===
总用户数: <%= users ? users.length : 0 %> 人
活跃用户: <%= users ? users.filter(u => u.status === 'active').length : 0 %> 人
本月新增: <%= users ? users.filter(u => new Date(u.created_at) > new Date(Date.now() - 30*24*60*60*1000)).length : 0 %> 人
管理员数: <%= users ? users.filter(u => u.role === 'admin').length : 0 %> 人

=== 数据来源 ===
智慧养鹅管理系统 - 用户管理模块
      `;

      const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `用户统计报告_${new Date().toISOString().split('T')[0]}.txt`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    // Add user form submission
    document.getElementById('addUserForm').addEventListener('submit', async function (e) {
      e.preventDefault();

      const formData = Utils.getFormData(this);

      try {
        Utils.showLoading('正在创建用户...');
        const response = await axios.post('/api/users/create', formData);

        if (response.data.success) {
          Utils.showToast('用户创建成功', 'success');
          bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
          setTimeout(() => window.location.reload(), 1000);
        }
      } catch (error) {
        console.error('Create user error:', error);
      } finally {
        Utils.hideLoading();
      }
    });

    // Edit user function
    async function editUser(userId) {
      Utils.showToast('编辑功能开发中', 'info');
    }

    // Delete user function
    async function deleteUser(userId) {
      Utils.confirm('确定要删除这个用户吗？此操作不可恢复。', async () => {
        try {
          Utils.showLoading('正在删除用户...');
          const response = await axios.delete(`/api/users/${userId}`);

          if (response.data.success) {
            Utils.showToast('用户删除成功', 'success');
            setTimeout(() => window.location.reload(), 1000);
          }
        } catch (error) {
          console.error('Delete user error:', error);
        } finally {
          Utils.hideLoading();
        }
      });
    }

    // Export users function
    async function exportUsers() {
      try {
        Utils.showLoading('正在导出用户数据...');

        // 构建导出URL，包含当前的筛选条件
        const params = new URLSearchParams(window.location.search);
        const exportUrl = `/api/users/export?${params.toString()}`;

        // 创建下载链接
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `用户数据_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Utils.showToast('用户数据导出成功', 'success');
      } catch (error) {
        console.error('Export users error:', error);
        Utils.showToast('导出失败，请稍后重试', 'error');
      } finally {
        Utils.hideLoading();
      }
    }
  </script>