<%- contentFor('title') %>
销售分析

<%- contentFor('head') %>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<%- contentFor('content') %>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-graph-up"></i> 销售分析
        </h1>
        <div class="d-flex gap-2">
            <select class="form-select" id="timeRangeFilter" style="width: auto;">
                <option value="7">最近7天</option>
                <option value="30" selected>最近30天</option>
                <option value="90">最近90天</option>
                <option value="365">最近一年</option>
            </select>
            <button type="button" class="btn btn-info" onclick="exportAnalytics()">
                <i class="bi bi-download"></i> 导出报告
            </button>
            <button type="button" class="btn btn-primary" onclick="refreshData()">
                <i class="bi bi-arrow-clockwise"></i> 刷新数据
            </button>
        </div>
    </div>

    <!-- 销售统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">总销售额</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalRevenue">¥0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-dollar text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">订单数量</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalOrders">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-cart text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">客单价</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="avgOrderValue">¥0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-person-check text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">转化率</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="conversionRate">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-graph-up-arrow text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">销售趋势</h6>
                </div>
                <div class="card-body">
                    <canvas id="salesTrendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">商品分类销售占比</h6>
                </div>
                <div class="card-body">
                    <canvas id="categoryPieChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 热销商品排行 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">热销商品TOP10</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>商品名称</th>
                                    <th>销量</th>
                                    <th>销售额</th>
                                </tr>
                            </thead>
                            <tbody id="topProductsTable">
                                <!-- 热销商品数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">地区销售分布</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>地区</th>
                                    <th>订单数</th>
                                    <th>销售额</th>
                                    <th>占比</th>
                                </tr>
                            </thead>
                            <tbody id="regionSalesTable">
                                <!-- 地区销售数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户行为分析 -->
    <div class="row">
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">用户访问统计</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>页面浏览量</span>
                            <strong id="pageViews">0</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>独立访客</span>
                            <strong id="uniqueVisitors">0</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>平均停留时间</span>
                            <strong id="avgSessionTime">0分钟</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>跳出率</span>
                            <strong id="bounceRate">0%</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">订单状态分布</h6>
                </div>
                <div class="card-body">
                    <canvas id="orderStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadAnalyticsData();
    initializeCharts();
    
    $('#timeRangeFilter').change(function() {
        const days = $(this).val();
        loadAnalyticsData(days);
        updateCharts(days);
    });
});

// 加载分析数据
function loadAnalyticsData(days = 30) {
    // 模拟销售统计数据
    const stats = {
        totalRevenue: 156789.50,
        totalOrders: 1247,
        avgOrderValue: 125.67,
        conversionRate: 3.2,
        pageViews: 45678,
        uniqueVisitors: 12345,
        avgSessionTime: 8.5,
        bounceRate: 35.2
    };
    
    $('#totalRevenue').text(`¥${stats.totalRevenue.toLocaleString()}`);
    $('#totalOrders').text(stats.totalOrders.toLocaleString());
    $('#avgOrderValue').text(`¥${stats.avgOrderValue}`);
    $('#conversionRate').text(`${stats.conversionRate}%`);
    $('#pageViews').text(stats.pageViews.toLocaleString());
    $('#uniqueVisitors').text(stats.uniqueVisitors.toLocaleString());
    $('#avgSessionTime').text(`${stats.avgSessionTime}分钟`);
    $('#bounceRate').text(`${stats.bounceRate}%`);
    
    loadTopProducts();
    loadRegionSales();
}

// 加载热销商品
function loadTopProducts() {
    const products = [
        { rank: 1, name: '优质鹅蛋 12枚装', sales: 456, revenue: 22800 },
        { rank: 2, name: '农家散养鹅肉 2kg', sales: 234, revenue: 46800 },
        { rank: 3, name: '鹅毛枕头', sales: 189, revenue: 18900 },
        { rank: 4, name: '鹅绒被', sales: 156, revenue: 31200 },
        { rank: 5, name: '鹅肝酱', sales: 123, revenue: 12300 }
    ];
    
    const tbody = $('#topProductsTable');
    tbody.empty();
    
    products.forEach(product => {
        tbody.append(`
            <tr>
                <td><span class="badge bg-primary">${product.rank}</span></td>
                <td>${product.name}</td>
                <td>${product.sales}</td>
                <td class="text-success">¥${product.revenue.toLocaleString()}</td>
            </tr>
        `);
    });
}

// 加载地区销售数据
function loadRegionSales() {
    const regions = [
        { name: '广东省', orders: 345, revenue: 45678, percentage: 28.5 },
        { name: '江苏省', orders: 289, revenue: 38456, percentage: 24.0 },
        { name: '浙江省', orders: 234, revenue: 31234, percentage: 19.5 },
        { name: '上海市', orders: 156, revenue: 20789, percentage: 13.0 },
        { name: '北京市', orders: 123, revenue: 16543, percentage: 10.3 }
    ];
    
    const tbody = $('#regionSalesTable');
    tbody.empty();
    
    regions.forEach(region => {
        tbody.append(`
            <tr>
                <td>${region.name}</td>
                <td>${region.orders}</td>
                <td class="text-success">¥${region.revenue.toLocaleString()}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: ${region.percentage}%">
                            ${region.percentage}%
                        </div>
                    </div>
                </td>
            </tr>
        `);
    });
}

// 初始化图表
function initializeCharts() {
    // 销售趋势图
    const salesTrendCtx = document.getElementById('salesTrendChart').getContext('2d');
    window.salesTrendChart = new Chart(salesTrendCtx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            datasets: [{
                label: '销售额 (万元)',
                data: [12, 15, 18, 22, 25, 28, 32, 35, 30, 28, 33, 38],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '订单数量',
                data: [120, 150, 180, 220, 250, 280, 320, 350, 300, 280, 330, 380],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.4,
                fill: true,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
    
    // 商品分类饼图
    const categoryPieCtx = document.getElementById('categoryPieChart').getContext('2d');
    window.categoryPieChart = new Chart(categoryPieCtx, {
        type: 'doughnut',
        data: {
            labels: ['鹅蛋类', '鹅肉类', '鹅毛制品', '鹅肝制品', '其他'],
            datasets: [{
                data: [35, 25, 20, 15, 5],
                backgroundColor: [
                    'rgb(255, 99, 132)',
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)',
                    'rgb(75, 192, 192)',
                    'rgb(153, 102, 255)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 订单状态图
    const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
    window.orderStatusChart = new Chart(orderStatusCtx, {
        type: 'bar',
        data: {
            labels: ['待付款', '待发货', '已发货', '已完成', '已取消', '退款中'],
            datasets: [{
                label: '订单数量',
                data: [45, 123, 89, 567, 23, 12],
                backgroundColor: [
                    'rgba(255, 206, 84, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(75, 192, 75, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(153, 102, 255, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateCharts(days) {
    // 根据时间范围更新图表数据
    showAlert(`已切换到最近${days}天的数据`, 'info');
}

function refreshData() {
    loadAnalyticsData();
    showAlert('数据已刷新', 'success');
}

function exportAnalytics() {
    showAlert('销售分析报告导出功能开发中', 'info');
}

function showAlert(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(alert);
    
    setTimeout(() => {
        alert.alert('close');
    }, 3000);
}
</script>
