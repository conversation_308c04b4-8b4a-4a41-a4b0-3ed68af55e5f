<%- contentFor('title') %>
评价管理

<%- contentFor('head') %>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">

<%- contentFor('content') %>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-star"></i> 评价管理
        </h1>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-warning" onclick="batchModerate()">
                <i class="bi bi-check-all"></i> 批量审核
            </button>
            <button type="button" class="btn btn-info" onclick="exportReviews()">
                <i class="bi bi-download"></i> 导出数据
            </button>
        </div>
    </div>

    <!-- 评价统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总评价数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalReviews">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-star text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">好评</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="positiveReviews">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-hand-thumbs-up text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">待审核</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pendingReviews">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">平均评分</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="averageRating">0.0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-star-fill text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 评价列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">评价列表</h6>
            <div class="d-flex gap-2">
                <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                    <option value="">全部状态</option>
                    <option value="approved">已通过</option>
                    <option value="pending">待审核</option>
                    <option value="rejected">已拒绝</option>
                </select>
                <select class="form-select form-select-sm" id="ratingFilter" style="width: auto;">
                    <option value="">全部评分</option>
                    <option value="5">5星</option>
                    <option value="4">4星</option>
                    <option value="3">3星</option>
                    <option value="2">2星</option>
                    <option value="1">1星</option>
                </select>
                <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="搜索商品或用户" style="width: 200px;">
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="reviewsTable">
                    <thead>
                        <tr>
                            <th>商品信息</th>
                            <th>用户</th>
                            <th>评分</th>
                            <th>评价内容</th>
                            <th>评价时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="reviewsTableBody">
                        <tr>
                            <td colspan="7" class="text-center text-muted py-4">
                                <i class="bi bi-star fa-3x mb-3 d-block"></i>
                                暂无评价记录
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 评价详情模态框 -->
<div class="modal fade" id="reviewDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">评价详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="reviewDetailContent">
                <!-- 评价详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-success" onclick="approveReview()">通过</button>
                <button type="button" class="btn btn-danger" onclick="rejectReview()">拒绝</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadReviewsStats();
    loadReviewsList();
});

// 加载评价统计
function loadReviewsStats() {
    const stats = {
        totalReviews: 1247,
        positiveReviews: 1089,
        pendingReviews: 23,
        averageRating: 4.6
    };
    
    $('#totalReviews').text(stats.totalReviews);
    $('#positiveReviews').text(stats.positiveReviews);
    $('#pendingReviews').text(stats.pendingReviews);
    $('#averageRating').text(stats.averageRating);
}

// 加载评价列表
function loadReviewsList() {
    const reviews = [
        {
            id: 1,
            productName: '优质鹅蛋 12枚装',
            productImage: '/images/products/eggs.jpg',
            userName: '张三',
            userAvatar: '/images/avatars/user1.jpg',
            rating: 5,
            content: '鹅蛋很新鲜，包装也很好，物流很快，下次还会购买！',
            reviewDate: '2024-01-15 14:30',
            status: 'approved',
            hasImages: true
        },
        {
            id: 2,
            productName: '农家散养鹅肉 2kg',
            productImage: '/images/products/goose.jpg',
            userName: '李四',
            userAvatar: '/images/avatars/user2.jpg',
            rating: 4,
            content: '肉质不错，就是价格稍微贵了一点，总体还是满意的。',
            reviewDate: '2024-01-14 16:45',
            status: 'pending',
            hasImages: false
        },
        {
            id: 3,
            productName: '鹅毛枕头',
            productImage: '/images/products/pillow.jpg',
            userName: '王五',
            userAvatar: '/images/avatars/user3.jpg',
            rating: 2,
            content: '枕头有异味，质量不太好，不推荐购买。',
            reviewDate: '2024-01-13 09:20',
            status: 'approved',
            hasImages: false
        }
    ];
    
    const tbody = $('#reviewsTableBody');
    tbody.empty();
    
    if (reviews.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="bi bi-star fa-3x mb-3 d-block"></i>
                    暂无评价记录
                </td>
            </tr>
        `);
        return;
    }
    
    reviews.forEach(review => {
        const statusBadge = getReviewStatusBadge(review.status);
        const stars = generateStars(review.rating);
        const imageIcon = review.hasImages ? '<i class="bi bi-image text-info ms-1"></i>' : '';
        
        tbody.append(`
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="${review.productImage}" alt="商品图片" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;" onerror="this.src='/images/placeholder.jpg'">
                        <div>
                            <div class="fw-bold">${review.productName}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="${review.userAvatar}" alt="用户头像" class="rounded-circle me-2" style="width: 30px; height: 30px;" onerror="this.src='/images/default-avatar.jpg'">
                        <span>${review.userName}</span>
                    </div>
                </td>
                <td>${stars}</td>
                <td>
                    <div class="review-content" style="max-width: 200px;">
                        ${review.content.length > 50 ? review.content.substring(0, 50) + '...' : review.content}
                        ${imageIcon}
                    </div>
                </td>
                <td>${review.reviewDate}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewReviewDetail(${review.id})">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${review.status === 'pending' ? `
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="approveReviewQuick(${review.id})">
                                <i class="bi bi-check"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="rejectReviewQuick(${review.id})">
                                <i class="bi bi-x"></i>
                            </button>
                        ` : ''}
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteReview(${review.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });
}

function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="bi bi-star-fill text-warning"></i>';
        } else {
            stars += '<i class="bi bi-star text-muted"></i>';
        }
    }
    return stars;
}

function getReviewStatusBadge(status) {
    switch(status) {
        case 'approved': return '<span class="badge bg-success">已通过</span>';
        case 'pending': return '<span class="badge bg-warning">待审核</span>';
        case 'rejected': return '<span class="badge bg-danger">已拒绝</span>';
        default: return '<span class="badge bg-light">未知</span>';
    }
}

function viewReviewDetail(id) {
    // 模拟加载评价详情
    const reviewDetail = `
        <div class="row">
            <div class="col-md-4">
                <img src="/images/products/eggs.jpg" alt="商品图片" class="img-fluid rounded" onerror="this.src='/images/placeholder.jpg'">
            </div>
            <div class="col-md-8">
                <h5>优质鹅蛋 12枚装</h5>
                <div class="mb-2">
                    <strong>评价用户：</strong> 张三
                </div>
                <div class="mb-2">
                    <strong>评分：</strong> ${generateStars(5)}
                </div>
                <div class="mb-2">
                    <strong>评价时间：</strong> 2024-01-15 14:30
                </div>
                <div class="mb-3">
                    <strong>评价内容：</strong>
                    <p class="mt-1">鹅蛋很新鲜，包装也很好，物流很快，下次还会购买！质量确实不错，推荐给大家。</p>
                </div>
                <div class="mb-2">
                    <strong>评价图片：</strong>
                    <div class="mt-1">
                        <img src="/images/reviews/review1.jpg" alt="评价图片" class="img-thumbnail me-2" style="width: 80px; height: 80px;" onerror="this.src='/images/placeholder.jpg'">
                        <img src="/images/reviews/review2.jpg" alt="评价图片" class="img-thumbnail" style="width: 80px; height: 80px;" onerror="this.src='/images/placeholder.jpg'">
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#reviewDetailContent').html(reviewDetail);
    $('#reviewDetailModal').modal('show');
}

function approveReviewQuick(id) {
    showAlert('评价已通过审核', 'success');
    loadReviewsList();
}

function rejectReviewQuick(id) {
    if (confirm('确定要拒绝这条评价吗？')) {
        showAlert('评价已拒绝', 'warning');
        loadReviewsList();
    }
}

function approveReview() {
    showAlert('评价已通过审核', 'success');
    $('#reviewDetailModal').modal('hide');
    loadReviewsList();
}

function rejectReview() {
    if (confirm('确定要拒绝这条评价吗？')) {
        showAlert('评价已拒绝', 'warning');
        $('#reviewDetailModal').modal('hide');
        loadReviewsList();
    }
}

function deleteReview(id) {
    if (confirm('确定要删除这条评价吗？删除后无法恢复。')) {
        showAlert('评价删除成功', 'success');
        loadReviewsList();
    }
}

function batchModerate() {
    showAlert('批量审核功能开发中', 'info');
}

function exportReviews() {
    showAlert('评价数据导出功能开发中', 'info');
}

function showAlert(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(alert);
    
    setTimeout(() => {
        alert.alert('close');
    }, 3000);
}
</script>
