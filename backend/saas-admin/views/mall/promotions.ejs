<%- contentFor('title') %>
促销活动管理

<%- contentFor('head') %>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">

<%- contentFor('content') %>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-megaphone"></i> 促销活动管理
        </h1>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createPromotionModal">
                <i class="bi bi-plus-lg"></i> 创建活动
            </button>
            <button type="button" class="btn btn-info" onclick="exportPromotions()">
                <i class="bi bi-download"></i> 导出数据
            </button>
        </div>
    </div>

    <!-- 促销活动统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总活动数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalPromotions">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-megaphone text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">进行中</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="activePromotions">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-play-circle text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">即将开始</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="upcomingPromotions">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">已结束</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="endedPromotions">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-stop-circle text-danger" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 促销活动列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">促销活动列表</h6>
            <div class="d-flex gap-2">
                <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                    <option value="">全部状态</option>
                    <option value="active">进行中</option>
                    <option value="upcoming">即将开始</option>
                    <option value="ended">已结束</option>
                </select>
                <select class="form-select form-select-sm" id="typeFilter" style="width: auto;">
                    <option value="">全部类型</option>
                    <option value="discount">折扣活动</option>
                    <option value="flash_sale">限时抢购</option>
                    <option value="bundle">套餐优惠</option>
                    <option value="free_shipping">包邮活动</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="promotionsTable">
                    <thead>
                        <tr>
                            <th>活动名称</th>
                            <th>活动类型</th>
                            <th>优惠力度</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>状态</th>
                            <th>参与商品</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="promotionsTableBody">
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">
                                <i class="bi bi-megaphone fa-3x mb-3 d-block"></i>
                                暂无促销活动
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 创建促销活动模态框 -->
<div class="modal fade" id="createPromotionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建促销活动</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createPromotionForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="promotionName" class="form-label">活动名称</label>
                                <input type="text" class="form-control" id="promotionName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="promotionType" class="form-label">活动类型</label>
                                <select class="form-select" id="promotionType" name="type" required>
                                    <option value="">请选择类型</option>
                                    <option value="discount">折扣活动</option>
                                    <option value="flash_sale">限时抢购</option>
                                    <option value="bundle">套餐优惠</option>
                                    <option value="free_shipping">包邮活动</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="startDate" class="form-label">开始时间</label>
                                <input type="datetime-local" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="endDate" class="form-label">结束时间</label>
                                <input type="datetime-local" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="discountValue" class="form-label">优惠力度</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="discountValue" name="discount_value" step="0.01" min="0" required>
                                    <select class="form-select" id="discountType" name="discount_type" style="max-width: 100px;">
                                        <option value="percent">%</option>
                                        <option value="amount">元</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="minAmount" class="form-label">最低消费金额</label>
                                <input type="number" class="form-control" id="minAmount" name="min_amount" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">活动描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">创建活动</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadPromotionsStats();
    loadPromotionsList();
    
    // 创建促销活动表单提交
    $('#createPromotionForm').submit(function(e) {
        e.preventDefault();
        showAlert('促销活动创建成功', 'success');
        $('#createPromotionModal').modal('hide');
        this.reset();
        loadPromotionsStats();
        loadPromotionsList();
    });
});

// 加载促销活动统计
function loadPromotionsStats() {
    const stats = {
        totalPromotions: 12,
        activePromotions: 3,
        upcomingPromotions: 2,
        endedPromotions: 7
    };
    
    $('#totalPromotions').text(stats.totalPromotions);
    $('#activePromotions').text(stats.activePromotions);
    $('#upcomingPromotions').text(stats.upcomingPromotions);
    $('#endedPromotions').text(stats.endedPromotions);
}

// 加载促销活动列表
function loadPromotionsList() {
    const promotions = [
        {
            id: 1,
            name: '春季大促销',
            type: 'discount',
            discountValue: '20%',
            startDate: '2024-03-01 00:00',
            endDate: '2024-03-31 23:59',
            status: 'active',
            productCount: 25
        },
        {
            id: 2,
            name: '限时抢购',
            type: 'flash_sale',
            discountValue: '50%',
            startDate: '2024-03-15 10:00',
            endDate: '2024-03-15 18:00',
            status: 'ended',
            productCount: 5
        }
    ];
    
    const tbody = $('#promotionsTableBody');
    tbody.empty();
    
    if (promotions.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="bi bi-megaphone fa-3x mb-3 d-block"></i>
                    暂无促销活动
                </td>
            </tr>
        `);
        return;
    }
    
    promotions.forEach(promotion => {
        const statusBadge = getStatusBadge(promotion.status);
        const typeName = getTypeName(promotion.type);
        
        tbody.append(`
            <tr>
                <td><strong>${promotion.name}</strong></td>
                <td>${typeName}</td>
                <td class="text-success">${promotion.discountValue}</td>
                <td>${promotion.startDate}</td>
                <td>${promotion.endDate}</td>
                <td>${statusBadge}</td>
                <td>${promotion.productCount} 个商品</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editPromotion(${promotion.id})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deletePromotion(${promotion.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });
}

function getStatusBadge(status) {
    switch(status) {
        case 'active': return '<span class="badge bg-success">进行中</span>';
        case 'upcoming': return '<span class="badge bg-warning">即将开始</span>';
        case 'ended': return '<span class="badge bg-secondary">已结束</span>';
        default: return '<span class="badge bg-light">未知</span>';
    }
}

function getTypeName(type) {
    switch(type) {
        case 'discount': return '折扣活动';
        case 'flash_sale': return '限时抢购';
        case 'bundle': return '套餐优惠';
        case 'free_shipping': return '包邮活动';
        default: return '未知类型';
    }
}

function editPromotion(id) {
    showAlert('编辑促销活动功能开发中', 'info');
}

function deletePromotion(id) {
    if (confirm('确定要删除这个促销活动吗？')) {
        showAlert('促销活动删除成功', 'success');
        loadPromotionsList();
    }
}

function exportPromotions() {
    showAlert('促销活动数据导出功能开发中', 'info');
}

function showAlert(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(alert);
    
    setTimeout(() => {
        alert.alert('close');
    }, 3000);
}
</script>
