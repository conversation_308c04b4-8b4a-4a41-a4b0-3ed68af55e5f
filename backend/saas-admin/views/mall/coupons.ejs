<%- contentFor('title') %>
优惠券管理

<%- contentFor('head') %>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">

<%- contentFor('content') %>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-ticket-perforated"></i> 优惠券管理
        </h1>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createCouponModal">
                <i class="bi bi-plus-lg"></i> 创建优惠券
            </button>
            <button type="button" class="btn btn-warning" onclick="batchGenerate()">
                <i class="bi bi-collection"></i> 批量生成
            </button>
            <button type="button" class="btn btn-info" onclick="exportCoupons()">
                <i class="bi bi-download"></i> 导出数据
            </button>
        </div>
    </div>

    <!-- 优惠券统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总优惠券数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalCoupons">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-ticket-perforated text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">可用优惠券</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="availableCoupons">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">已使用</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="usedCoupons">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check2-square text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">已过期</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="expiredCoupons">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-x-circle text-danger" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 优惠券列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">优惠券列表</h6>
            <div class="d-flex gap-2">
                <select class="form-select form-select-sm" id="statusFilter" style="width: auto;">
                    <option value="">全部状态</option>
                    <option value="available">可用</option>
                    <option value="used">已使用</option>
                    <option value="expired">已过期</option>
                </select>
                <select class="form-select form-select-sm" id="typeFilter" style="width: auto;">
                    <option value="">全部类型</option>
                    <option value="discount">折扣券</option>
                    <option value="amount">满减券</option>
                    <option value="free_shipping">包邮券</option>
                </select>
                <input type="text" class="form-control form-control-sm" id="searchInput" placeholder="搜索优惠券码" style="width: 200px;">
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="couponsTable">
                    <thead>
                        <tr>
                            <th>优惠券码</th>
                            <th>优惠券名称</th>
                            <th>类型</th>
                            <th>优惠金额</th>
                            <th>使用条件</th>
                            <th>有效期</th>
                            <th>状态</th>
                            <th>使用次数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="couponsTableBody">
                        <tr>
                            <td colspan="9" class="text-center text-muted py-4">
                                <i class="bi bi-ticket-perforated fa-3x mb-3 d-block"></i>
                                暂无优惠券
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 创建优惠券模态框 -->
<div class="modal fade" id="createCouponModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建优惠券</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createCouponForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="couponName" class="form-label">优惠券名称</label>
                                <input type="text" class="form-control" id="couponName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="couponCode" class="form-label">优惠券码</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="couponCode" name="code" required>
                                    <button type="button" class="btn btn-outline-secondary" onclick="generateCouponCode()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="couponType" class="form-label">优惠券类型</label>
                                <select class="form-select" id="couponType" name="type" required>
                                    <option value="">请选择类型</option>
                                    <option value="discount">折扣券</option>
                                    <option value="amount">满减券</option>
                                    <option value="free_shipping">包邮券</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="discountValue" class="form-label">优惠金额</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="discountValue" name="discount_value" step="0.01" min="0" required>
                                    <span class="input-group-text" id="discountUnit">元</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="minAmount" class="form-label">最低消费金额</label>
                                <input type="number" class="form-control" id="minAmount" name="min_amount" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="usageLimit" class="form-label">使用次数限制</label>
                                <input type="number" class="form-control" id="usageLimit" name="usage_limit" min="1" value="1">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="startDate" class="form-label">开始时间</label>
                                <input type="datetime-local" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="endDate" class="form-label">结束时间</label>
                                <input type="datetime-local" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">使用说明</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">创建优惠券</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadCouponsStats();
    loadCouponsList();
    
    // 优惠券类型改变时更新单位
    $('#couponType').change(function() {
        const type = $(this).val();
        const unit = type === 'discount' ? '%' : '元';
        $('#discountUnit').text(unit);
    });
    
    // 创建优惠券表单提交
    $('#createCouponForm').submit(function(e) {
        e.preventDefault();
        showAlert('优惠券创建成功', 'success');
        $('#createCouponModal').modal('hide');
        this.reset();
        loadCouponsStats();
        loadCouponsList();
    });
});

// 生成优惠券码
function generateCouponCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 8; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    $('#couponCode').val(code);
}

// 加载优惠券统计
function loadCouponsStats() {
    const stats = {
        totalCoupons: 156,
        availableCoupons: 89,
        usedCoupons: 45,
        expiredCoupons: 22
    };
    
    $('#totalCoupons').text(stats.totalCoupons);
    $('#availableCoupons').text(stats.availableCoupons);
    $('#usedCoupons').text(stats.usedCoupons);
    $('#expiredCoupons').text(stats.expiredCoupons);
}

// 加载优惠券列表
function loadCouponsList() {
    const coupons = [
        {
            id: 1,
            code: 'SPRING20',
            name: '春季折扣券',
            type: 'discount',
            discountValue: '20%',
            minAmount: 100,
            validUntil: '2024-03-31',
            status: 'available',
            usageCount: 0,
            usageLimit: 100
        },
        {
            id: 2,
            code: 'SAVE50',
            name: '满减优惠券',
            type: 'amount',
            discountValue: '50元',
            minAmount: 200,
            validUntil: '2024-04-15',
            status: 'available',
            usageCount: 15,
            usageLimit: 50
        }
    ];
    
    const tbody = $('#couponsTableBody');
    tbody.empty();
    
    if (coupons.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="9" class="text-center text-muted py-4">
                    <i class="bi bi-ticket-perforated fa-3x mb-3 d-block"></i>
                    暂无优惠券
                </td>
            </tr>
        `);
        return;
    }
    
    coupons.forEach(coupon => {
        const statusBadge = getCouponStatusBadge(coupon.status);
        const typeName = getCouponTypeName(coupon.type);
        const condition = coupon.minAmount > 0 ? `满${coupon.minAmount}元可用` : '无门槛';
        
        tbody.append(`
            <tr>
                <td><code>${coupon.code}</code></td>
                <td><strong>${coupon.name}</strong></td>
                <td>${typeName}</td>
                <td class="text-success">${coupon.discountValue}</td>
                <td>${condition}</td>
                <td>${coupon.validUntil}</td>
                <td>${statusBadge}</td>
                <td>${coupon.usageCount}/${coupon.usageLimit}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editCoupon(${coupon.id})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCoupon(${coupon.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
    });
}

function getCouponStatusBadge(status) {
    switch(status) {
        case 'available': return '<span class="badge bg-success">可用</span>';
        case 'used': return '<span class="badge bg-warning">已使用</span>';
        case 'expired': return '<span class="badge bg-secondary">已过期</span>';
        default: return '<span class="badge bg-light">未知</span>';
    }
}

function getCouponTypeName(type) {
    switch(type) {
        case 'discount': return '折扣券';
        case 'amount': return '满减券';
        case 'free_shipping': return '包邮券';
        default: return '未知类型';
    }
}

function editCoupon(id) {
    showAlert('编辑优惠券功能开发中', 'info');
}

function deleteCoupon(id) {
    if (confirm('确定要删除这个优惠券吗？')) {
        showAlert('优惠券删除成功', 'success');
        loadCouponsList();
    }
}

function batchGenerate() {
    showAlert('批量生成优惠券功能开发中', 'info');
}

function exportCoupons() {
    showAlert('优惠券数据导出功能开发中', 'info');
}

function showAlert(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(alert);
    
    setTimeout(() => {
        alert.alert('close');
    }, 3000);
}
</script>
