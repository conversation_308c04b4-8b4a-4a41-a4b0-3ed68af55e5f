<div class="row">
  <div class="col-12">
    <!-- Success/Error Messages -->
    <% if (typeof query !== 'undefined' && query.success) { %>
      <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        系统设置已成功更新
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <% } %>

    <!-- System Configuration Form -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-cogs me-2"></i>系统配置
        </h3>
        <div class="card-tools">
          <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetToDefaults()">
            <i class="fas fa-undo me-1"></i>恢复默认
          </button>
        </div>
      </div>
      <form id="systemConfigForm" action="/system/update" method="POST">
        <div class="card-body">
          <!-- Basic Settings -->
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label for="siteName" class="form-label">网站名称</label>
                <input type="text" class="form-control" id="siteName" name="siteName" 
                       value="<%= systemConfig.siteName %>" required>
              </div>
              <div class="form-group mb-3">
                <label for="siteDescription" class="form-label">网站描述</label>
                <textarea class="form-control" id="siteDescription" name="siteDescription" rows="3" required><%= systemConfig.siteDescription %></textarea>
              </div>
              <div class="form-group mb-3">
                <label for="adminEmail" class="form-label">管理员邮箱</label>
                <input type="email" class="form-control" id="adminEmail" name="adminEmail" 
                       value="<%= systemConfig.adminEmail %>" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label for="timezone" class="form-label">时区</label>
                <select class="form-select" id="timezone" name="timezone">
                  <option value="Asia/Shanghai" <%= systemConfig.timezone === 'Asia/Shanghai' ? 'selected' : '' %>>Asia/Shanghai (中国标准时间)</option>
                  <option value="UTC" <%= systemConfig.timezone === 'UTC' ? 'selected' : '' %>>UTC (协调世界时)</option>
                  <option value="America/New_York" <%= systemConfig.timezone === 'America/New_York' ? 'selected' : '' %>>America/New_York (东部时间)</option>
                  <option value="Europe/London" <%= systemConfig.timezone === 'Europe/London' ? 'selected' : '' %>>Europe/London (伦敦时间)</option>
                </select>
              </div>
              <div class="form-group mb-3">
                <label for="language" class="form-label">系统语言</label>
                <select class="form-select" id="language" name="language">
                  <option value="zh-CN" <%= systemConfig.language === 'zh-CN' ? 'selected' : '' %>>简体中文</option>
                  <option value="zh-TW" <%= systemConfig.language === 'zh-TW' ? 'selected' : '' %>>繁體中文</option>
                  <option value="en-US" <%= systemConfig.language === 'en-US' ? 'selected' : '' %>>English</option>
                </select>
              </div>
              <div class="form-group mb-3">
                <label for="logLevel" class="form-label">日志级别</label>
                <select class="form-select" id="logLevel" name="logLevel">
                  <option value="debug" <%= systemConfig.logLevel === 'debug' ? 'selected' : '' %>>Debug</option>
                  <option value="info" <%= systemConfig.logLevel === 'info' ? 'selected' : '' %>>Info</option>
                  <option value="warn" <%= systemConfig.logLevel === 'warn' ? 'selected' : '' %>>Warning</option>
                  <option value="error" <%= systemConfig.logLevel === 'error' ? 'selected' : '' %>>Error</option>
                </select>
              </div>
            </div>
          </div>

          <!-- System Switches -->
          <div class="row mb-4">
            <div class="col-12">
              <h5 class="mb-3"><i class="fas fa-toggle-on me-2 text-primary"></i>系统开关</h5>
              <div class="row">
                <div class="col-md-4">
                  <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="maintenanceMode" name="maintenanceMode" 
                           <%= systemConfig.maintenanceMode ? 'checked' : '' %>>
                    <label class="form-check-label" for="maintenanceMode">
                      <strong>维护模式</strong>
                      <br><small class="text-muted">启用后用户无法访问系统</small>
                    </label>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="registrationEnabled" name="registrationEnabled" 
                           <%= systemConfig.registrationEnabled ? 'checked' : '' %>>
                    <label class="form-check-label" for="registrationEnabled">
                      <strong>允许注册</strong>
                      <br><small class="text-muted">允许新用户注册账户</small>
                    </label>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="emailVerificationRequired" name="emailVerificationRequired" 
                           <%= systemConfig.emailVerificationRequired ? 'checked' : '' %>>
                    <label class="form-check-label" for="emailVerificationRequired">
                      <strong>邮箱验证</strong>
                      <br><small class="text-muted">注册时需要邮箱验证</small>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Performance Settings -->
          <div class="row mb-4">
            <div class="col-12">
              <h5 class="mb-3"><i class="fas fa-tachometer-alt me-2 text-success"></i>性能配置</h5>
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label for="maxFileUploadSize" class="form-label">最大文件上传大小 (MB)</label>
                    <div class="input-group">
                      <input type="number" class="form-control" id="maxFileUploadSize" name="maxFileUploadSize" 
                             value="<%= systemConfig.maxFileUploadSize %>" min="1" max="100">
                      <span class="input-group-text">MB</span>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label for="sessionTimeout" class="form-label">会话超时 (分钟)</label>
                    <div class="input-group">
                      <input type="number" class="form-control" id="sessionTimeout" name="sessionTimeout" 
                             value="<%= systemConfig.sessionTimeout %>" min="5" max="1440">
                      <span class="input-group-text">分钟</span>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-3">
                    <label for="backupFrequency" class="form-label">备份频率</label>
                    <select class="form-select" id="backupFrequency" name="backupFrequency">
                      <option value="hourly" <%= systemConfig.backupFrequency === 'hourly' ? 'selected' : '' %>>每小时</option>
                      <option value="daily" <%= systemConfig.backupFrequency === 'daily' ? 'selected' : '' %>>每日</option>
                      <option value="weekly" <%= systemConfig.backupFrequency === 'weekly' ? 'selected' : '' %>>每周</option>
                      <option value="monthly" <%= systemConfig.backupFrequency === 'monthly' ? 'selected' : '' %>>每月</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <div class="row">
            <div class="col-md-6">
              <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                某些设置更改可能需要重启系统才能生效
              </small>
            </div>
            <div class="col-md-6 text-end">
              <button type="button" class="btn btn-secondary me-2" onclick="testSettings()">
                <i class="fas fa-vial me-1"></i>测试设置
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-1"></i>保存设置
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- System Information -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-server me-2"></i>系统信息
        </h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-sm">
              <tbody>
                <tr>
                  <th width="40%">系统版本:</th>
                  <td>1.0.0</td>
                </tr>
                <tr>
                  <th>Node.js版本:</th>
                  <td><%= process.version %></td>
                </tr>
                <tr>
                  <th>运行环境:</th>
                  <td><%= process.env.NODE_ENV || 'development' %></td>
                </tr>
                <tr>
                  <th>启动时间:</th>
                  <td id="startTime">计算中...</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-sm">
              <tbody>
                <tr>
                  <th width="40%">数据库状态:</th>
                  <td><span class="badge badge-success">已连接</span></td>
                </tr>
                <tr>
                  <th>缓存状态:</th>
                  <td><span class="badge badge-success">正常</span></td>
                </tr>
                <tr>
                  <th>内存使用:</th>
                  <td id="memoryUsage">计算中...</td>
                </tr>
                <tr>
                  <th>CPU使用:</th>
                  <td id="cpuUsage">计算中...</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-bolt me-2"></i>快捷操作
        </h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <div class="d-grid">
              <button class="btn btn-outline-info" onclick="clearCache()">
                <i class="fas fa-broom mb-2"></i><br>清理缓存
              </button>
            </div>
          </div>
          <div class="col-md-3">
            <div class="d-grid">
              <button class="btn btn-outline-warning" onclick="optimizeDatabase()">
                <i class="fas fa-database mb-2"></i><br>优化数据库
              </button>
            </div>
          </div>
          <div class="col-md-3">
            <div class="d-grid">
              <button class="btn btn-outline-success" onclick="createBackup()">
                <i class="fas fa-download mb-2"></i><br>创建备份
              </button>
            </div>
          </div>
          <div class="col-md-3">
            <div class="d-grid">
              <button class="btn btn-outline-danger" onclick="showRestartModal()">
                <i class="fas fa-redo mb-2"></i><br>重启系统
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Restart Confirmation Modal -->
<div class="modal fade" id="restartModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title">
          <i class="fas fa-exclamation-triangle me-2"></i>系统重启确认
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <p><strong>警告：</strong>重启系统将会：</p>
        <ul>
          <li>暂时中断所有用户连接</li>
          <li>清除内存中的临时数据</li>
          <li>重新加载所有配置文件</li>
        </ul>
        <p>确定要继续吗？</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-danger" onclick="confirmRestart()">确认重启</button>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Update system info
  updateSystemInfo();
  
  // Update system info every 30 seconds
  setInterval(updateSystemInfo, 30000);
  
  // Form validation
  const form = document.getElementById('systemConfigForm');
  form.addEventListener('submit', function(e) {
    if (!form.checkValidity()) {
      e.preventDefault();
      e.stopPropagation();
    }
    form.classList.add('was-validated');
  });
});

function updateSystemInfo() {
  // Update start time
  const startTime = new Date(Date.now() - process.uptime() * 1000);
  document.getElementById('startTime').textContent = startTime.toLocaleString();
  
  // Update memory usage (mock data)
  const memUsage = (Math.random() * 30 + 40).toFixed(1);
  document.getElementById('memoryUsage').textContent = memUsage + '%';
  
  // Update CPU usage (mock data)
  const cpuUsage = (Math.random() * 20 + 10).toFixed(1);
  document.getElementById('cpuUsage').textContent = cpuUsage + '%';
}

function resetToDefaults() {
  if (confirm('确定要恢复所有设置为默认值吗？这将撤销所有自定义配置。')) {
    // Reset form to default values
    document.getElementById('siteName').value = '智慧养鹅SaaS平台';
    document.getElementById('siteDescription').value = '专业的养鹅管理SaaS解决方案';
    document.getElementById('adminEmail').value = '<EMAIL>';
    document.getElementById('timezone').value = 'Asia/Shanghai';
    document.getElementById('language').value = 'zh-CN';
    document.getElementById('logLevel').value = 'info';
    document.getElementById('maintenanceMode').checked = false;
    document.getElementById('registrationEnabled').checked = true;
    document.getElementById('emailVerificationRequired').checked = true;
    document.getElementById('maxFileUploadSize').value = 10;
    document.getElementById('sessionTimeout').value = 30;
    document.getElementById('backupFrequency').value = 'daily';
  }
}

function testSettings() {
  showToast('正在测试系统设置...', 'info');
  
  // Simulate testing
  setTimeout(() => {
    showToast('设置测试完成，所有配置正常！', 'success');
  }, 2000);
}

function clearCache() {
  if (confirm('确定要清理系统缓存吗？这可能会暂时影响系统性能。')) {
    showToast('正在清理缓存...', 'info');
    
    // Simulate cache clearing
    setTimeout(() => {
      showToast('缓存清理完成！', 'success');
    }, 3000);
  }
}

function optimizeDatabase() {
  if (confirm('确定要优化数据库吗？这可能需要几分钟时间。')) {
    showToast('正在优化数据库...', 'info');
    
    // Simulate database optimization
    setTimeout(() => {
      showToast('数据库优化完成！', 'success');
    }, 5000);
  }
}

function createBackup() {
  showToast('正在创建系统备份...', 'info');
  
  // Simulate backup creation
  setTimeout(() => {
    showToast('备份创建完成！', 'success');
  }, 4000);
}

function showRestartModal() {
  const modal = new bootstrap.Modal(document.getElementById('restartModal'));
  modal.show();
}

function confirmRestart() {
  const modal = bootstrap.Modal.getInstance(document.getElementById('restartModal'));
  modal.hide();
  
  showToast('系统正在重启，请稍后...', 'warning');
  
  // Simulate restart
  setTimeout(() => {
    location.reload();
  }, 3000);
}

function showToast(message, type = 'info') {
  const toast = document.getElementById('mainToast');
  const toastMessage = document.getElementById('toastMessage');
  
  // Set message
  toastMessage.innerHTML = message;
  
  // Set type-specific styling
  toast.className = 'toast align-items-center border-0';
  switch(type) {
    case 'success':
      toast.classList.add('text-bg-success');
      break;
    case 'warning':
      toast.classList.add('text-bg-warning');
      break;
    case 'danger':
      toast.classList.add('text-bg-danger');
      break;
    default:
      toast.classList.add('text-bg-info');
  }
  
  // Show toast
  const bsToast = new bootstrap.Toast(toast);
  bsToast.show();
}
</script>