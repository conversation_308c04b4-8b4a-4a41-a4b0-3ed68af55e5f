<div class="row">
  <div class="col-12">
    <!-- Current Status -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">系统状态</h3>
        <div class="card-tools">
          <% if (maintenanceInfo.maintenanceMode) { %>
            <button type="button" class="btn btn-sm btn-success" onclick="disableMaintenanceMode()">
              <i class="fas fa-power-off"></i> 退出维护模式
            </button>
          <% } else { %>
            <button type="button" class="btn btn-sm btn-warning" onclick="enableMaintenanceMode()">
              <i class="fas fa-tools"></i> 进入维护模式
            </button>
          <% } %>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <h5>当前状态</h5>
            <% if (maintenanceInfo.maintenanceMode) { %>
              <div class="alert alert-warning">
                <h6><i class="icon fas fa-tools"></i> 系统维护中</h6>
                系统当前处于维护模式，用户访问受限。
              </div>
            <% } else { %>
              <div class="alert alert-success">
                <h6><i class="icon fas fa-check-circle"></i> 系统正常运行</h6>
                所有服务正常，用户可以正常访问。
              </div>
            <% } %>
          </div>
          <div class="col-md-6">
            <h5>紧急联系人</h5>
            <ul class="list-unstyled">
              <% maintenanceInfo.emergencyContacts.forEach(contact => { %>
                <li class="mb-2">
                  <strong><%= contact.name %></strong><br>
                  <i class="fas fa-phone text-muted"></i> <%= contact.phone %><br>
                  <i class="fas fa-envelope text-muted"></i> <%= contact.email %>
                </li>
              <% }) %>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Scheduled Maintenance -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">计划维护</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleMaintenanceModal">
            <i class="fas fa-plus"></i> 计划维护
          </button>
        </div>
      </div>
      <div class="card-body">
        <% if (maintenanceInfo.scheduledMaintenance.length > 0) { %>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>标题</th>
                  <th>计划时间</th>
                  <th>预计时长</th>
                  <th>影响服务</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% maintenanceInfo.scheduledMaintenance.forEach(maintenance => { %>
                  <tr>
                    <td>
                      <strong><%= maintenance.title %></strong><br>
                      <small class="text-muted"><%= maintenance.description %></small>
                    </td>
                    <td><%= new Date(maintenance.scheduledTime).toLocaleString() %></td>
                    <td><%= maintenance.estimatedDuration %></td>
                    <td>
                      <% maintenance.affectedServices.forEach(service => { %>
                        <span class="badge badge-secondary mr-1"><%= service %></span>
                      <% }) %>
                    </td>
                    <td>
                      <span class="badge badge-<%= maintenance.status === 'scheduled' ? 'info' : maintenance.status === 'in_progress' ? 'warning' : 'success' %>">
                        <%= maintenance.status === 'scheduled' ? '已计划' : maintenance.status === 'in_progress' ? '进行中' : '已完成' %>
                      </span>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button class="btn btn-sm btn-outline-info" onclick="viewMaintenanceDetails(<%= maintenance.id %>)">
                          <i class="fas fa-eye"></i>
                        </button>
                        <% if (maintenance.status === 'scheduled') { %>
                          <button class="btn btn-sm btn-outline-danger" onclick="cancelMaintenance(<%= maintenance.id %>)">
                            <i class="fas fa-times"></i>
                          </button>
                        <% } %>
                      </div>
                    </td>
                  </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <p class="text-muted">暂无计划维护任务</p>
        <% } %>
      </div>
    </div>

    <!-- Maintenance History -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">维护历史</h3>
      </div>
      <div class="card-body">
        <% if (maintenanceInfo.maintenanceHistory.length > 0) { %>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>标题</th>
                  <th>开始时间</th>
                  <th>结束时间</th>
                  <th>持续时间</th>
                  <th>状态</th>
                  <th>结果</th>
                </tr>
              </thead>
              <tbody>
                <% maintenanceInfo.maintenanceHistory.forEach(history => { %>
                  <tr>
                    <td>
                      <strong><%= history.title %></strong><br>
                      <small class="text-muted"><%= history.description %></small>
                    </td>
                    <td><%= new Date(history.startTime).toLocaleString() %></td>
                    <td><%= new Date(history.endTime).toLocaleString() %></td>
                    <td><%= history.duration %></td>
                    <td>
                      <span class="badge badge-<%= history.status === 'completed' ? 'success' : 'danger' %>">
                        <%= history.status === 'completed' ? '已完成' : '失败' %>
                      </span>
                    </td>
                    <td><%= history.result %></td>
                  </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        <% } else { %>
          <p class="text-muted">暂无维护历史记录</p>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Schedule Maintenance Modal -->
<div class="modal fade" id="scheduleMaintenanceModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">计划维护</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form id="scheduleMaintenanceForm">
        <div class="modal-body">
          <div class="mb-3">
            <label for="maintenanceTitle" class="form-label">维护标题</label>
            <input type="text" class="form-control" id="maintenanceTitle" required>
          </div>
          <div class="mb-3">
            <label for="maintenanceDescription" class="form-label">维护描述</label>
            <textarea class="form-control" id="maintenanceDescription" rows="3" required></textarea>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="maintenanceDate" class="form-label">维护日期</label>
                <input type="date" class="form-control" id="maintenanceDate" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="maintenanceTime" class="form-label">维护时间</label>
                <input type="time" class="form-control" id="maintenanceTime" required>
              </div>
            </div>
          </div>
          <div class="mb-3">
            <label for="estimatedDuration" class="form-label">预计时长</label>
            <input type="text" class="form-control" id="estimatedDuration" placeholder="例如: 2小时" required>
          </div>
          <div class="mb-3">
            <label class="form-label">影响的服务</label>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" value="database" id="serviceDatabase">
              <label class="form-check-label" for="serviceDatabase">数据库</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" value="api" id="serviceApi">
              <label class="form-check-label" for="serviceApi">API服务</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" value="web" id="serviceWeb">
              <label class="form-check-label" for="serviceWeb">网页界面</label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary">计划维护</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
function enableMaintenanceMode() {
  if (confirm('确定要启用维护模式吗？这将限制用户访问系统。')) {
    const message = prompt('请输入维护提示信息:', '系统正在维护中，请稍后访问');
    if (message !== null) {
      axios.post('/system/maintenance/enable', {
        message: message,
        duration: '2小时'
      }).then(response => {
        if (response.data.success) {
          location.reload();
        } else {
          alert('启用维护模式失败');
        }
      }).catch(error => {
        alert('启用维护模式失败');
        console.error(error);
      });
    }
  }
}

function disableMaintenanceMode() {
  if (confirm('确定要退出维护模式吗？')) {
    axios.post('/system/maintenance/disable')
      .then(response => {
        if (response.data.success) {
          location.reload();
        } else {
          alert('退出维护模式失败');
        }
      })
      .catch(error => {
        alert('退出维护模式失败');
        console.error(error);
      });
  }
}

function viewMaintenanceDetails(id) {
  // TODO: Implement maintenance details modal
  alert('查看维护详情: ' + id);
}

function cancelMaintenance(id) {
  if (confirm('确定要取消此维护计划吗？')) {
    // TODO: Implement cancel maintenance
    alert('取消维护: ' + id);
  }
}

// Schedule maintenance form
document.getElementById('scheduleMaintenanceForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const services = [];
  document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
    services.push(checkbox.value);
  });
  
  // TODO: Submit maintenance schedule
  alert('维护计划已提交');
  bootstrap.Modal.getInstance(document.getElementById('scheduleMaintenanceModal')).hide();
});
</script>