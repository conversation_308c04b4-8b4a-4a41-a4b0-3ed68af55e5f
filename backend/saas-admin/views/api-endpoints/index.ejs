<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 智慧养鹅管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .endpoint-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .endpoint-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .method-badge {
            font-weight: bold;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .method-get { background-color: #28a745; }
        .method-post { background-color: #007bff; }
        .method-put { background-color: #ffc107; color: #000; }
        .method-delete { background-color: #dc3545; }
        .category-header {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
        }
        .parameter-tag {
            background-color: #e9ecef;
            color: #6c757d;
            font-size: 0.75rem;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
            display: inline-block;
        }
        .api-path {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.9rem;
            color: #333;
        }
        .test-button {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .endpoint-card:hover .test-button {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="bi bi-gear me-2"></i>API接口文档
                        </h1>
                        <p class="text-muted">智慧养鹅管理系统API接口列表及使用说明</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="testAllEndpoints()">
                            <i class="bi bi-play-circle me-1"></i>测试所有接口
                        </button>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="bi bi-api" style="font-size: 2rem;"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0">总接口数</h5>
                                        <h3 class="mb-0" id="totalEndpoints"><%= endpoints.length %></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0">GET接口</h5>
                                        <h3 class="mb-0" id="getEndpoints"><%= endpoints.filter(e => e.method === 'GET').length %></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="bi bi-plus-circle" style="font-size: 2rem;"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0">POST接口</h5>
                                        <h3 class="mb-0" id="postEndpoints"><%= endpoints.filter(e => e.method === 'POST').length %></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="bi bi-pencil-square" style="font-size: 2rem;"></i>
                                    </div>
                                    <div>
                                        <h5 class="card-title mb-0">其他接口</h5>
                                        <h3 class="mb-0" id="otherEndpoints"><%= endpoints.filter(e => e.method !== 'GET' && e.method !== 'POST').length %></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 接口分组展示 -->
                <%
                    const groupedEndpoints = {};
                    endpoints.forEach(endpoint => {
                        if (!groupedEndpoints[endpoint.category]) {
                            groupedEndpoints[endpoint.category] = [];
                        }
                        groupedEndpoints[endpoint.category].push(endpoint);
                    });
                %>

                <% for (const [category, categoryEndpoints] of Object.entries(groupedEndpoints)) { %>
                <div class="mb-4">
                    <h4 class="category-header">
                        <i class="bi bi-folder me-2"></i><%= category %>
                        <span class="badge bg-secondary ms-2"><%= categoryEndpoints.length %></span>
                    </h4>
                    
                    <div class="row">
                        <% categoryEndpoints.forEach(endpoint => { %>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card endpoint-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <span class="badge method-badge method-<%= endpoint.method.toLowerCase() %>">
                                            <%= endpoint.method %>
                                        </span>
                                        <button class="btn btn-sm btn-outline-primary test-button" onclick="testEndpoint('<%= endpoint.path %>', '<%= endpoint.method %>')">
                                            <i class="bi bi-play-circle"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="api-path mb-2">
                                        <%= endpoint.path %>
                                    </div>
                                    
                                    <p class="card-text text-muted small mb-2">
                                        <%= endpoint.description %>
                                    </p>
                                    
                                    <% if (endpoint.parameters && endpoint.parameters.length > 0) { %>
                                    <div class="mt-2">
                                        <small class="text-muted d-block mb-1">参数：</small>
                                        <% endpoint.parameters.forEach(param => { %>
                                            <span class="parameter-tag"><%= param %></span>
                                        <% }) %>
                                    </div>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                        <% }) %>
                    </div>
                </div>
                <% } %>
            </div>
        </div>
    </div>

    <!-- 测试结果模态框 -->
    <div class="modal fade" id="testResultModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">API测试结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="testResultContent"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testModal;

        document.addEventListener('DOMContentLoaded', function() {
            testModal = new bootstrap.Modal(document.getElementById('testResultModal'));
        });

        function testEndpoint(path, method) {
            const loading = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在测试 ${method} ${path}...</p>
                </div>
            `;
            
            document.getElementById('testResultContent').innerHTML = loading;
            testModal.show();

            fetch(path, {
                method: method,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                return response.json().then(data => ({
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }));
            })
            .then(result => {
                const isSuccess = result.status >= 200 && result.status < 300;
                const statusClass = isSuccess ? 'success' : 'danger';
                const statusIcon = isSuccess ? 'check-circle' : 'x-circle';
                
                const resultHtml = `
                    <div class="alert alert-${statusClass}">
                        <h6><i class="bi bi-${statusIcon} me-2"></i>${method} ${path}</h6>
                        <p>状态码: ${result.status} ${result.statusText}</p>
                    </div>
                    
                    <h6>响应数据:</h6>
                    <pre class="bg-light p-3 rounded"><code>${JSON.stringify(result.data, null, 2)}</code></pre>
                `;
                
                document.getElementById('testResultContent').innerHTML = resultHtml;
            })
            .catch(error => {
                const errorHtml = `
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-x-circle me-2"></i>测试失败</h6>
                        <p>${method} ${path}</p>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
                
                document.getElementById('testResultContent').innerHTML = errorHtml;
            });
        }

        function testAllEndpoints() {
            const endpoints = <%- JSON.stringify(endpoints) %>;
            const getEndpoints = endpoints.filter(ep => ep.method === 'GET');
            
            if (getEndpoints.length === 0) {
                alert('没有可测试的GET接口');
                return;
            }

            const loading = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在测试 ${getEndpoints.length} 个GET接口...</p>
                </div>
            `;
            
            document.getElementById('testResultContent').innerHTML = loading;
            testModal.show();

            Promise.allSettled(getEndpoints.map(endpoint => 
                fetch(endpoint.path, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                }).then(response => ({
                    endpoint: endpoint,
                    status: response.status,
                    success: response.ok
                })).catch(error => ({
                    endpoint: endpoint,
                    status: 0,
                    success: false,
                    error: error.message
                }))
            )).then(results => {
                const successCount = results.filter(r => r.value && r.value.success).length;
                const failCount = results.length - successCount;
                
                let resultHtml = `
                    <div class="alert alert-info mb-3">
                        <h6><i class="bi bi-info-circle me-2"></i>批量测试完成</h6>
                        <p>成功: ${successCount} | 失败: ${failCount} | 总计: ${results.length}</p>
                    </div>
                    
                    <div class="row">
                `;
                
                results.forEach(result => {
                    const endpoint = result.value.endpoint;
                    const isSuccess = result.value.success;
                    const statusClass = isSuccess ? 'success' : 'danger';
                    const statusIcon = isSuccess ? 'check-circle' : 'x-circle';
                    
                    resultHtml += `
                        <div class="col-md-6 mb-2">
                            <div class="card border-${statusClass}">
                                <div class="card-body py-2">
                                    <small>
                                        <i class="bi bi-${statusIcon} text-${statusClass} me-1"></i>
                                        ${endpoint.path}
                                        <span class="float-end">${result.value.status || 'ERR'}</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                resultHtml += '</div>';
                
                document.getElementById('testResultContent').innerHTML = resultHtml;
            });
        }
    </script>
</body>
</html>