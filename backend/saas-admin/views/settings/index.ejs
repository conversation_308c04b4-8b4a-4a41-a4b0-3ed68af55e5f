<%- contentFor('title') %>系统设置 - 智慧养鹅SAAS管理平台<% ; %>

<div class="row">
  <div class="col-12">
    <div class="page-title-box">
      <h4 class="page-title">系统设置</h4>
      <div class="page-title-right">
        <ol class="breadcrumb m-0">
          <li class="breadcrumb-item"><a href="/dashboard">控制台</a></li>
          <li class="breadcrumb-item active">系统设置</li>
        </ol>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- 基本设置 -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">基本设置</h5>
      </div>
      <div class="card-body">
        <form id="basicSettingsForm">
          <div class="mb-3">
            <label for="siteName" class="form-label">站点名称</label>
            <input type="text" class="form-control" id="siteName" value="智慧养鹅SAAS管理平台">
          </div>
          
          <div class="mb-3">
            <label for="siteDescription" class="form-label">站点描述</label>
            <textarea class="form-control" id="siteDescription" rows="3">智慧养鹅多租户管理平台，提供全面的养殖管理解决方案</textarea>
          </div>
          
          <div class="mb-3">
            <label for="contactEmail" class="form-label">联系邮箱</label>
            <input type="email" class="form-control" id="contactEmail" value="<EMAIL>">
          </div>
          
          <div class="mb-3">
            <label for="timezone" class="form-label">时区</label>
            <select class="form-control" id="timezone">
              <option value="Asia/Shanghai" selected>亚洲/上海</option>
              <option value="UTC">UTC</option>
            </select>
          </div>
          
          <button type="submit" class="btn btn-primary">保存基本设置</button>
        </form>
      </div>
    </div>
  </div>

  <!-- 安全设置 -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">安全设置</h5>
      </div>
      <div class="card-body">
        <form id="securitySettingsForm">
          <div class="mb-3">
            <label for="sessionTimeout" class="form-label">会话超时时间（分钟）</label>
            <input type="number" class="form-control" id="sessionTimeout" value="30" min="5" max="1440">
          </div>
          
          <div class="mb-3">
            <label for="maxLoginAttempts" class="form-label">最大登录尝试次数</label>
            <input type="number" class="form-control" id="maxLoginAttempts" value="5" min="1" max="20">
          </div>
          
          <div class="mb-3">
            <label for="passwordMinLength" class="form-label">密码最小长度</label>
            <input type="number" class="form-control" id="passwordMinLength" value="8" min="6" max="32">
          </div>
          
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="requireTwoFactor">
              <label class="form-check-label" for="requireTwoFactor">
                启用双因素认证
              </label>
            </div>
          </div>
          
          <div class="mb-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" id="enableAuditLog" checked>
              <label class="form-check-label" for="enableAuditLog">
                启用审计日志
              </label>
            </div>
          </div>
          
          <button type="submit" class="btn btn-warning">更新安全设置</button>
        </form>
      </div>
    </div>
  </div>
</div>

<div class="row mt-4">
  <!-- 系统维护 -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">系统维护</h5>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <button type="button" class="btn btn-info me-2" onclick="clearCache()">清空缓存</button>
          <button type="button" class="btn btn-secondary me-2" onclick="optimizeDatabase()">优化数据库</button>
        </div>
        
        <div class="mb-3">
          <label class="form-label">维护模式</label>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="maintenanceMode">
            <label class="form-check-label" for="maintenanceMode">
              启用维护模式（将暂停所有租户访问）
            </label>
          </div>
        </div>
        
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>注意：</strong>启用维护模式将影响所有租户的正常使用
        </div>
      </div>
    </div>
  </div>

  <!-- 通知设置 -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">通知设置</h5>
      </div>
      <div class="card-body">
        <form id="notificationSettingsForm">
          <div class="mb-3">
            <label for="smtpHost" class="form-label">SMTP服务器</label>
            <input type="text" class="form-control" id="smtpHost" placeholder="smtp.example.com">
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="smtpPort" class="form-label">端口</label>
                <input type="number" class="form-control" id="smtpPort" value="587">
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="smtpSecurity" class="form-label">安全连接</label>
                <select class="form-control" id="smtpSecurity">
                  <option value="tls">TLS</option>
                  <option value="ssl">SSL</option>
                  <option value="none">无</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="smtpUsername" class="form-label">用户名</label>
                <input type="text" class="form-control" id="smtpUsername">
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="smtpPassword" class="form-label">密码</label>
                <input type="password" class="form-control" id="smtpPassword">
              </div>
            </div>
          </div>
          
          <button type="submit" class="btn btn-success">保存通知设置</button>
          <button type="button" class="btn btn-outline-info ms-2" onclick="testEmail()">测试发送</button>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
// 基本设置保存
document.getElementById('basicSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const settings = {
        siteName: document.getElementById('siteName').value,
        siteDescription: document.getElementById('siteDescription').value,
        contactEmail: document.getElementById('contactEmail').value,
        timezone: document.getElementById('timezone').value
    };
    
    fetch('/api/settings/basic', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('成功', '基本设置已保存', 'success');
        } else {
            Swal.fire('错误', data.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire('错误', '保存失败，请重试', 'error');
    });
});

// 安全设置保存
document.getElementById('securitySettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const settings = {
        sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
        maxLoginAttempts: parseInt(document.getElementById('maxLoginAttempts').value),
        passwordMinLength: parseInt(document.getElementById('passwordMinLength').value),
        requireTwoFactor: document.getElementById('requireTwoFactor').checked,
        enableAuditLog: document.getElementById('enableAuditLog').checked
    };
    
    fetch('/api/settings/security', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('成功', '安全设置已更新', 'success');
        } else {
            Swal.fire('错误', data.message || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire('错误', '更新失败，请重试', 'error');
    });
});

// 通知设置保存
document.getElementById('notificationSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const settings = {
        smtpHost: document.getElementById('smtpHost').value,
        smtpPort: parseInt(document.getElementById('smtpPort').value),
        smtpSecurity: document.getElementById('smtpSecurity').value,
        smtpUsername: document.getElementById('smtpUsername').value,
        smtpPassword: document.getElementById('smtpPassword').value
    };
    
    fetch('/api/settings/notification', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('成功', '通知设置已保存', 'success');
        } else {
            Swal.fire('错误', data.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire('错误', '保存失败，请重试', 'error');
    });
});

// 清空缓存
function clearCache() {
    Swal.fire({
        title: '确认操作',
        text: '确定要清空系统缓存吗？',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/api/system/clear-cache', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('成功', '缓存已清空', 'success');
                } else {
                    Swal.fire('错误', data.message || '操作失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('错误', '操作失败，请重试', 'error');
            });
        }
    });
}

// 优化数据库
function optimizeDatabase() {
    Swal.fire({
        title: '确认操作',
        text: '确定要优化数据库吗？这可能需要几分钟时间',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: '正在优化数据库...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            fetch('/api/system/optimize-database', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('成功', '数据库优化完成', 'success');
                } else {
                    Swal.fire('错误', data.message || '优化失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('错误', '优化失败，请重试', 'error');
            });
        }
    });
}

// 测试邮件发送
function testEmail() {
    const smtpHost = document.getElementById('smtpHost').value;
    const smtpUsername = document.getElementById('smtpUsername').value;
    
    if (!smtpHost || !smtpUsername) {
        Swal.fire('提示', '请先填写SMTP配置信息', 'warning');
        return;
    }
    
    Swal.fire({
        title: '发送测试邮件',
        input: 'email',
        inputPlaceholder: '输入测试邮箱地址',
        showCancelButton: true,
        confirmButtonText: '发送',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed && result.value) {
            fetch('/api/settings/test-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    testEmail: result.value,
                    smtpConfig: {
                        host: document.getElementById('smtpHost').value,
                        port: parseInt(document.getElementById('smtpPort').value),
                        security: document.getElementById('smtpSecurity').value,
                        username: document.getElementById('smtpUsername').value,
                        password: document.getElementById('smtpPassword').value
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('成功', '测试邮件发送成功', 'success');
                } else {
                    Swal.fire('错误', data.message || '测试邮件发送失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('错误', '测试邮件发送失败，请重试', 'error');
            });
        }
    });
}

// 维护模式切换
document.getElementById('maintenanceMode').addEventListener('change', function() {
    const isEnabled = this.checked;
    
    Swal.fire({
        title: '确认操作',
        text: `确定要${isEnabled ? '启用' : '关闭'}维护模式吗？`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('/api/settings/maintenance-mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ enabled: isEnabled })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('成功', `维护模式已${isEnabled ? '启用' : '关闭'}`, 'success');
                } else {
                    Swal.fire('错误', data.message || '操作失败', 'error');
                    this.checked = !isEnabled; // 还原状态
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire('错误', '操作失败，请重试', 'error');
                this.checked = !isEnabled; // 还原状态
            });
        } else {
            this.checked = !isEnabled; // 还原状态
        }
    });
});

// 页面加载时获取当前设置
document.addEventListener('DOMContentLoaded', function() {
    // 获取基本设置
    fetch('/api/settings/basic')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.settings) {
                const settings = data.settings;
                if (settings.siteName) document.getElementById('siteName').value = settings.siteName;
                if (settings.siteDescription) document.getElementById('siteDescription').value = settings.siteDescription;
                if (settings.contactEmail) document.getElementById('contactEmail').value = settings.contactEmail;
                if (settings.timezone) document.getElementById('timezone').value = settings.timezone;
            }
        })
        .catch(error => console.error('Error loading basic settings:', error));
    
    // 获取安全设置
    fetch('/api/settings/security')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.settings) {
                const settings = data.settings;
                if (settings.sessionTimeout) document.getElementById('sessionTimeout').value = settings.sessionTimeout;
                if (settings.maxLoginAttempts) document.getElementById('maxLoginAttempts').value = settings.maxLoginAttempts;
                if (settings.passwordMinLength) document.getElementById('passwordMinLength').value = settings.passwordMinLength;
                if (typeof settings.requireTwoFactor === 'boolean') document.getElementById('requireTwoFactor').checked = settings.requireTwoFactor;
                if (typeof settings.enableAuditLog === 'boolean') document.getElementById('enableAuditLog').checked = settings.enableAuditLog;
            }
        })
        .catch(error => console.error('Error loading security settings:', error));
    
    // 获取通知设置
    fetch('/api/settings/notification')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.settings) {
                const settings = data.settings;
                if (settings.smtpHost) document.getElementById('smtpHost').value = settings.smtpHost;
                if (settings.smtpPort) document.getElementById('smtpPort').value = settings.smtpPort;
                if (settings.smtpSecurity) document.getElementById('smtpSecurity').value = settings.smtpSecurity;
                if (settings.smtpUsername) document.getElementById('smtpUsername').value = settings.smtpUsername;
                // 密码不回显，保持为空
            }
        })
        .catch(error => console.error('Error loading notification settings:', error));
    
    // 获取维护模式状态
    fetch('/api/settings/maintenance-mode')
        .then(response => response.json())
        .then(data => {
            if (data.success && typeof data.enabled === 'boolean') {
                document.getElementById('maintenanceMode').checked = data.enabled;
            }
        })
        .catch(error => console.error('Error loading maintenance mode status:', error));
});
</script>