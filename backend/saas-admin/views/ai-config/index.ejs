<!-- AI配置页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">
                        <i class="fas fa-robot me-2"></i>
                        AI配置管理
                    </h1>
                    <p class="page-subtitle">配置和管理AI大模型服务参数</p>
                </div>
                <button class="btn btn-primary" onclick="openAddConfigModal()">
                    <i class="fas fa-plus me-1"></i>添加配置
                </button>
            </div>
        </div>
    </div>

    <!-- AI服务状态 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                配置总数
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalConfigs"><%= stats?.totalConfigs || 0 %></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cog fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                活跃配置
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeConfigs"><%= stats?.activeConfigs || 0 %></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                今日调用
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayUsage"><%= stats?.todayUsage || 0 %></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                成功率
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="successRate"><%= stats?.successRate || 0 %>%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI配置列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">AI服务配置列表</h6>
                    <div>
                        <button class="btn btn-success btn-sm" onclick="testAllConfigs()">
                            <i class="fas fa-play me-1"></i>批量测试
                        </button>
                        <button class="btn btn-info btn-sm" onclick="refreshStats()">
                            <i class="fas fa-sync me-1"></i>刷新统计
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="configsTable">
                            <thead>
                                <tr>
                                    <th>服务商</th>
                                    <th>名称</th>
                                    <th>API密钥</th>
                                    <th>基础URL</th>
                                    <th>模型</th>
                                    <th>状态</th>
                                    <th>是否默认</th>
                                    <th>优先级</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="configsTableBody">
                                <% if (configs && configs.length > 0) { %>
                                    <% configs.forEach(function(config) { %>
                                    <tr data-id="<%= config.id %>">
                                        <td>
                                            <span class="badge badge-<%= config.provider === 'zhipu' ? 'info' : config.provider === 'siliconflow' ? 'success' : config.provider === 'openai' ? 'primary' : 'warning' %>">
                                                <%= providerInfo[config.provider]?.name || config.provider %>
                                            </span>
                                        </td>
                                        <td><%= config.name %></td>
                                        <td><code><%= config.maskedKey || '****' %></code></td>
                                        <td><small><%= config.baseUrl %></small></td>
                                        <td><%= (config.models || []).slice(0, 2).join(', ') %><%= (config.models || []).length > 2 ? '...' : '' %></td>
                                        <td>
                                            <span class="badge badge-<%= config.enabled ? 'success' : 'secondary' %>">
                                                <%= config.enabled ? '已启用' : '已禁用' %>
                                            </span>
                                        </td>
                                        <td>
                                            <% if (config.isDefault) { %>
                                                <span class="badge badge-warning">默认</span>
                                            <% } else { %>
                                                <span class="text-muted">-</span>
                                            <% } %>
                                        </td>
                                        <td><%= config.priority || 0 %></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-info" title="编辑" onclick="editConfig('<%= config.id %>')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-success" title="测试" onclick="testConfig('<%= config.id %>')">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <% if (!config.isDefault) { %>
                                                <button class="btn btn-danger" title="删除" onclick="deleteConfig('<%= config.id %>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <% } %>
                                            </div>
                                        </td>
                                    </tr>
                                    <% }); %>
                                <% } else { %>
                                    <tr>
                                        <td colspan="9" class="text-center text-muted py-4">
                                            <i class="fas fa-robot fa-2x mb-2"></i><br>
                                            暂无AI配置，请点击“添加配置”按钮添加新的AI服务配置
                                        </td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


<!-- AI配置模态框 -->
<div class="modal fade" id="configModal" tabindex="-1" role="dialog" aria-labelledby="configModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="configModalLabel">添加AI配置</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="configForm">
                <div class="modal-body">
                    <input type="hidden" id="configId" name="id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="provider">服务提供商 <span class="text-danger">*</span></label>
                                <select class="form-control" id="provider" name="provider" required onchange="onProviderChange()">
                                    <option value="">请选择</option>
                                    <option value="zhipu">智谱AI</option>
                                    <option value="siliconflow">硅基流动</option>
                                    <option value="openai">OpenAI</option>
                                    <option value="qianwen">通义千问</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">配置名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required placeholder="例如：智谱AI生产环境">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="apiKey">API密钥 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="apiKey" name="apiKey" required placeholder="请输入API密钥">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKeyVisibility()">
                                            <i class="fas fa-eye" id="apiKeyToggleIcon"></i>
                                        </button>
                                    </div>
                                </div>
                                <small class="form-text text-muted" id="keyFormatHint">请输入正确格式的API密钥</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="baseUrl">基础URL</label>
                                <input type="url" class="form-control" id="baseUrl" name="baseUrl" placeholder="API基础URL">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="maxTokens">最大Token数</label>
                                <input type="number" class="form-control" id="maxTokens" name="maxTokens" value="4096" min="1" max="32000">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="temperature">温度参数</label>
                                <input type="range" class="form-control-range" id="temperature" name="temperature" min="0" max="1" step="0.1" value="0.7">
                                <small class="form-text text-muted">当前值: <span id="temperatureValue">0.7</span></small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="priority">优先级</label>
                                <input type="number" class="form-control" id="priority" name="priority" value="0" min="0" max="100">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="enabled" name="enabled" checked>
                                    <label class="custom-control-label" for="enabled">启用此配置</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="isDefault" name="isDefault">
                                    <label class="custom-control-label" for="isDefault">设为默认配置</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="modelsSection" style="display: none;">
                        <div class="form-group">
                            <label>支持的模型</label>
                            <div id="modelsList"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>保存配置
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
    </div>
</div>

<script>
// 服务提供商信息
const providerInfo = {
    zhipu: {
        name: '智谱AI',
        keyFormat: 'xxxxxx.xxxxxx',
        baseUrl: 'https://open.bigmodel.cn/api/paas/v4/',
        models: ['glm-4', 'glm-4v', 'glm-3-turbo']
    },
    siliconflow: {
        name: '硅基流动',
        keyFormat: 'sk-xxxxxx',
        baseUrl: 'https://api.siliconflow.cn/v1/',
        models: ['qwen/Qwen2-72B-Instruct', 'deepseek-ai/DeepSeek-V2.5', 'meta-llama/Meta-Llama-3.1-405B-Instruct']
    },
    openai: {
        name: 'OpenAI',
        keyFormat: 'sk-xxxxxx',
        baseUrl: 'https://api.openai.com/v1/',
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo']
    },
    qianwen: {
        name: '通义千问',
        keyFormat: 'xxxxxx',
        baseUrl: 'https://dashscope.aliyuncs.com/api/v1/',
        models: ['qwen-turbo', 'qwen-plus', 'qwen-max']
    }
};

$(document).ready(function() {
    // 初始化数据表
    $('#configsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Chinese.json"
        },
        "order": [[ 7, "desc" ]], // 按优先级排序
        "pageLength": 25
    });

    // 温度参数滑块
    $('#temperature').on('input', function() {
        $('#temperatureValue').text(this.value);
    });

    // 表单提交
    $('#configForm').on('submit', function(e) {
        e.preventDefault();
        saveConfig();
    });

    // 定时刷新统计数据
    setInterval(refreshStats, 30000); // 每30秒刷新一次
});

// 打开添加配置模态框
function openAddConfigModal() {
    $('#configModalLabel').text('添加AI配置');
    $('#configForm')[0].reset();
    $('#configId').val('');
    $('#temperatureValue').text('0.7');
    $('#configModal').modal('show');
}

// 编辑配置
function editConfig(id) {
    // 这里应该加载配置数据
    $('#configModalLabel').text('编辑AI配置');
    $('#configId').val(id);
    $('#configModal').modal('show');
    
    // 模拟加载数据
    // 实际应该通过AJAX从服务器获取数据
}

// 删除配置
function deleteConfig(id) {
    if (confirm('确定要删除这个AI配置吗？此操作不可恢复。')) {
        $.ajax({
            url: `/ai-config/api/configs/${id}`,
            type: 'DELETE',
            success: function(result) {
                if (result.success) {
                    showAlert('success', '配置删除成功');
                    location.reload();
                } else {
                    showAlert('danger', result.message || '删除失败');
                }
            },
            error: function(xhr) {
                const error = xhr.responseJSON;
                showAlert('danger', error?.message || '删除失败');
            }
        });
    }
}

// 测试单个配置
function testConfig(id) {
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    $.ajax({
        url: `/ai-config/api/configs/${id}/test`,
        type: 'POST',
        success: function(result) {
            if (result.success) {
                showAlert('success', `连接测试成功！响应时间: ${result.data.responseTime}ms`);
            } else {
                showAlert('warning', result.message || '连接测试失败');
            }
        },
        error: function(xhr) {
            const error = xhr.responseJSON;
            showAlert('danger', error?.message || '连接测试失败');
        },
        complete: function() {
            btn.innerHTML = originalHtml;
            btn.disabled = false;
        }
    });
}

// 批量测试所有配置
function testAllConfigs() {
    showAlert('info', '正在测试所有配置，请稍候...');
    
    $.ajax({
        url: '/ai-config/api/configs',
        type: 'GET',
        success: function(result) {
            if (result.success) {
                const activeConfigs = result.data.filter(config => config.enabled);
                testConfigsSequentially(activeConfigs, 0);
            }
        },
        error: function() {
            showAlert('danger', '获取配置列表失败');
        }
    });
}

// 依次测试配置
function testConfigsSequentially(configs, index) {
    if (index >= configs.length) {
        showAlert('success', '所有配置测试完成');
        return;
    }
    
    const config = configs[index];
    $.ajax({
        url: `/ai-config/api/configs/${config.id}/test`,
        type: 'POST',
        success: function(result) {
            // 可以在这里更新UI显示测试结果
        },
        complete: function() {
            // 继续测试下一个
            setTimeout(() => testConfigsSequentially(configs, index + 1), 500);
        }
    });
}

// 保存配置
function saveConfig() {
    const formData = new FormData($('#configForm')[0]);
    const configId = $('#configId').val();
    
    const data = {
        provider: formData.get('provider'),
        name: formData.get('name'),
        apiKey: formData.get('apiKey'),
        baseUrl: formData.get('baseUrl'),
        maxTokens: parseInt(formData.get('maxTokens')),
        temperature: parseFloat(formData.get('temperature')),
        priority: parseInt(formData.get('priority')),
        enabled: formData.has('enabled'),
        isDefault: formData.has('isDefault')
    };

    // 获取选中的模型
    const selectedModels = [];
    $('#modelsList input:checked').each(function() {
        selectedModels.push($(this).val());
    });
    if (selectedModels.length > 0) {
        data.models = selectedModels;
    }

    const url = configId ? `/ai-config/api/configs/${configId}` : '/ai-config/api/configs';
    const method = configId ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        type: method,
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(result) {
            if (result.success) {
                showAlert('success', configId ? '配置更新成功' : '配置创建成功');
                $('#configModal').modal('hide');
                location.reload();
            } else {
                showAlert('danger', result.message || '保存失败');
            }
        },
        error: function(xhr) {
            const error = xhr.responseJSON;
            showAlert('danger', error?.message || '保存失败');
        }
    });
}

// 服务提供商改变时的处理
function onProviderChange() {
    const provider = $('#provider').val();
    const info = providerInfo[provider];
    
    if (info) {
        $('#keyFormatHint').text(`格式：${info.keyFormat}`);
        $('#baseUrl').val(info.baseUrl);
        $('#name').val(info.name + ' 配置');
        
        // 显示模型选择
        if (info.models && info.models.length > 0) {
            let modelsHtml = '';
            info.models.forEach(model => {
                modelsHtml += `
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="model_${model}" value="${model}" checked>
                        <label class="custom-control-label" for="model_${model}">${model}</label>
                    </div>
                `;
            });
            $('#modelsList').html(modelsHtml);
            $('#modelsSection').show();
        } else {
            $('#modelsSection').hide();
        }
    } else {
        $('#keyFormatHint').text('请输入正确格式的API密钥');
        $('#modelsSection').hide();
    }
}

// 切换API密钥显示/隐藏
function toggleApiKeyVisibility() {
    const input = $('#apiKey');
    const icon = $('#apiKeyToggleIcon');
    
    if (input.attr('type') === 'password') {
        input.attr('type', 'text');
        icon.removeClass('fa-eye').addClass('fa-eye-slash');
    } else {
        input.attr('type', 'password');
        icon.removeClass('fa-eye-slash').addClass('fa-eye');
    }
}

// 刷新统计数据
function refreshStats() {
    $.ajax({
        url: '/ai-config/api/stats',
        type: 'GET',
        success: function(result) {
            if (result.success) {
                // 更新统计卡片
                // 这里可以更新页面上的统计数据
            }
        }
    });
}

// 显示提示信息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;
    
    // 移除已存在的提示
    $('.alert').remove();
    
    // 添加新的提示
    $('.container-fluid').prepend(alertHtml);
    
    // 3秒后自动隐藏
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
