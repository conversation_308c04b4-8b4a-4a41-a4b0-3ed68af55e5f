<div class="row">
  <div class="col-12">
    <!-- API Statistics Cards -->
    <div class="row mb-4">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3><%= stats.totalCalls.toLocaleString() %></h3>
            <p>总调用次数</p>
          </div>
          <div class="icon">
            <i class="fas fa-chart-line"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3><%= stats.todayCalls.toLocaleString() %></h3>
            <p>今日调用</p>
          </div>
          <div class="icon">
            <i class="fas fa-calendar-day"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
          <div class="inner">
            <h3><%= stats.avgResponseTime %> ms</h3>
            <p>平均响应时间</p>
          </div>
          <div class="icon">
            <i class="fas fa-clock"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-primary">
          <div class="inner">
            <h3><%= stats.successRate %>%</h3>
            <p>成功率</p>
          </div>
          <div class="icon">
            <i class="fas fa-check-circle"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- API Endpoints Usage -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">热门API接口</h3>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>接口路径</th>
                <th>调用次数</th>
                <th>占比</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <% stats.topEndpoints.forEach(endpoint => { %>
                <tr>
                  <td><code><%= endpoint.endpoint %></code></td>
                  <td><%= endpoint.calls.toLocaleString() %></td>
                  <td>
                    <div class="progress progress-sm">
                      <div class="progress-bar bg-success" style="width: <%= endpoint.percentage %>%"></div>
                    </div>
                    <%= endpoint.percentage %>%
                  </td>
                  <td>
                    <span class="badge badge-success">正常</span>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Daily Statistics Chart -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">近7天调用趋势</h3>
      </div>
      <div class="card-body">
        <canvas id="dailyStatsChart" style="height: 300px;"></canvas>
      </div>
    </div>

    <!-- Error Statistics -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">错误统计</h3>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>接口路径</th>
                <th>错误次数</th>
                <th>错误率</th>
                <th>趋势</th>
              </tr>
            </thead>
            <tbody>
              <% stats.errorsByEndpoint.forEach(error => { %>
                <tr>
                  <td><code><%= error.endpoint %></code></td>
                  <td><%= error.errors %></td>
                  <td><%= error.rate %>%</td>
                  <td>
                    <span class="badge badge-<%= error.rate > 0.05 ? 'danger' : 'success' %>">
                      <%= error.rate > 0.05 ? '偏高' : '正常' %>
                    </span>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Daily Statistics Chart
  const ctx = document.getElementById('dailyStatsChart').getContext('2d');
  const dailyData = <%- JSON.stringify(stats.dailyStats) %>;
  
  new Chart(ctx, {
    type: 'line',
    data: {
      labels: dailyData.map(item => item.date),
      datasets: [{
        label: 'API调用次数',
        data: dailyData.map(item => item.calls),
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.1)',
        tension: 0.1
      }, {
        label: '错误次数',
        data: dailyData.map(item => item.errors),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.1)',
        yAxisID: 'y1',
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          grid: {
            drawOnChartArea: false,
          },
        }
      }
    }
  });
});
</script>