<div class="row">
  <div class="col-12">
    <!-- System Status Overview -->
    <div class="row mb-4">
      <div class="col-lg-3 col-6">
        <div class="info-box">
          <span class="info-box-icon bg-<%= monitorData.systemStatus === 'healthy' ? 'success' : 'danger' %>">
            <i class="fas fa-<%= monitorData.systemStatus === 'healthy' ? 'heart' : 'exclamation-triangle' %>"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">系统状态</span>
            <span class="info-box-number">
              <%= monitorData.systemStatus === 'healthy' ? '健康' : '异常' %>
            </span>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="info-box">
          <span class="info-box-icon bg-info">
            <i class="fas fa-clock"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">运行时间</span>
            <span class="info-box-number"><%= monitorData.uptime %></span>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="info-box">
          <span class="info-box-icon bg-warning">
            <i class="fas fa-tachometer-alt"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">响应时间</span>
            <span class="info-box-number"><%= monitorData.apiHealth.responseTime %>ms</span>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="info-box">
          <span class="info-box-icon bg-primary">
            <i class="fas fa-exchange-alt"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">吞吐量</span>
            <span class="info-box-number"><%= monitorData.apiHealth.throughput %>/s</span>
          </div>
        </div>
      </div>
    </div>

    <!-- API Endpoints Status -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">API端点监控</h3>
        <div class="card-tools">
          <button type="button" class="btn btn-sm btn-primary" onclick="refreshMonitoring()">
            <i class="fas fa-sync-alt"></i> 刷新
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>端点</th>
                <th>状态</th>
                <th>响应时间</th>
                <th>可用性</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <% monitorData.endpoints.forEach(endpoint => { %>
                <tr>
                  <td><code><%= endpoint.name %></code></td>
                  <td>
                    <% if (endpoint.status === 'healthy') { %>
                      <span class="badge badge-success">健康</span>
                    <% } else if (endpoint.status === 'warning') { %>
                      <span class="badge badge-warning">警告</span>
                    <% } else { %>
                      <span class="badge badge-danger">异常</span>
                    <% } %>
                  </td>
                  <td>
                    <span class="badge badge-<%= endpoint.responseTime > 200 ? 'danger' : endpoint.responseTime > 150 ? 'warning' : 'success' %>">
                      <%= endpoint.responseTime %>ms
                    </span>
                  </td>
                  <td>
                    <div class="progress progress-sm">
                      <div class="progress-bar bg-<%= endpoint.uptime >= 99.5 ? 'success' : endpoint.uptime >= 99 ? 'warning' : 'danger' %>" 
                           style="width: <%= endpoint.uptime %>%"></div>
                    </div>
                    <%= endpoint.uptime %>%
                  </td>
                  <td>
                    <button class="btn btn-sm btn-outline-info" onclick="viewEndpointDetails('<%= endpoint.name %>')">
                      <i class="fas fa-eye"></i> 详情
                    </button>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- System Components -->
    <div class="row mt-4">
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">数据库</h3>
          </div>
          <div class="card-body">
            <div class="info-box-content">
              <div class="row">
                <div class="col-6">
                  <small class="text-muted">状态</small>
                  <p><span class="badge badge-<%= monitorData.database.status === 'connected' ? 'success' : 'danger' %>">
                    <%= monitorData.database.status === 'connected' ? '已连接' : '断开' %>
                  </span></p>
                </div>
                <div class="col-6">
                  <small class="text-muted">连接池</small>
                  <p><%= monitorData.database.connectionPool %>/<%= monitorData.database.maxConnections %></p>
                </div>
              </div>
              <div class="row">
                <div class="col-12">
                  <small class="text-muted">平均查询时间</small>
                  <p><%= monitorData.database.avgQueryTime %>ms</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">缓存</h3>
          </div>
          <div class="card-body">
            <div class="info-box-content">
              <div class="row">
                <div class="col-6">
                  <small class="text-muted">状态</small>
                  <p><span class="badge badge-<%= monitorData.cache.status === 'connected' ? 'success' : 'danger' %>">
                    <%= monitorData.cache.status === 'connected' ? '已连接' : '断开' %>
                  </span></p>
                </div>
                <div class="col-6">
                  <small class="text-muted">命中率</small>
                  <p><%= monitorData.cache.hitRate %>%</p>
                </div>
              </div>
              <div class="row">
                <div class="col-12">
                  <small class="text-muted">内存使用</small>
                  <div class="progress progress-sm">
                    <div class="progress-bar bg-info" style="width: <%= monitorData.cache.memoryUsage %>%"></div>
                  </div>
                  <p class="mt-1"><%= monitorData.cache.memoryUsage %>%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">系统警报</h3>
          </div>
          <div class="card-body">
            <% if (monitorData.alerts.length > 0) { %>
              <% monitorData.alerts.forEach(alert => { %>
                <div class="alert alert-<%= alert.level === 'warning' ? 'warning' : alert.level === 'error' ? 'danger' : 'info' %> alert-dismissible">
                  <h6><i class="icon fas fa-<%= alert.level === 'warning' ? 'exclamation-triangle' : alert.level === 'error' ? 'times-circle' : 'info-circle' %>"></i> 
                    <%= alert.level === 'warning' ? '警告' : alert.level === 'error' ? '错误' : '信息' %>
                  </h6>
                  <%= alert.message %>
                  <% if (alert.endpoint) { %>
                    <br><small>端点: <%= alert.endpoint %></small>
                  <% } %>
                  <br><small class="text-muted">时间: <%= new Date(alert.timestamp).toLocaleString() %></small>
                </div>
              <% }) %>
            <% } else { %>
              <p class="text-muted">暂无警报信息</p>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function refreshMonitoring() {
  location.reload();
}

function viewEndpointDetails(endpoint) {
  // TODO: Implement endpoint details modal
  alert('查看端点详情: ' + endpoint);
}

// Auto refresh every 30 seconds
setInterval(function() {
  refreshMonitoring();
}, 30000);
</script>