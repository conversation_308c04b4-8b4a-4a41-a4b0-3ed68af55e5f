<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .tenant-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
            transition: transform 0.2s;
        }
        .tenant-card:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.8em;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-building text-primary"></i> 平台租户管理</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <% breadcrumb.forEach((item, index) => { %>
                            <% if (index === breadcrumb.length - 1) { %>
                                <li class="breadcrumb-item active"><%= item.name %></li>
                            <% } else { %>
                                <li class="breadcrumb-item"><a href="<%= item.url %>"><%= item.name %></a></li>
                            <% } %>
                        <% }); %>
                    </ol>
                </nav>
            </div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTenantModal">
                <i class="fas fa-plus"></i> 新增租户
            </button>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><%= stats.total_tenants %></h3>
                            <p class="mb-0">总租户数</p>
                        </div>
                        <i class="fas fa-building fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><%= stats.active_tenants %></h3>
                            <p class="mb-0">活跃租户</p>
                        </div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3><%= stats.total_users %></h3>
                            <p class="mb-0">总用户数</p>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3>¥<%= (stats.total_revenue || 0).toLocaleString() %></h3>
                            <p class="mb-0">总收入</p>
                        </div>
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 租户列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> 租户列表
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <% if (tenants && tenants.length > 0) { %>
                        <% tenants.forEach(tenant => { %>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card tenant-card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h6 class="card-title mb-0">
                                                <%= tenant.display_name || tenant.name %>
                                            </h6>
                                            <span class="badge status-badge bg-<%= tenant.status === 'active' ? 'success' : 'secondary' %>">
                                                <%= tenant.status === 'active' ? '正常' : '暂停' %>
                                            </span>
                                        </div>
                                        
                                        <div class="mb-2">
                                            <small class="text-muted">租户代码:</small>
                                            <span class="fw-bold"><%= tenant.tenant_code %></span>
                                        </div>
                                        
                                        <div class="mb-2">
                                            <small class="text-muted">联系人:</small>
                                            <span><%= tenant.contact_name %></span>
                                        </div>
                                        
                                        <div class="mb-2">
                                            <small class="text-muted">订阅计划:</small>
                                            <span class="badge bg-info"><%= tenant.subscription_plan %></span>
                                        </div>
                                        
                                        <div class="row text-center mt-3">
                                            <div class="col-4">
                                                <small class="text-muted d-block">用户</small>
                                                <strong><%= tenant.user_count || 0 %></strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">鹅群</small>
                                                <strong><%= tenant.flock_count || 0 %></strong>
                                            </div>
                                            <div class="col-4">
                                                <small class="text-muted d-block">收入</small>
                                                <strong>¥<%= (tenant.total_revenue || 0).toLocaleString() %></strong>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <small class="text-muted">创建时间: <%= new Date(tenant.created_at).toLocaleDateString() %></small>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <div class="btn-group w-100" role="group">
                                            <a href="/platform-tenants/<%= tenant.id %>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i> 详情
                                            </a>
                                            <button class="btn btn-outline-success btn-sm" onclick="manageTenant('<%= tenant.id %>')">
                                                <i class="fas fa-cog"></i> 管理
                                            </button>
                                            <button class="btn btn-outline-info btn-sm" onclick="viewStats('<%= tenant.id %>')">
                                                <i class="fas fa-chart-bar"></i> 统计
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    <% } else { %>
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">暂无租户数据</h5>
                                <p class="text-muted">点击右上角"新增租户"按钮创建第一个租户</p>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增租户模态框 -->
    <div class="modal fade" id="addTenantModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">新增租户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addTenantForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">租户代码 *</label>
                                    <input type="text" class="form-control" name="tenant_code" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">租户名称 *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">显示名称</label>
                                    <input type="text" class="form-control" name="display_name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">联系人 *</label>
                                    <input type="text" class="form-control" name="contact_name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">联系电话</label>
                                    <input type="tel" class="form-control" name="contact_phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">订阅计划 *</label>
                                    <select class="form-select" name="subscription_plan" required>
                                        <option value="">请选择订阅计划</option>
                                        <% plans.forEach(plan => { %>
                                            <option value="<%= plan.plan_code %>">
                                                <%= plan.display_name %> (¥<%= plan.monthly_price %>/月)
                                            </option>
                                        <% }); %>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitAddTenant()">创建租户</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function manageTenant(tenantId) {
            // 跳转到租户级管理界面
            window.location.href = '/tenant/' + tenantId + '/dashboard';
        }

        function viewStats(tenantId) {
            // 查看租户统计
            window.location.href = '/platform-tenants/' + tenantId + '/stats';
        }

        function submitAddTenant() {
            const form = document.getElementById('addTenantForm');
            const formData = new FormData(form);
            
            // TODO: 实现创建租户的AJAX请求
            alert('创建租户功能开发中...');
        }
    </script>
</body>
</html>