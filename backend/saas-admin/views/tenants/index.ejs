<div class="row">
  <div class="col-12">
    <!-- 头部统计卡片 -->
    <div class="row mb-4">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3>
              <%= tenants.length %>
            </h3>
            <p>总租户数</p>
          </div>
          <div class="icon">
            <i class="fas fa-building"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3>
              <%= tenants.filter(t=> t.status === 'active').length %>
            </h3>
            <p>活跃租户</p>
          </div>
          <div class="icon">
            <i class="fas fa-check-circle"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
          <div class="inner">
            <h3>
              <%= tenants.reduce((sum, t)=> sum + parseInt(t.user_count), 0) %>
            </h3>
            <p>总用户数</p>
          </div>
          <div class="icon">
            <i class="fas fa-users"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
          <div class="inner">
            <h3>¥<%= tenants.reduce((sum, t)=> sum + parseFloat(t.total_revenue || 0), 0).toLocaleString() %></h3>
            <p>总收入</p>
          </div>
          <div class="icon">
            <i class="fas fa-dollar-sign"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 租户列表卡片 -->
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="fas fa-building me-2"></i>
            租户管理
          </h5>
          <div class="btn-group">
            <a href="/tenants/create" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>
              创建租户
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="selectAllTenants()">
              <i class="fas fa-check-square me-1"></i>
              全选
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="batchUpdateTenantStatus('suspended')">
              <i class="fas fa-pause me-1"></i>
              批量暂停
            </button>
            <button type="button" class="btn btn-outline-success" onclick="batchUpdateTenantStatus('active')">
              <i class="fas fa-play me-1"></i>
              批量激活
            </button>
            <button type="button" class="btn btn-success" onclick="exportTenants()">
              <i class="fas fa-download me-1"></i>
              导出数据
            </button>
            <a href="/tenants/subscriptions" class="btn btn-warning">
              <i class="fas fa-calendar-check me-1"></i>
              订阅管理
            </a>
            <a href="/tenants/usage" class="btn btn-info">
              <i class="fas fa-chart-bar me-1"></i>
              使用统计
            </a>
          </div>
        </div>
      </div>
      <div class="card-body">
        <% if (tenants && tenants.length> 0) { %>
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th width="50">
                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAllTenants()">
                  </th>
                  <th>租户信息</th>
                  <th>类型</th>
                  <th>订阅计划</th>
                  <th>用户数</th>
                  <th>鹅群数</th>
                  <th>收入</th>
                  <th>状态</th>
                  <th>订阅到期</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% tenants.forEach(function(tenant) { %>
                  <tr>
                    <td>
                      <input type="checkbox" class="tenant-checkbox" value="<%= tenant.id %>">
                    </td>
                    <td>
                      <div>
                        <strong>
                          <%= tenant.tenant_name %>
                        </strong>
                        <br>
                        <small class="text-muted">
                          代码: <%= tenant.tenant_code %>
                            <br>
                            联系人: <%= tenant.contact_person %>
                              <br>
                              电话: <%= tenant.contact_phone %>
                        </small>
                      </div>
                    </td>
                    <td>
                      <% if (tenant.tenant_type==='individual' ) { %>
                        <span class="badge bg-primary">个人</span>
                        <% } else if (tenant.tenant_type==='enterprise' ) { %>
                          <span class="badge bg-success">企业</span>
                          <% } else if (tenant.tenant_type==='cooperative' ) { %>
                            <span class="badge bg-info">合作社</span>
                            <% } %>
                    </td>
                    <td>
                      <% if (tenant.subscription_plan==='basic' ) { %>
                        <span class="badge bg-secondary">基础版</span>
                        <% } else if (tenant.subscription_plan==='standard' ) { %>
                          <span class="badge bg-primary">标准版</span>
                          <% } else if (tenant.subscription_plan==='premium' ) { %>
                            <span class="badge bg-warning">高级版</span>
                            <% } else if (tenant.subscription_plan==='enterprise' ) { %>
                              <span class="badge bg-success">企业版</span>
                              <% } %>
                    </td>
                    <td>
                      <span class="badge bg-info">
                        <%= tenant.user_count %>
                      </span>
                      <small class="text-muted">/ <%= tenant.max_users %></small>
                    </td>
                    <td>
                      <span class="badge bg-warning">
                        <%= tenant.flock_count %>
                      </span>
                      <small class="text-muted">/ <%= tenant.max_flocks %></small>
                    </td>
                    <td>
                      <strong>¥<%= (parseFloat(tenant.total_revenue) || 0).toLocaleString() %></strong>
                    </td>
                    <td>
                      <% if (tenant.status==='active' ) { %>
                        <span class="badge bg-success">活跃</span>
                        <% } else if (tenant.status==='suspended' ) { %>
                          <span class="badge bg-warning">暂停</span>
                          <% } else { %>
                            <span class="badge bg-danger">停用</span>
                            <% } %>
                    </td>
                    <td>
                      <% if (tenant.subscription_end) { %>
                        <%= new Date(tenant.subscription_end).toLocaleDateString() %>
                          <% const today=new Date(); const endDate=new Date(tenant.subscription_end); const
                            diffTime=endDate.getTime() - today.getTime(); const diffDays=Math.ceil(diffTime / (1000 * 60
                            * 60 * 24)); %>
                            <br>
                            <% if (diffDays < 0) { %>
                              <small class="text-danger">已过期 <%= Math.abs(diffDays) %> 天</small>
                              <% } else if (diffDays <=30) { %>
                                <small class="text-warning">还剩 <%= diffDays %> 天</small>
                                <% } else { %>
                                  <small class="text-success">还剩 <%= diffDays %> 天</small>
                                  <% } %>
                                    <% } else { %>
                                      <span class="text-muted">无期限</span>
                                      <% } %>
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <a href="/tenants/<%= tenant.id %>/details" class="btn btn-sm btn-info" title="查看详情">
                          <i class="fas fa-eye"></i>
                        </a>
                        <a href="/tenants/<%= tenant.id %>/edit" class="btn btn-sm btn-warning" title="编辑">
                          <i class="fas fa-edit"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                  <% }); %>
              </tbody>
            </table>
          </div>
          <% } else { %>
            <div class="text-center py-5">
              <i class="fas fa-building fa-4x text-muted mb-3"></i>
              <h4>暂无租户</h4>
              <p class="text-muted">还没有创建任何租户</p>
              <a href="/tenants/create" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                创建第一个租户
              </a>
            </div>
            <% } %>
      </div>
    </div>
  </div>
</div>

<!-- 成功提示 -->
<% if (typeof success !=='undefined' ) { %>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      let message = '';
            <% if (success === 'created') { %>
        message = '租户创建成功！';
            <% } else if (success === 'updated') { %>
        message = '租户更新成功！';
            <% } %>
            
            if (message) {
        // 创建成功提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-check-circle me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;
        document.body.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', function () {
          document.body.removeChild(toast);
        });
      }
    });
  </script>
  <% } %>

    <style>
      /* 按钮加载状态样式 */
      .btn.loading {
        position: relative;
        color: transparent !important;
      }

      .btn.loading::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid transparent;
        border-top-color: currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }

      /* 改善toast样式 */
      .toast {
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      /* 表格行悬停效果 */
      .table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
      }

      /* 选中行样式 */
      .table tbody tr.selected {
        background-color: rgba(0, 123, 255, 0.1);
      }
    </style>

    <script>
      // 批量操作功能 - 确保函数在全局作用域中

      // 全选/取消全选
      window.toggleSelectAllTenants = function () {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const tenantCheckboxes = document.querySelectorAll('.tenant-checkbox');

        tenantCheckboxes.forEach(checkbox => {
          checkbox.checked = selectAllCheckbox.checked;
          // 更新行的选中状态样式
          const row = checkbox.closest('tr');
          if (checkbox.checked) {
            row.classList.add('selected');
          } else {
            row.classList.remove('selected');
          }
        });

        // 更新批量操作按钮状态
        updateBatchButtonsState();
      }

      // 选择所有租户
      window.selectAllTenants = function () {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        selectAllCheckbox.checked = true;
        window.toggleSelectAllTenants();
      }

      // 获取选中的租户ID
      window.getSelectedTenantIds = function () {
        const checkedBoxes = document.querySelectorAll('.tenant-checkbox:checked');
        return Array.from(checkedBoxes).map(checkbox => checkbox.value);
      }

      // 批量更新租户状态
      window.batchUpdateTenantStatus = function (status) {
        const selectedIds = window.getSelectedTenantIds();

        if (selectedIds.length === 0) {
          showToast('请先选择要操作的租户', 'warning');
          return;
        }

        const statusText = status === 'active' ? '激活' : status === 'suspended' ? '暂停' : '更新';

        // 显示确认对话框
        const confirmModal = createConfirmModal(
          `批量${statusText}租户`,
          `确定要批量${statusText}选中的 ${selectedIds.length} 个租户吗？此操作不可撤销。`,
          () => performBatchUpdate(status, selectedIds, statusText)
        );
        confirmModal.show();
      }

      // 执行批量更新
      function performBatchUpdate(status, selectedIds, statusText) {
        // 禁用相关按钮
        const batchButtons = document.querySelectorAll('[onclick*="batchUpdateTenantStatus"]');
        batchButtons.forEach(btn => {
          btn.disabled = true;
          btn.classList.add('loading');
        });

        // 显示加载状态
        showToast(`正在批量${statusText}租户...`, 'info');

        // 调用真实的API接口
        fetch('/tenants/api/batch-update-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tenantIds: selectedIds,
            status: status
          })
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showToast(data.message, 'success');
              // 刷新页面显示更新后的状态
              setTimeout(() => {
                location.reload();
              }, 1500);
            } else {
              showToast(data.message || '操作失败', 'error');
            }
          })
          .catch(error => {
            console.error('批量更新失败:', error);
            showToast('网络错误，请稍后重试', 'error');
          })
          .finally(() => {
            // 恢复按钮状态
            const batchButtons = document.querySelectorAll('[onclick*="batchUpdateTenantStatus"]');
            batchButtons.forEach(btn => {
              btn.disabled = false;
              btn.classList.remove('loading');
            });
          });
      }

      // 创建确认模态框
      function createConfirmModal(title, message, onConfirm) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">
                  <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                  ${title}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <p class="mb-0">${message}</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmAction()">确认</button>
              </div>
            </div>
          </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);

        // 绑定确认事件
        window.confirmAction = function () {
          onConfirm();
          bsModal.hide();
        };

        // 模态框关闭后清理
        modal.addEventListener('hidden.bs.modal', () => {
          document.body.removeChild(modal);
          delete window.confirmAction;
        });

        return bsModal;
      }

      // 更新批量操作按钮状态
      function updateBatchButtonsState() {
        const selectedCount = document.querySelectorAll('.tenant-checkbox:checked').length;
        const batchButtons = document.querySelectorAll('[onclick*="batchUpdateTenantStatus"]');

        batchButtons.forEach(btn => {
          if (selectedCount > 0) {
            btn.disabled = false;
            btn.classList.remove('disabled');
          } else {
            btn.disabled = true;
            btn.classList.add('disabled');
          }
        });

        // 更新按钮文本显示选中数量
        if (selectedCount > 0) {
          batchButtons.forEach(btn => {
            const originalText = btn.getAttribute('data-original-text') || btn.textContent;
            if (!btn.getAttribute('data-original-text')) {
              btn.setAttribute('data-original-text', originalText);
            }
            btn.innerHTML = btn.innerHTML.replace(/\(\d+\)/, '') + ` (${selectedCount})`;
          });
        } else {
          batchButtons.forEach(btn => {
            const originalText = btn.getAttribute('data-original-text');
            if (originalText) {
              btn.innerHTML = originalText;
            }
          });
        }
      }

      // 初始化页面时绑定事件
      document.addEventListener('DOMContentLoaded', function () {
        // 为所有租户复选框绑定事件
        document.querySelectorAll('.tenant-checkbox').forEach(checkbox => {
          checkbox.addEventListener('change', function () {
            const row = this.closest('tr');
            if (this.checked) {
              row.classList.add('selected');
            } else {
              row.classList.remove('selected');
            }
            updateBatchButtonsState();

            // 更新全选复选框状态
            const allCheckboxes = document.querySelectorAll('.tenant-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.tenant-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');

            if (checkedCheckboxes.length === allCheckboxes.length) {
              selectAllCheckbox.checked = true;
              selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length > 0) {
              selectAllCheckbox.checked = false;
              selectAllCheckbox.indeterminate = true;
            } else {
              selectAllCheckbox.checked = false;
              selectAllCheckbox.indeterminate = false;
            }
          });
        });

        // 初始化批量操作按钮状态
        updateBatchButtonsState();
      });

      // 租户导出功能
      window.exportTenants = function () {
        // 显示导出选项模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-download me-2"></i>
            导出租户数据
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="exportForm">
            <div class="mb-3">
              <label class="form-label">导出格式</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="format" value="excel" id="formatExcel" checked>
                <label class="form-check-label" for="formatExcel">
                  <i class="fas fa-file-excel text-success me-2"></i>
                  Excel (.xlsx)
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="format" value="csv" id="formatCsv">
                <label class="form-check-label" for="formatCsv">
                  <i class="fas fa-file-csv text-info me-2"></i>
                  CSV (.csv)
                </label>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label">导出范围</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="scope" value="all" id="scopeAll" checked>
                <label class="form-check-label" for="scopeAll">
                  所有租户
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="scope" value="active" id="scopeActive">
                <label class="form-check-label" for="scopeActive">
                  仅活跃租户
                </label>
              </div>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="includeStats" checked>
                <label class="form-check-label" for="includeStats">
                  包含统计信息
                </label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-success" onclick="performExport()">
            <i class="fas fa-download me-1"></i>
            开始导出
          </button>
        </div>
      </div>
    </div>
  `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // 模态框关闭后移除DOM
        modal.addEventListener('hidden.bs.modal', () => {
          document.body.removeChild(modal);
        });
      }

      function performExport() {
        const form = document.getElementById('exportForm');
        const formData = new FormData(form);

        const exportParams = {
          format: formData.get('format'),
          scope: formData.get('scope'),
          includeStats: document.getElementById('includeStats').checked
        };

        // 显示加载状态
        const exportBtn = document.querySelector('.modal-footer .btn-success');
        const originalText = exportBtn.innerHTML;
        exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
        exportBtn.disabled = true;

        // 构建导出URL
        const params = new URLSearchParams(exportParams);
        const exportUrl = `/api/tenants/export?${params}`;

        // 执行导出
        fetch(exportUrl)
          .then(response => {
            if (!response.ok) {
              throw new Error(`导出失败: ${response.status} ${response.statusText}`);
            }
            // 检查响应类型
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              return response.json();
            } else {
              // 直接下载文件
              return response.blob().then(blob => {
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `租户数据_${new Date().toISOString().split('T')[0]}.${exportParams.format === 'excel' ? 'xlsx' : 'csv'}`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                return { success: true, message: '导出成功' };
              });
            }
          })
          .then(data => {
            if (data.success) {
              if (data.downloadUrl) {
                // 使用返回的下载链接
                const link = document.createElement('a');
                link.href = data.downloadUrl;
                link.download = data.filename || `租户数据_${new Date().toISOString().split('T')[0]}.${exportParams.format === 'excel' ? 'xlsx' : 'csv'}`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }
              showToast(data.message || '导出成功！', 'success');

              // 关闭模态框
              const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
              modal.hide();
            } else {
              throw new Error(data.message || '导出失败');
            }
          })
          .catch(error => {
            console.error('导出失败:', error);
            showToast(error.message || '导出失败，请稍后重试', 'error');
          })
          .finally(() => {
            // 恢复按钮状态
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
          });
      }

      function showToast(message, type = 'info') {
        // 定义消息类型配置
        const typeConfig = {
          success: { bg: 'success', icon: 'check-circle', closeBtn: 'btn-close-white' },
          error: { bg: 'danger', icon: 'exclamation-triangle', closeBtn: 'btn-close-white' },
          warning: { bg: 'warning', icon: 'exclamation-triangle', closeBtn: 'btn-close' },
          info: { bg: 'primary', icon: 'info-circle', closeBtn: 'btn-close-white' }
        };

        const config = typeConfig[type] || typeConfig.info;

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-bg-${config.bg} border-0 position-fixed top-0 end-0 m-3`;
        toast.style.zIndex = '9999';
        toast.innerHTML = `
          <div class="d-flex">
            <div class="toast-body">
              <i class="fas fa-${config.icon} me-2"></i>
              ${message}
            </div>
            <button type="button" class="btn-close ${config.closeBtn} me-2 m-auto" data-bs-dismiss="toast"></button>
          </div>
        `;

        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
          document.body.removeChild(toast);
        });
      }
    </script>