<%- contentFor('title') %>
  库存数据汇总

  <%- contentFor('head') %>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <%- contentFor('content') %>
      <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-boxes"></i> 库存数据汇总
          </h1>
          <div class="d-flex gap-2">
            <select class="form-select" id="timeRangeFilter" style="width: auto;">
              <option value="7">最近7天</option>
              <option value="30" selected>最近30天</option>
              <option value="90">最近90天</option>
            </select>
            <button type="button" class="btn btn-info" onclick="exportInventorySummary()">
              <i class="bi bi-download"></i> 导出汇总
            </button>
            <button type="button" class="btn btn-primary" onclick="refreshAllData()">
              <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
          </div>
        </div>

        <!-- 平台库存汇总统计卡片 -->
        <div class="row mb-4">
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">活跃租户</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeTenants">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-building text-info" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总库存品种</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalItems">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-boxes text-primary" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">库存充足</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="sufficientStock">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">库存不足</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="lowStock">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">缺货警报</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="outOfStock">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-x-circle text-danger" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">库存记录数</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalRecords">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-file-earmark-text text-secondary" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 库存列表 -->
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">库存列表</h6>
            <div class="d-flex gap-2">
              <select class="form-select form-select-sm" id="categoryFilter" style="width: auto;">
                <option value="">全部分类</option>
                <option value="feed">饲料</option>
                <option value="medicine">药品</option>
                <option value="equipment">设备</option>
                <option value="supplies">用品</option>
              </select>
              <button class="btn btn-sm btn-outline-primary" id="searchBtn">
                <i class="bi bi-search"></i> 搜索
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered" id="inventoryTable">
                <thead>
                  <tr>
                    <th>物品名称</th>
                    <th>分类</th>
                    <th>当前库存</th>
                    <th>最低库存</th>
                    <th>单位</th>
                    <th>状态</th>
                    <th>最后更新</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="inventoryTableBody">
                  <tr>
                    <td colspan="8" class="text-center text-muted py-4">
                      <i class="bi bi-inbox fa-3x mb-3 d-block"></i>
                      暂无库存记录
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加库存模态框 -->
      <div class="modal fade" id="addInventoryModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">添加库存物品</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addInventoryForm">
              <div class="modal-body">
                <div class="mb-3">
                  <label for="itemName" class="form-label">物品名称</label>
                  <input type="text" class="form-control" id="itemName" name="name" required>
                </div>
                <div class="mb-3">
                  <label for="itemCategory" class="form-label">分类</label>
                  <select class="form-select" id="itemCategory" name="category" required>
                    <option value="">请选择分类</option>
                    <option value="feed">饲料</option>
                    <option value="medicine">药品</option>
                    <option value="equipment">设备</option>
                    <option value="supplies">用品</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="currentStock" class="form-label">当前库存</label>
                  <input type="number" class="form-control" id="currentStock" name="current_stock" min="0" required>
                </div>
                <div class="mb-3">
                  <label for="minStock" class="form-label">最低库存</label>
                  <input type="number" class="form-control" id="minStock" name="min_stock" min="0" required>
                </div>
                <div class="mb-3">
                  <label for="unit" class="form-label">单位</label>
                  <input type="text" class="form-control" id="unit" name="unit" placeholder="如：kg、袋、个" required>
                </div>
                <div class="mb-3">
                  <label for="description" class="form-label">描述</label>
                  <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 入库模态框 -->
      <div class="modal fade" id="stockInModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">入库操作</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="stockInForm">
              <div class="modal-body">
                <div class="mb-3">
                  <label for="stockInItem" class="form-label">选择物品</label>
                  <select class="form-select" id="stockInItem" name="item_id" required>
                    <option value="">请选择物品</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="stockInQuantity" class="form-label">入库数量</label>
                  <input type="number" class="form-control" id="stockInQuantity" name="quantity" min="1" required>
                </div>
                <div class="mb-3">
                  <label for="stockInDate" class="form-label">入库日期</label>
                  <input type="date" class="form-control" id="stockInDate" name="date" required>
                </div>
                <div class="mb-3">
                  <label for="stockInNotes" class="form-label">备注</label>
                  <textarea class="form-control" id="stockInNotes" name="notes" rows="3"></textarea>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="submit" class="btn btn-warning">确认入库</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 出库模态框 -->
      <div class="modal fade" id="stockOutModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">出库操作</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="stockOutForm">
              <div class="modal-body">
                <div class="mb-3">
                  <label for="stockOutItem" class="form-label">选择物品</label>
                  <select class="form-select" id="stockOutItem" name="item_id" required>
                    <option value="">请选择物品</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="stockOutQuantity" class="form-label">出库数量</label>
                  <input type="number" class="form-control" id="stockOutQuantity" name="quantity" min="1" required>
                </div>
                <div class="mb-3">
                  <label for="stockOutDate" class="form-label">出库日期</label>
                  <input type="date" class="form-control" id="stockOutDate" name="date" required>
                </div>
                <div class="mb-3">
                  <label for="stockOutNotes" class="form-label">备注</label>
                  <textarea class="form-control" id="stockOutNotes" name="notes" rows="3"></textarea>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="submit" class="btn btn-danger">确认出库</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <script>
        $(document).ready(function () {
          // 设置默认日期为今天
          const today = new Date().toISOString().split('T')[0];
          $('#stockInDate, #stockOutDate').val(today);

          // 加载统计数据
          loadInventoryStats();

          // 添加库存表单提交
          $('#addInventoryForm').submit(function (e) {
            e.preventDefault();
            showAlert('库存物品添加成功', 'success');
            $('#addInventoryModal').modal('hide');
            this.reset();
            loadInventoryStats();
          });

          // 入库表单提交
          $('#stockInForm').submit(function (e) {
            e.preventDefault();
            showAlert('入库操作成功', 'success');
            $('#stockInModal').modal('hide');
            this.reset();
            $('#stockInDate').val(today);
            loadInventoryStats();
          });

          // 出库表单提交
          $('#stockOutForm').submit(function (e) {
            e.preventDefault();
            showAlert('出库操作成功', 'success');
            $('#stockOutModal').modal('hide');
            this.reset();
            $('#stockOutDate').val(today);
            loadInventoryStats();
          });
        });

        // 加载库存统计数据
        function loadInventoryStats() {
          // 模拟数据
          const stats = {
            totalItems: 25,
            sufficientStock: 18,
            lowStock: 5,
            outOfStock: 2
          };

          $('#totalItems').text(stats.totalItems);
          $('#sufficientStock').text(stats.sufficientStock);
          $('#lowStock').text(stats.lowStock);
          $('#outOfStock').text(stats.outOfStock);
        }

        // 导出库存数据
        function exportInventoryData() {
          showAlert('库存数据导出功能开发中', 'info');
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
          const alertClass = type === 'success' ? 'alert-success' :
            type === 'error' ? 'alert-danger' :
              type === 'warning' ? 'alert-warning' : 'alert-info';

          const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

          $('body').append(alert);

          setTimeout(() => {
            alert.alert('close');
          }, 3000);
        }
      </script>