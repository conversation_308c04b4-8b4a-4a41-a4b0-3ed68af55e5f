<%- contentFor('title') %>
  财务数据汇总

  <%- contentFor('head') %>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <%- contentFor('content') %>
      <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-graph-up"></i> 财务数据汇总
          </h1>
          <div class="d-flex gap-2">
            <select class="form-select" id="timeRangeFilter" style="width: auto;">
              <option value="7">最近7天</option>
              <option value="30" selected>最近30天</option>
              <option value="90">最近90天</option>
              <option value="365">最近一年</option>
            </select>
            <button type="button" class="btn btn-info" onclick="exportSummaryData()">
              <i class="bi bi-download"></i> 导出汇总
            </button>
            <button type="button" class="btn btn-primary" onclick="refreshData()">
              <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
          </div>
        </div>

        <!-- 平台财务汇总统计卡片 -->
        <div class="row mb-4">
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">活跃租户</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeTenants">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-building text-info" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">总收入</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalIncome">¥0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-arrow-up-circle text-success" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">总支出</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalExpense">¥0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-arrow-down-circle text-danger" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">平台净利润</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="platformProfit">¥0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-graph-up text-primary" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">平均利润率</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="avgProfitMargin">0%</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-percent text-warning" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-2 col-md-4 col-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">财务记录数</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalRecords">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-file-earmark-text text-secondary" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 租户财务汇总表 -->
        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">租户财务汇总</h6>
            <div class="d-flex gap-2">
              <select class="form-select form-select-sm" id="tenantFilter" style="width: auto;">
                <option value="">全部租户</option>
                <option value="active">活跃租户</option>
                <option value="suspended">暂停租户</option>
              </select>
              <select class="form-select form-select-sm" id="sortBy" style="width: auto;">
                <option value="profit_desc">按利润降序</option>
                <option value="profit_asc">按利润升序</option>
                <option value="income_desc">按收入降序</option>
                <option value="records_desc">按记录数降序</option>
              </select>
              <button class="btn btn-sm btn-outline-primary" onclick="refreshTenantData()">
                <i class="bi bi-arrow-clockwise"></i> 刷新
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered" id="tenantFinanceTable">
                <thead>
                  <tr>
                    <th>租户名称</th>
                    <th>租户状态</th>
                    <th>总收入</th>
                    <th>总支出</th>
                    <th>净利润</th>
                    <th>利润率</th>
                    <th>记录数</th>
                    <th>最后更新</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="tenantFinanceTableBody">
                  <tr>
                    <td colspan="9" class="text-center text-muted py-4">
                      <i class="bi bi-building fa-3x mb-3 d-block"></i>
                      正在加载租户财务数据...
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 财务趋势图表 -->
        <div class="row">
          <div class="col-lg-8 mb-4">
            <div class="card shadow">
              <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">平台财务趋势</h6>
              </div>
              <div class="card-body">
                <canvas id="financeTrendChart" width="400" height="200"></canvas>
              </div>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="card shadow">
              <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">收入分布</h6>
              </div>
              <div class="card-body">
                <canvas id="incomeDistributionChart" width="400" height="200"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 租户详情模态框 -->
      <div class="modal fade" id="tenantDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">租户财务详情</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div id="tenantDetailContent">
                <div class="text-center py-4">
                  <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
              <button type="button" class="btn btn-primary" onclick="viewTenantDetails()">查看租户详情</button>
            </div>
          </div>
        </div>
      </div>

      <script>
        $(document).ready(function () {
          // 初始化时间范围过滤器
          $('#timeRangeFilter').change(function () {
            const days = $(this).val();
            loadPlatformStats(days);
            loadTenantFinanceData(days);
          });

          // 初始加载数据
          loadPlatformStats(30);
          loadTenantFinanceData(30);
          initializeCharts();
        });

        // 加载平台财务统计数据
        function loadPlatformStats(days = 30) {
          // 模拟跨租户汇总数据
          const stats = {
            activeTenants: 15,
            totalIncome: 234567.89,
            totalExpense: 156789.45,
            platformProfit: 77778.44,
            avgProfitMargin: 33.2,
            totalRecords: 1247
          };

          $('#activeTenants').text(stats.activeTenants);
          $('#totalIncome').text(`¥${stats.totalIncome.toLocaleString()}`);
          $('#totalExpense').text(`¥${stats.totalExpense.toLocaleString()}`);
          $('#platformProfit').text(`¥${stats.platformProfit.toLocaleString()}`);
          $('#avgProfitMargin').text(`${stats.avgProfitMargin}%`);
          $('#totalRecords').text(stats.totalRecords);
        }

        // 加载租户财务数据
        function loadTenantFinanceData(days = 30) {
          // 模拟租户财务汇总数据
          const tenantData = [
            {
              id: 1,
              name: '绿野养鹅场',
              status: 'active',
              totalIncome: 45678.90,
              totalExpense: 32145.67,
              netProfit: 13533.23,
              profitMargin: 29.6,
              recordCount: 156,
              lastUpdate: '2024-01-15 14:30:00'
            },
            {
              id: 2,
              name: '金羽农业合作社',
              status: 'active',
              totalIncome: 67890.12,
              totalExpense: 45123.89,
              netProfit: 22766.23,
              profitMargin: 33.5,
              recordCount: 203,
              lastUpdate: '2024-01-15 16:45:00'
            },
            {
              id: 3,
              name: '山水鹅业',
              status: 'suspended',
              totalIncome: 23456.78,
              totalExpense: 18765.43,
              netProfit: 4691.35,
              profitMargin: 20.0,
              recordCount: 89,
              lastUpdate: '2024-01-10 09:15:00'
            }
          ];

          const tbody = $('#tenantFinanceTableBody');
          tbody.empty();

          if (tenantData.length === 0) {
            tbody.append(`
            <tr>
                <td colspan="9" class="text-center text-muted py-4">
                    <i class="bi bi-building fa-3x mb-3 d-block"></i>
                    暂无租户财务数据
                </td>
            </tr>
        `);
            return;
          }

          tenantData.forEach(tenant => {
            const statusBadge = tenant.status === 'active' ?
              '<span class="badge bg-success">活跃</span>' :
              '<span class="badge bg-warning">暂停</span>';

            const profitClass = tenant.netProfit > 0 ? 'text-success' : 'text-danger';

            tbody.append(`
            <tr>
                <td><strong>${tenant.name}</strong></td>
                <td>${statusBadge}</td>
                <td class="text-success">¥${tenant.totalIncome.toLocaleString()}</td>
                <td class="text-danger">¥${tenant.totalExpense.toLocaleString()}</td>
                <td class="${profitClass}">¥${tenant.netProfit.toLocaleString()}</td>
                <td>${tenant.profitMargin}%</td>
                <td>${tenant.recordCount}</td>
                <td>${tenant.lastUpdate}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary"
                                onclick="viewTenantFinanceDetail(${tenant.id})">
                            <i class="bi bi-eye"></i>
                        </button>
                        <a href="/saas-admin/tenants/${tenant.id}" class="btn btn-sm btn-outline-info">
                            <i class="bi bi-gear"></i>
                        </a>
                    </div>
                </td>
            </tr>
        `);
          });
        }

        // 查看租户财务详情
        function viewTenantFinanceDetail(tenantId) {
          $('#tenantDetailModal').modal('show');
          // 这里可以加载具体的租户财务详情
        }

        // 刷新租户数据
        function refreshTenantData() {
          const days = $('#timeRangeFilter').val();
          loadTenantFinanceData(days);
          showAlert('数据已刷新', 'success');
        }

        // 刷新所有数据
        function refreshData() {
          const days = $('#timeRangeFilter').val();
          loadPlatformStats(days);
          loadTenantFinanceData(days);
          showAlert('所有数据已刷新', 'success');
        }

        // 导出汇总数据
        function exportSummaryData() {
          showAlert('财务汇总数据导出功能开发中', 'info');
        }

        // 初始化图表
        function initializeCharts() {
          // 财务趋势图表
          const trendCtx = document.getElementById('financeTrendChart').getContext('2d');
          new Chart(trendCtx, {
            type: 'line',
            data: {
              labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
              datasets: [{
                label: '总收入',
                data: [120000, 135000, 148000, 162000, 175000, 189000],
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
              }, {
                label: '总支出',
                data: [85000, 92000, 98000, 105000, 112000, 118000],
                borderColor: 'rgb(255, 99, 132)',
                tension: 0.1
              }]
            },
            options: {
              responsive: true,
              scales: {
                y: {
                  beginAtZero: true
                }
              }
            }
          });

          // 收入分布图表
          const distributionCtx = document.getElementById('incomeDistributionChart').getContext('2d');
          new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
              labels: ['鹅蛋销售', '鹅肉销售', '鹅毛销售', '其他收入'],
              datasets: [{
                data: [45, 30, 15, 10],
                backgroundColor: [
                  'rgb(255, 99, 132)',
                  'rgb(54, 162, 235)',
                  'rgb(255, 205, 86)',
                  'rgb(75, 192, 192)'
                ]
              }]
            },
            options: {
              responsive: true
            }
          });
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
          const alertClass = type === 'success' ? 'alert-success' :
            type === 'error' ? 'alert-danger' :
              type === 'warning' ? 'alert-warning' : 'alert-info';

          const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

          $('body').append(alert);

          setTimeout(() => {
            alert.alert('close');
          }, 3000);
        }
      </script>