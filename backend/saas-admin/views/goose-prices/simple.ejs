<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h3><i class="fas fa-chart-line"></i> 今日鹅价管理</h3>
                    </div>
                    <div class="card-body">
                        <!-- 价格概览 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h2>¥<%= stats.today_price %></h2>
                                        <p>今日鹅价 (元/斤)</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h2>+<%= stats.price_change %></h2>
                                        <p>较昨日变化</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h2><%= stats.total_regions %></h2>
                                        <p>覆盖地区</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h2><%= stats.total_breeds %></h2>
                                        <p>鹅品种</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 最新价格记录 -->
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list"></i> 最新价格记录</h5>
                                <button class="btn btn-warning btn-sm float-end">
                                    <i class="fas fa-plus"></i> 发布新价格
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>日期</th>
                                                <th>价格 (元/斤)</th>
                                                <th>地区</th>
                                                <th>品种</th>
                                                <th>趋势</th>
                                                <th>发布人</th>
                                                <th>备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% if (latestPrices && latestPrices.length > 0) { %>
                                                <% latestPrices.forEach(price => { %>
                                                    <tr>
                                                        <td><%= new Date(price.price_date).toLocaleDateString() %></td>
                                                        <td><strong>¥<%= price.price_per_kg %></strong></td>
                                                        <td><%= price.region %></td>
                                                        <td><%= price.breed_type %></td>
                                                        <td>
                                                            <span class="badge bg-<%= price.market_trend === 'up' ? 'success' : price.market_trend === 'down' ? 'danger' : 'secondary' %>">
                                                                <%= price.market_trend === 'up' ? '上涨' : price.market_trend === 'down' ? '下跌' : '稳定' %>
                                                            </span>
                                                        </td>
                                                        <td><%= price.publisher_name %></td>
                                                        <td><%= price.notes || '-' %></td>
                                                    </tr>
                                                <% }); %>
                                            <% } else { %>
                                                <tr>
                                                    <td colspan="7" class="text-center text-muted">暂无价格数据</td>
                                                </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 功能说明 -->
                        <div class="alert alert-info mt-4">
                            <h6><i class="fas fa-info-circle"></i> 功能说明</h6>
                            <ul class="mb-0">
                                <li><strong>价格发布</strong>: 管理员可以发布最新的鹅价信息</li>
                                <li><strong>趋势分析</strong>: 系统自动分析价格变化趋势</li>
                                <li><strong>区域管理</strong>: 支持不同地区的价格差异化管理</li>
                                <li><strong>品种分类</strong>: 按鹅的品种进行价格分类统计</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>