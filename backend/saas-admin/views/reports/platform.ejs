<div class="row">
  <div class="col-12">
    <!-- Platform Overview Cards -->
    <div class="row mb-4">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3><%= platformStats.totalTenants %></h3>
            <p>总租户数</p>
          </div>
          <div class="icon">
            <i class="fas fa-building"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3><%= platformStats.activeTenants %></h3>
            <p>活跃租户</p>
          </div>
          <div class="icon">
            <i class="fas fa-users"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
          <div class="inner">
            <h3><%= platformStats.totalUsers.toLocaleString() %></h3>
            <p>总用户数</p>
          </div>
          <div class="icon">
            <i class="fas fa-user-friends"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-primary">
          <div class="inner">
            <h3><%= (platformStats.totalApiCalls / 1000000).toFixed(1) %>M</h3>
            <p>API调用总数</p>
          </div>
          <div class="icon">
            <i class="fas fa-chart-line"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Growth Statistics -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">租户增长</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-6">
                <div class="description-block border-right">
                  <div class="description-percentage text-success">
                    <i class="fas fa-caret-up"></i> <%= platformStats.tenantGrowth.growthRate %>%
                  </div>
                  <h5 class="description-header"><%= platformStats.tenantGrowth.thisMonth %></h5>
                  <span class="description-text">本月新增</span>
                </div>
              </div>
              <div class="col-6">
                <div class="description-block">
                  <div class="description-percentage text-muted">
                    上月: <%= platformStats.tenantGrowth.lastMonth %>
                  </div>
                  <h5 class="description-header"><%= platformStats.systemUptime %>%</h5>
                  <span class="description-text">系统可用性</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">用户增长</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-6">
                <div class="description-block border-right">
                  <div class="description-percentage text-success">
                    <i class="fas fa-caret-up"></i> <%= platformStats.userGrowth.growthRate %>%
                  </div>
                  <h5 class="description-header"><%= platformStats.userGrowth.thisMonth %></h5>
                  <span class="description-text">本月新增</span>
                </div>
              </div>
              <div class="col-6">
                <div class="description-block">
                  <div class="description-percentage text-muted">
                    上月: <%= platformStats.userGrowth.lastMonth %>
                  </div>
                  <h5 class="description-header"><%= platformStats.avgResponseTime %>ms</h5>
                  <span class="description-text">平均响应时间</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Tenants -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">热门租户</h3>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>租户名称</th>
                <th>用户数</th>
                <th>API调用量</th>
                <th>套餐</th>
                <th>活跃度</th>
              </tr>
            </thead>
            <tbody>
              <% platformStats.topTenants.forEach((tenant, index) => { %>
                <tr>
                  <td>
                    <span class="badge badge-<%= index === 0 ? 'warning' : index === 1 ? 'secondary' : index === 2 ? 'info' : 'light' %> mr-2">
                      <%= index + 1 %>
                    </span>
                    <%= tenant.name %>
                  </td>
                  <td><%= tenant.users %></td>
                  <td><%= tenant.apiCalls.toLocaleString() %></td>
                  <td>
                    <span class="badge badge-<%= tenant.plan === 'enterprise' ? 'danger' : tenant.plan === 'premium' ? 'warning' : tenant.plan === 'standard' ? 'success' : 'secondary' %>">
                      <%= tenant.plan === 'enterprise' ? '企业版' : tenant.plan === 'premium' ? '高级版' : tenant.plan === 'standard' ? '标准版' : '基础版' %>
                    </span>
                  </td>
                  <td>
                    <div class="progress progress-sm">
                      <div class="progress-bar bg-success" style="width: <%= Math.min((tenant.apiCalls / 150000) * 100, 100) %>%"></div>
                    </div>
                    <%= Math.round((tenant.apiCalls / 150000) * 100) %>%
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Platform Performance Metrics -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">平台性能指标</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box">
              <span class="info-box-icon bg-success">
                <i class="fas fa-server"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">系统可用性</span>
                <span class="info-box-number"><%= platformStats.systemUptime %>%</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box">
              <span class="info-box-icon bg-info">
                <i class="fas fa-clock"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">响应时间</span>
                <span class="info-box-number"><%= platformStats.avgResponseTime %>ms</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box">
              <span class="info-box-icon bg-warning">
                <i class="fas fa-exchange-alt"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">月度API调用</span>
                <span class="info-box-number"><%= (platformStats.monthlyApiCalls / 1000).toFixed(0) %>K</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box">
              <span class="info-box-icon bg-primary">
                <i class="fas fa-user-check"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">活跃用户</span>
                <span class="info-box-number"><%= platformStats.activeUsers.toLocaleString() %></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>