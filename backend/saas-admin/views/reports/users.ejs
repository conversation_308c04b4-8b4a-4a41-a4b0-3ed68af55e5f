<%- contentFor('title') %>
用户分析报告

<%- contentFor('head') %>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<%- contentFor('content') %>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-people"></i> 用户分析报告
        </h1>
        <div class="d-flex gap-2">
            <select class="form-select" id="timeRange" style="width: auto;">
                <option value="7">最近7天</option>
                <option value="30" selected>最近30天</option>
                <option value="90">最近90天</option>
                <option value="365">最近一年</option>
            </select>
            <button type="button" class="btn btn-info" onclick="exportUserReport()">
                <i class="bi bi-download"></i> 导出报告
            </button>
            <button type="button" class="btn btn-primary" onclick="refreshData()">
                <i class="bi bi-arrow-clockwise"></i> 刷新数据
            </button>
        </div>
    </div>

    <!-- 用户统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总用户数</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalUsers">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">活跃用户</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeUsers">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-person-check text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">新增用户</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="newUsers">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-person-plus text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">留存率</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="retentionRate">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-graph-up text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">用户增长趋势</h6>
                </div>
                <div class="card-body">
                    <canvas id="userGrowthChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">用户角色分布</h6>
                </div>
                <div class="card-body">
                    <canvas id="userRoleChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户行为分析 -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">用户活跃度分析</h6>
                </div>
                <div class="card-body">
                    <canvas id="userActivityChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">地区分布</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>地区</th>
                                    <th>用户数</th>
                                    <th>占比</th>
                                </tr>
                            </thead>
                            <tbody id="regionDistributionTable">
                                <!-- 地区分布数据 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户详细分析 -->
    <div class="row">
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">用户设备分析</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>移动端</span>
                            <strong id="mobileUsers">0%</strong>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-primary" id="mobileProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>桌面端</span>
                            <strong id="desktopUsers">0%</strong>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" id="desktopProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>平板端</span>
                            <strong id="tabletUsers">0%</strong>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" id="tabletProgress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">用户行为热力图</h6>
                </div>
                <div class="card-body">
                    <canvas id="userBehaviorHeatmap" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadUserStats();
    loadRegionDistribution();
    initializeCharts();
    
    $('#timeRange').change(function() {
        const days = $(this).val();
        loadUserStats(days);
        updateCharts(days);
    });
});

// 加载用户统计数据
function loadUserStats(days = 30) {
    // 模拟用户统计数据
    const stats = {
        totalUsers: 12456,
        activeUsers: 8934,
        newUsers: 567,
        retentionRate: 78.5,
        deviceStats: {
            mobile: 65,
            desktop: 28,
            tablet: 7
        }
    };
    
    $('#totalUsers').text(stats.totalUsers.toLocaleString());
    $('#activeUsers').text(stats.activeUsers.toLocaleString());
    $('#newUsers').text(stats.newUsers.toLocaleString());
    $('#retentionRate').text(`${stats.retentionRate}%`);
    
    // 更新设备统计
    $('#mobileUsers').text(`${stats.deviceStats.mobile}%`);
    $('#mobileProgress').css('width', `${stats.deviceStats.mobile}%`);
    $('#desktopUsers').text(`${stats.deviceStats.desktop}%`);
    $('#desktopProgress').css('width', `${stats.deviceStats.desktop}%`);
    $('#tabletUsers').text(`${stats.deviceStats.tablet}%`);
    $('#tabletProgress').css('width', `${stats.deviceStats.tablet}%`);
}

// 加载地区分布数据
function loadRegionDistribution() {
    const regions = [
        { name: '广东省', users: 2345, percentage: 18.8 },
        { name: '江苏省', users: 1876, percentage: 15.1 },
        { name: '浙江省', users: 1543, percentage: 12.4 },
        { name: '上海市', users: 1234, percentage: 9.9 },
        { name: '北京市', users: 1098, percentage: 8.8 },
        { name: '其他地区', users: 4360, percentage: 35.0 }
    ];
    
    const tbody = $('#regionDistributionTable');
    tbody.empty();
    
    regions.forEach(region => {
        tbody.append(`
            <tr>
                <td>${region.name}</td>
                <td>${region.users.toLocaleString()}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" style="width: ${region.percentage}%">
                            ${region.percentage}%
                        </div>
                    </div>
                </td>
            </tr>
        `);
    });
}

// 初始化图表
function initializeCharts() {
    // 用户增长趋势图
    const growthCtx = document.getElementById('userGrowthChart').getContext('2d');
    window.userGrowthChart = new Chart(growthCtx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '新增用户',
                data: [450, 520, 680, 750, 890, 1200],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '活跃用户',
                data: [3200, 3450, 3800, 4200, 4650, 5100],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 用户角色分布饼图
    const roleCtx = document.getElementById('userRoleChart').getContext('2d');
    window.userRoleChart = new Chart(roleCtx, {
        type: 'doughnut',
        data: {
            labels: ['普通用户', '管理员', '超级管理员', '访客'],
            datasets: [{
                data: [85, 10, 3, 2],
                backgroundColor: [
                    'rgb(54, 162, 235)',
                    'rgb(255, 99, 132)',
                    'rgb(255, 205, 86)',
                    'rgb(75, 192, 192)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 用户活跃度分析
    const activityCtx = document.getElementById('userActivityChart').getContext('2d');
    window.userActivityChart = new Chart(activityCtx, {
        type: 'bar',
        data: {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [{
                label: '活跃用户数',
                data: [1200, 1350, 1100, 1400, 1600, 800, 600],
                backgroundColor: 'rgba(54, 162, 235, 0.8)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 用户行为热力图（简化版）
    const heatmapCtx = document.getElementById('userBehaviorHeatmap').getContext('2d');
    window.userBehaviorChart = new Chart(heatmapCtx, {
        type: 'scatter',
        data: {
            datasets: [{
                label: '用户行为分布',
                data: generateHeatmapData(),
                backgroundColor: 'rgba(255, 99, 132, 0.6)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '使用时长(小时)'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '功能使用次数'
                    }
                }
            }
        }
    });
}

function generateHeatmapData() {
    const data = [];
    for (let i = 0; i < 50; i++) {
        data.push({
            x: Math.random() * 24,
            y: Math.random() * 100
        });
    }
    return data;
}

function updateCharts(days) {
    showAlert(`已切换到最近${days}天的数据`, 'info');
}

function exportUserReport() {
    showAlert('用户分析报告导出功能开发中', 'info');
}

function refreshData() {
    loadUserStats();
    loadRegionDistribution();
    showAlert('用户数据已刷新', 'success');
}

function showAlert(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(alert);
    
    setTimeout(() => {
        alert.alert('close');
    }, 3000);
}
</script>
