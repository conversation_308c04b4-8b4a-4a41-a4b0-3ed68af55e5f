<div class="row">
  <div class="col-12">
    <!-- Usage Overview Cards -->
    <div class="row mb-4">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3><%= (usageStats.apiUsage.totalCalls / 1000000).toFixed(1) %>M</h3>
            <p>API调用总数</p>
          </div>
          <div class="icon">
            <i class="fas fa-chart-line"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3><%= usageStats.apiUsage.usageRate %>%</h3>
            <p>API使用率</p>
          </div>
          <div class="icon">
            <i class="fas fa-percentage"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
          <div class="inner">
            <h3><%= usageStats.storageUsage.totalUsed %> GB</h3>
            <p>存储使用量</p>
          </div>
          <div class="icon">
            <i class="fas fa-hdd"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-primary">
          <div class="inner">
            <h3><%= usageStats.userActivity.dailyActiveUsers %></h3>
            <p>日活跃用户</p>
          </div>
          <div class="icon">
            <i class="fas fa-users"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- API Usage Details -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">API使用情况</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-12">
                <div class="progress mb-3">
                  <div class="progress-bar bg-<%= usageStats.apiUsage.usageRate > 80 ? 'danger' : usageStats.apiUsage.usageRate > 60 ? 'warning' : 'success' %>" 
                       style="width: <%= usageStats.apiUsage.usageRate %>%">
                    <%= usageStats.apiUsage.usageRate %>%
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6">
                <div class="description-block border-right">
                  <h5 class="description-header"><%= usageStats.apiUsage.totalCalls.toLocaleString() %></h5>
                  <span class="description-text">总调用数</span>
                </div>
              </div>
              <div class="col-6">
                <div class="description-block">
                  <h5 class="description-header"><%= usageStats.apiUsage.monthlyLimit.toLocaleString() %></h5>
                  <span class="description-text">月度限制</span>
                </div>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-12">
                <small class="text-muted">
                  平均每日调用: <%= usageStats.apiUsage.avgCallsPerDay.toLocaleString() %>
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">存储使用情况</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-12">
                <div class="progress mb-3">
                  <div class="progress-bar bg-<%= usageStats.storageUsage.usageRate > 80 ? 'danger' : usageStats.storageUsage.usageRate > 60 ? 'warning' : 'info' %>" 
                       style="width: <%= usageStats.storageUsage.usageRate %>%">
                    <%= usageStats.storageUsage.usageRate %>%
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-6">
                <div class="description-block border-right">
                  <h5 class="description-header"><%= usageStats.storageUsage.totalUsed %> GB</h5>
                  <span class="description-text">已使用</span>
                </div>
              </div>
              <div class="col-6">
                <div class="description-block">
                  <h5 class="description-header"><%= usageStats.storageUsage.totalLimit %> GB</h5>
                  <span class="description-text">总限制</span>
                </div>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-12">
                <small class="text-muted">
                  月增长: <%= usageStats.storageUsage.avgGrowthPerMonth %> GB
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Activity -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">用户活跃度</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box">
              <span class="info-box-icon bg-info">
                <i class="fas fa-calendar-day"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">日活跃</span>
                <span class="info-box-number"><%= usageStats.userActivity.dailyActiveUsers %></span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box">
              <span class="info-box-icon bg-success">
                <i class="fas fa-calendar-week"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">周活跃</span>
                <span class="info-box-number"><%= usageStats.userActivity.weeklyActiveUsers %></span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box">
              <span class="info-box-icon bg-warning">
                <i class="fas fa-calendar-alt"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">月活跃</span>
                <span class="info-box-number"><%= usageStats.userActivity.monthlyActiveUsers %></span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box">
              <span class="info-box-icon bg-primary">
                <i class="fas fa-clock"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">平均会话</span>
                <span class="info-box-number"><%= usageStats.userActivity.avgSessionDuration %>分钟</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Usage -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">功能使用统计</h3>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>功能</th>
                <th>使用率</th>
                <th>趋势</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <% usageStats.featureUsage.forEach(feature => { %>
                <tr>
                  <td><%= feature.feature %></td>
                  <td>
                    <div class="progress progress-sm">
                      <div class="progress-bar bg-<%= feature.usage > 80 ? 'success' : feature.usage > 60 ? 'info' : feature.usage > 40 ? 'warning' : 'danger' %>" 
                           style="width: <%= feature.usage %>%"></div>
                    </div>
                    <%= feature.usage %>%
                  </td>
                  <td>
                    <i class="fas fa-arrow-<%= feature.usage > 70 ? 'up text-success' : feature.usage > 50 ? 'right text-info' : 'down text-danger' %>"></i>
                    <small class="text-<%= feature.usage > 70 ? 'success' : feature.usage > 50 ? 'info' : 'danger' %>">
                      <%= feature.usage > 70 ? '+' + (Math.random() * 10).toFixed(1) : feature.usage > 50 ? (Math.random() * 5).toFixed(1) : '-' + (Math.random() * 5).toFixed(1) %>%
                    </small>
                  </td>
                  <td>
                    <span class="badge badge-<%= feature.usage > 80 ? 'success' : feature.usage > 60 ? 'info' : feature.usage > 40 ? 'warning' : 'secondary' %>">
                      <%= feature.usage > 80 ? '热门' : feature.usage > 60 ? '常用' : feature.usage > 40 ? '一般' : '待推广' %>
                    </span>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Peak Usage Hours -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">高峰使用时段</h3>
      </div>
      <div class="card-body">
        <canvas id="peakUsageChart" style="height: 250px;"></canvas>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Peak Usage Chart
  const ctx = document.getElementById('peakUsageChart').getContext('2d');
  const peakData = <%- JSON.stringify(usageStats.peakUsageHours) %>;
  
  new Chart(ctx, {
    type: 'bar',
    data: {
      labels: peakData.map(item => item.hour),
      datasets: [{
        label: 'API调用次数',
        data: peakData.map(item => item.calls),
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
        borderColor: 'rgb(54, 162, 235)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return value.toLocaleString();
            }
          }
        }
      }
    }
  });
});
</script>