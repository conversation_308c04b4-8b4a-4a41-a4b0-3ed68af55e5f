<div class="row">
  <div class="col-12">
    <!-- Revenue Overview Cards -->
    <div class="row mb-4">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3>¥<%= revenueStats.totalRevenue.toLocaleString() %></h3>
            <p>总收入</p>
          </div>
          <div class="icon">
            <i class="fas fa-dollar-sign"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3>¥<%= revenueStats.monthlyRevenue.toLocaleString() %></h3>
            <p>本月收入</p>
          </div>
          <div class="icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
          <div class="inner">
            <h3>¥<%= revenueStats.yearlyRevenue.toLocaleString() %></h3>
            <p>年度收入</p>
          </div>
          <div class="icon">
            <i class="fas fa-chart-line"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="small-box bg-primary">
          <div class="inner">
            <h3><%= revenueStats.growth.monthly %>%</h3>
            <p>月增长率</p>
          </div>
          <div class="icon">
            <i class="fas fa-percentage"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Revenue by Plan -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">套餐收入分布</h3>
          </div>
          <div class="card-body">
            <canvas id="planRevenueChart" style="height: 250px;"></canvas>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">订阅统计</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-6">
                <div class="description-block border-right">
                  <div class="description-percentage text-success">
                    <i class="fas fa-plus"></i> <%= revenueStats.subscriptions.new %>
                  </div>
                  <h5 class="description-header">新订阅</h5>
                  <span class="description-text">本月</span>
                </div>
              </div>
              <div class="col-6">
                <div class="description-block">
                  <div class="description-percentage text-info">
                    <i class="fas fa-sync-alt"></i> <%= revenueStats.subscriptions.renewals %>
                  </div>
                  <h5 class="description-header">续费</h5>
                  <span class="description-text">本月</span>
                </div>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-6">
                <div class="description-block border-right">
                  <div class="description-percentage text-danger">
                    <i class="fas fa-times"></i> <%= revenueStats.subscriptions.cancellations %>
                  </div>
                  <h5 class="description-header">取消</h5>
                  <span class="description-text">本月</span>
                </div>
              </div>
              <div class="col-6">
                <div class="description-block">
                  <div class="description-percentage text-<%= revenueStats.subscriptions.churnRate < 5 ? 'success' : 'warning' %>">
                    <%= revenueStats.subscriptions.churnRate %>%
                  </div>
                  <h5 class="description-header">流失率</h5>
                  <span class="description-text">本月</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Monthly Revenue Trend -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">月度收入趋势</h3>
      </div>
      <div class="card-body">
        <canvas id="monthlyRevenueChart" style="height: 300px;"></canvas>
      </div>
    </div>

    <!-- Revenue by Plan Details -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">套餐收入详情</h3>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>套餐</th>
                <th>收入金额</th>
                <th>收入占比</th>
                <th>趋势</th>
              </tr>
            </thead>
            <tbody>
              <% Object.entries(revenueStats.byPlan).forEach(([plan, amount]) => { %>
                <tr>
                  <td>
                    <span class="badge badge-<%= plan === 'enterprise' ? 'danger' : plan === 'premium' ? 'warning' : plan === 'standard' ? 'success' : plan === 'basic' ? 'info' : 'secondary' %>">
                      <%= plan === 'trial' ? '试用版' : plan === 'basic' ? '基础版' : plan === 'standard' ? '标准版' : plan === 'premium' ? '高级版' : '企业版' %>
                    </span>
                  </td>
                  <td>¥<%= amount.toLocaleString() %></td>
                  <td>
                    <div class="progress progress-sm">
                      <div class="progress-bar bg-<%= plan === 'enterprise' ? 'danger' : plan === 'premium' ? 'warning' : plan === 'standard' ? 'success' : plan === 'basic' ? 'info' : 'secondary' %>" 
                           style="width: <%= (amount / revenueStats.totalRevenue * 100) %>%"></div>
                    </div>
                    <%= Math.round(amount / revenueStats.totalRevenue * 100) %>%
                  </td>
                  <td>
                    <i class="fas fa-arrow-up text-success"></i>
                    <small class="text-success">+8.2%</small>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Revenue Forecast -->
    <div class="card mt-4">
      <div class="card-header">
        <h3 class="card-title">收入预测</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="info-box">
              <span class="info-box-icon bg-info">
                <i class="fas fa-calendar-plus"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">下月预测</span>
                <span class="info-box-number">¥<%= revenueStats.forecast.nextMonth.toLocaleString() %></span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-box">
              <span class="info-box-icon bg-success">
                <i class="fas fa-calendar-week"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">下季预测</span>
                <span class="info-box-number">¥<%= revenueStats.forecast.nextQuarter.toLocaleString() %></span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-box">
              <span class="info-box-icon bg-warning">
                <i class="fas fa-chart-bar"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">预测置信度</span>
                <span class="info-box-number"><%= revenueStats.forecast.confidence %>%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Plan Revenue Chart
  const planCtx = document.getElementById('planRevenueChart').getContext('2d');
  const planData = <%- JSON.stringify(revenueStats.byPlan) %>;
  
  new Chart(planCtx, {
    type: 'doughnut',
    data: {
      labels: Object.keys(planData).map(plan => {
        return plan === 'trial' ? '试用版' : plan === 'basic' ? '基础版' : plan === 'standard' ? '标准版' : plan === 'premium' ? '高级版' : '企业版';
      }),
      datasets: [{
        data: Object.values(planData),
        backgroundColor: [
          '#6c757d', '#17a2b8', '#28a745', '#ffc107', '#dc3545'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  });

  // Monthly Revenue Chart
  const monthlyCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
  const monthlyData = <%- JSON.stringify(revenueStats.byMonth) %>;
  
  new Chart(monthlyCtx, {
    type: 'line',
    data: {
      labels: monthlyData.map(item => item.month),
      datasets: [{
        label: '月度收入',
        data: monthlyData.map(item => item.revenue),
        borderColor: 'rgb(40, 167, 69)',
        backgroundColor: 'rgba(40, 167, 69, 0.1)',
        tension: 0.1,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return '¥' + value.toLocaleString();
            }
          }
        }
      }
    }
  });
});
</script>