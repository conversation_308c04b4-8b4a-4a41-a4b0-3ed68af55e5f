<%- contentFor('title') %>
  统计报告

  <%- contentFor('head') %>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <%- contentFor('content') %>
      <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-graph-up"></i> 统计报告
          </h1>
          <div class="d-flex gap-2">
            <select class="form-select" id="reportPeriod" style="width: auto;">
              <option value="today">今日</option>
              <option value="week">本周</option>
              <option value="month" selected>本月</option>
              <option value="quarter">本季度</option>
              <option value="year">本年</option>
            </select>
            <button type="button" class="btn btn-info" onclick="exportAllReports()">
              <i class="bi bi-download"></i> 导出报告
            </button>
            <button type="button" class="btn btn-primary" onclick="refreshReports()">
              <i class="bi bi-arrow-clockwise"></i> 刷新数据
            </button>
          </div>
        </div>

        <!-- 报告快捷入口 -->
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-left-primary shadow">
              <div class="card-body text-center">
                <i class="bi bi-building fa-3x text-primary mb-3"></i>
                <h5 class="card-title">平台统计</h5>
                <p class="card-text text-muted">租户、用户、订阅等平台数据统计</p>
                <a href="/reports/platform" class="btn btn-primary">查看报告</a>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-left-success shadow">
              <div class="card-body text-center">
                <i class="bi bi-currency-dollar fa-3x text-success mb-3"></i>
                <h5 class="card-title">收入统计</h5>
                <p class="card-text text-muted">订阅收入、商城收入等财务数据</p>
                <a href="/reports/revenue" class="btn btn-success">查看报告</a>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-left-info shadow">
              <div class="card-body text-center">
                <i class="bi bi-graph-up fa-3x text-info mb-3"></i>
                <h5 class="card-title">使用统计</h5>
                <p class="card-text text-muted">API调用、功能使用等数据分析</p>
                <a href="/reports/usage" class="btn btn-info">查看报告</a>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-left-warning shadow">
              <div class="card-body text-center">
                <i class="bi bi-people fa-3x text-warning mb-3"></i>
                <h5 class="card-title">用户分析</h5>
                <p class="card-text text-muted">用户行为、活跃度等分析报告</p>
                <a href="/reports/users" class="btn btn-warning">查看报告</a>
              </div>
            </div>
          </div>
        </div>

        <!-- 核心指标概览 -->
        <div class="row mb-4">
          <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">活跃租户</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeTenants">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">月度收入</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="monthlyRevenue">¥0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-currency-dollar text-success" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">API调用量</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="apiCalls">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-graph-up text-info" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">活跃用户</div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeUsers">0</div>
                  </div>
                  <div class="col-auto">
                    <i class="bi bi-people text-warning" style="font-size: 2rem;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="row mb-4">
          <div class="col-lg-8">
            <div class="card shadow">
              <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">平台增长趋势</h6>
              </div>
              <div class="card-body">
                <canvas id="growthTrendChart" width="400" height="200"></canvas>
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="card shadow">
              <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">租户分布</h6>
              </div>
              <div class="card-body">
                <canvas id="tenantDistributionChart" width="400" height="200"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- 最新报告列表 -->
        <div class="card shadow">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">最新生成的报告</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th>报告名称</th>
                    <th>报告类型</th>
                    <th>生成时间</th>
                    <th>数据范围</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="reportsTableBody">
                  <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                      <i class="bi bi-file-earmark-text fa-3x mb-3 d-block"></i>
                      暂无报告记录
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <script>
        $(document).ready(function () {
          loadReportStats();
          loadRecentReports();
          initializeCharts();

          $('#reportPeriod').change(function () {
            const period = $(this).val();
            loadReportStats(period);
            updateCharts(period);
          });
        });

        // 加载报告统计数据
        function loadReportStats(period = 'month') {
          // 模拟统计数据
          const stats = {
            activeTenants: 156,
            monthlyRevenue: 234567.89,
            apiCalls: 1234567,
            activeUsers: 2345
          };

          $('#activeTenants').text(stats.activeTenants);
          $('#monthlyRevenue').text(`¥${stats.monthlyRevenue.toLocaleString()}`);
          $('#apiCalls').text(stats.apiCalls.toLocaleString());
          $('#activeUsers').text(stats.activeUsers.toLocaleString());
        }

        // 加载最新报告
        function loadRecentReports() {
          const reports = [
            {
              id: 1,
              name: '月度平台统计报告',
              type: '平台统计',
              generatedAt: '2024-01-15 14:30:00',
              dataRange: '2024年1月',
              status: 'completed'
            },
            {
              id: 2,
              name: '收入分析报告',
              type: '收入统计',
              generatedAt: '2024-01-14 16:45:00',
              dataRange: '2024年Q1',
              status: 'completed'
            }
          ];

          const tbody = $('#reportsTableBody');
          tbody.empty();

          if (reports.length === 0) {
            tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="bi bi-file-earmark-text fa-3x mb-3 d-block"></i>
                    暂无报告记录
                </td>
            </tr>
        `);
            return;
          }

          reports.forEach(report => {
            const statusBadge = report.status === 'completed' ?
              '<span class="badge bg-success">已完成</span>' :
              '<span class="badge bg-warning">生成中</span>';

            tbody.append(`
            <tr>
                <td><strong>${report.name}</strong></td>
                <td>${report.type}</td>
                <td>${report.generatedAt}</td>
                <td>${report.dataRange}</td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewReport(${report.id})">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadReport(${report.id})">
                            <i class="bi bi-download"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
          });
        }

        // 初始化图表
        function initializeCharts() {
          // 平台增长趋势图
          const growthCtx = document.getElementById('growthTrendChart').getContext('2d');
          window.growthChart = new Chart(growthCtx, {
            type: 'line',
            data: {
              labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
              datasets: [{
                label: '租户数量',
                data: [50, 65, 78, 95, 120, 156],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4,
                fill: true
              }, {
                label: '活跃用户',
                data: [500, 750, 980, 1200, 1650, 2345],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.4,
                fill: true,
                yAxisID: 'y1'
              }]
            },
            options: {
              responsive: true,
              scales: {
                y: {
                  type: 'linear',
                  display: true,
                  position: 'left',
                },
                y1: {
                  type: 'linear',
                  display: true,
                  position: 'right',
                  grid: {
                    drawOnChartArea: false,
                  },
                }
              }
            }
          });

          // 租户分布饼图
          const distributionCtx = document.getElementById('tenantDistributionChart').getContext('2d');
          window.distributionChart = new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
              labels: ['试用版', '基础版', '标准版', '高级版', '企业版'],
              datasets: [{
                data: [45, 35, 25, 15, 5],
                backgroundColor: [
                  'rgb(108, 117, 125)',
                  'rgb(23, 162, 184)',
                  'rgb(40, 167, 69)',
                  'rgb(255, 193, 7)',
                  'rgb(220, 53, 69)'
                ]
              }]
            },
            options: {
              responsive: true,
              plugins: {
                legend: {
                  position: 'bottom'
                }
              }
            }
          });
        }

        function updateCharts(period) {
          // 根据时间范围更新图表数据
          showAlert(`已切换到${period === 'today' ? '今日' : period === 'week' ? '本周' : period === 'month' ? '本月' : period === 'quarter' ? '本季度' : '本年'}数据`, 'info');
        }

        function viewReport(id) {
          showAlert('查看报告功能开发中', 'info');
        }

        function downloadReport(id) {
          showAlert('下载报告功能开发中', 'info');
        }

        function exportAllReports() {
          showAlert('导出所有报告功能开发中', 'info');
        }

        function refreshReports() {
          loadReportStats();
          loadRecentReports();
          showAlert('报告数据已刷新', 'success');
        }

        function showAlert(message, type = 'info') {
          const alertClass = type === 'success' ? 'alert-success' :
            type === 'error' ? 'alert-danger' :
              type === 'warning' ? 'alert-warning' : 'alert-info';

          const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

          $('body').append(alert);

          setTimeout(() => {
            alert.alert('close');
          }, 3000);
        }
      </script>