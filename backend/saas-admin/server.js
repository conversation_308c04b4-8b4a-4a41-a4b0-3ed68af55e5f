const express = require('express');
const expressLayouts = require('express-ejs-layouts');
const session = require('express-session');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 4000;

// Database connection
const db = require('./config/database');

// Template utilities helper
const templateUtils = {
    formatDate: (dateString, format = 'YYYY-MM-DD HH:mm:ss') => {
        const date = new Date(dateString);
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    formatNumber: (num, decimals = 0) => {
        if (isNaN(num)) return '0';
        return Number(num).toLocaleString('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },
    
    formatCurrency: (amount) => {
        return '¥' + templateUtils.formatNumber(amount, 2);
    }
};

// Middleware setup
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com", "https://code.jquery.com"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com"],
            fontSrc: ["'self'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"]
        }
    }
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// CORS Configuration
app.use(cors({
    origin: process.env.NODE_ENV === 'development' ? true : process.env.CORS_ORIGIN,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || 15) * 60 * 1000,
    max: parseInt(process.env.RATE_LIMIT_MAX || 1000),
    message: { error: 'Too many requests from this IP, please try again later.' }
});
app.use('/api/', limiter);

// Session configuration
app.use(session({
    secret: process.env.SESSION_SECRET || 'smart-goose-saas-secret-key-2024',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // 开发环境中设为false，生产环境使用HTTPS时设为true
        httpOnly: true,
        maxAge: parseInt(process.env.SESSION_MAX_AGE || 86400000)
    }
}));

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(expressLayouts);
app.set('layout', 'layouts/main');

// Make utilities available in all templates
app.use((req, res, next) => {
    res.locals.Utils = templateUtils;
    // 设置默认的currentPage变量
    res.locals.currentPage = req.path.split('/')[1] || 'dashboard';
    next();
});

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Create upload directory if it doesn't exist
const uploadDir = path.join(__dirname, process.env.UPLOAD_DIR || 'uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Authentication middleware
const { authMiddleware } = require('./middleware/auth');

// Routes setup - 使用统一的SAAS管理路由
const unifiedSaasRoutes = require('./routes/unified-saas-admin');

// 设置数据库连接到app.locals，供路由使用
app.locals.db = db;

// 挂载所有SAAS管理路由到根路径
app.use('/', unifiedSaasRoutes);

// Health check endpoint is now handled by API routes

// Global error handler
app.use((err, req, res, next) => {
    console.error('Error:', err);
    
    if (req.xhr || req.headers.accept.indexOf('json') > -1) {
        res.status(500).json({
            success: false,
            message: process.env.NODE_ENV === 'development' ? err.message : 'Internal Server Error'
        });
    } else {
        res.status(500).render('error', {
            title: 'Server Error',
            message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong',
            layout: false,
            error: process.env.NODE_ENV === 'development' ? err : {}
        });
    }
});

// 404 handler
app.use('*', (req, res) => {
    if (req.xhr || (req.headers.accept && req.headers.accept.indexOf('json') > -1)) {
        res.status(404).json({
            success: false,
            message: 'API endpoint not found'
        });
    } else {
        res.status(404).render('error', {
            title: 'Page Not Found',
            message: 'The page you are looking for does not exist',
            layout: false
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Smart Goose SAAS Admin Server running on port ${PORT}`);
    console.log(`🌐 Environment: ${process.env.NODE_ENV}`);
    console.log(`🎯 Access URL: http://localhost:${PORT}`);
    
    // Test database connection
    db.testConnection()
        .then(() => console.log('✅ Database connected successfully'))
        .catch(err => console.error('❌ Database connection failed:', err.message));
});

module.exports = app;