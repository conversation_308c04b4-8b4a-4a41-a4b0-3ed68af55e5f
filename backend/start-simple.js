const express = require('express');
const cors = require('cors');
const path = require('path');

// 导入微信API响应格式工具
const { 
  wechatResponseMiddleware,
  wechatErrorHandler,
  createSuccessResponse,
  WECHAT_ERROR_CODES
} = require('./utils/wechat-response');

const app = express();
const PORT = process.env.PORT || 3001;

// 基础中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 应用微信API响应格式中间件
app.use('/api', wechatResponseMiddleware);

// 静态文件服务
app.use('/admin', express.static(path.join(__dirname, 'admin')));

// 简单的测试路由
app.get('/api/v2/test', (req, res) => {
  res.wechatSuccess({
    message: '服务器运行正常',
    timestamp: new Date().toISOString(),
    version: 'v2.0'
  });
});

// ============================================================================
// V2 标准化API端点 (符合微信小程序开发规范)
// ============================================================================

/**
 * 获取用户信息 (V2标准)
 * GET /api/v2/auth/user-info
 */
app.get('/api/v2/auth/user-info', (req, res) => {
  res.wechatSuccess({
    id: 1,
    name: '李经理',
    farmName: '智慧生态养鹅基地',
    role: 'admin',
    avatar: '/images/default_avatar.png',
    phone: '138****8888',
    email: '<EMAIL>',
    inventoryCount: 1250,
    healthRate: 95,
    environmentStatus: '优'
  });
});

/**
 * 获取首页仪表板数据 (V2标准)
 * GET /api/v2/home/<USER>
 */
app.get('/api/v2/home/<USER>', (req, res) => {
  res.wechatSuccess({
    userInfo: {
      id: 1,
      name: '李经理',
      farmName: '智慧生态养鹅基地',
      role: 'admin',
      inventoryCount: 1250,
      healthRate: '95%',
      environmentStatus: '优'
    },
    statistics: {
      totalFlocks: 12,
      totalGeese: 1250,
      todayEggs: 245,
      healthyCount: 1187,
      sickCount: 63
    },
    recentActivities: [
      {
        id: 1,
        type: 'production',
        message: '今日鹅蛋产量记录已更新',
        timestamp: new Date().toISOString()
      }
    ]
  });
});

/**
 * 获取公告列表 (V2标准)
 * GET /api/v2/home/<USER>
 */
app.get('/api/v2/home/<USER>', (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  const announcements = [
    {
      id: 1,
      title: '冬季养鹅注意事项及防疫措施',
      publishTime: '12-15',
      isImportant: true,
      content: '随着气温降低，需要加强鹅群的保暖措施...',
      createdAt: '2024-12-15T10:00:00Z'
    },
    {
      id: 2,
      title: '新品种鹅苗到货通知',
      publishTime: '12-14',
      isImportant: false,
      content: '优质白鹅苗已到货，数量有限...',
      createdAt: '2024-12-14T09:00:00Z'
    },
    {
      id: 3,
      title: '养殖技术培训通知',
      publishTime: '12-13',
      isImportant: false,
      content: '本周六将举行养殖技术培训...',
      createdAt: '2024-12-13T15:00:00Z'
    }
  ];
  
  res.wechatPaginated(
    announcements,
    {
      page: parseInt(page),
      limit: parseInt(limit),
      total: announcements.length
    }
  );
});

/**
 * 获取生产记录列表 (V2标准)
 * GET /api/v2/production/records
 */
app.get('/api/v2/production/records', (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  const records = [
    {
      id: 1,
      userId: 1,
      type: 'growth',
      batch: 'GE2023061501',
      date: '2023-06-15',
      details: {
        weight: 2.5,
        ratio: '2.8:1'
      },
      notes: '生长情况良好',
      status: 'submitted',
      createdAt: '2023-06-15T10:00:00Z',
      updatedAt: '2023-06-15T10:00:00Z'
    },
    {
      id: 2,
      userId: 1,
      type: 'weight',
      batch: 'GE2023060801',
      date: '2023-06-14',
      details: {
        count: 500,
        weight: 2.3
      },
      notes: '称重记录',
      status: 'submitted',
      createdAt: '2023-06-14T10:00:00Z',
      updatedAt: '2023-06-14T10:00:00Z'
    },
    {
      id: 3,
      userId: 1,
      type: 'sale',
      batch: 'GE2023060501',
      date: '2023-06-05',
      details: {
        count: 200,
        weight: 3.2
      },
      notes: '出栏记录',
      status: 'submitted',
      createdAt: '2023-06-05T10:00:00Z',
      updatedAt: '2023-06-05T10:00:00Z'
    }
  ];
  
  res.wechatPaginated(
    records,
    {
      page: parseInt(page),
      limit: parseInt(limit),
      total: records.length
    }
  );
});

/**
 * 创建生产记录 (V2标准)
 * POST /api/v2/production/records
 */
app.post('/api/v2/production/records', (req, res) => {
  const { type, batch, date, weight, ratio, count, notes } = req.body;

  const newRecord = {
    id: Date.now(),
    userId: 1,
    type: type,
    batch: batch,
    date: date,
    details: {},
    notes: notes || '',
    status: 'submitted',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // 根据类型设置详情
  if (type === 'growth') {
    newRecord.details = {
      weight: parseFloat(weight) || 0,
      ratio: ratio || ''
    };
  } else if (type === 'weight') {
    newRecord.details = {
      count: parseInt(count) || 0,
      weight: parseFloat(weight) || 0
    };
  } else if (type === 'sale') {
    newRecord.details = {
      count: parseInt(count) || 0,
      weight: parseFloat(weight) || 0
    };
  }

  res.wechatSuccess(newRecord, '生产记录创建成功');
});

/**
 * 获取物料列表 (V2标准)
 * GET /api/v2/materials
 */
app.get('/api/v2/materials', (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  const materials = [
    {
      id: 1,
      name: '雏鹅专用饲料',
      category: 'feed',
      spec: '25kg/袋',
      stock: 120,
      unit: '袋',
      minStock: 20,
      unitPrice: 85.0,
      supplier: '优质饲料公司',
      status: 'normal',
      createdAt: '2023-06-01T10:00:00Z',
      updatedAt: '2023-06-01T10:00:00Z'
    },
    {
      id: 2,
      name: '育肥期饲料',
      category: 'feed',
      spec: '25kg/袋',
      stock: 45,
      unit: '袋',
      minStock: 30,
      unitPrice: 78.0,
      supplier: '优质饲料公司',
      status: 'warning',
      createdAt: '2023-05-15T10:00:00Z',
      updatedAt: '2023-05-15T10:00:00Z'
    },
    {
      id: 3,
      name: '抗生素',
      category: 'medicine',
      spec: '100ml/瓶',
      stock: 15,
      unit: '瓶',
      minStock: 20,
      unitPrice: 25.0,
      supplier: '兽药公司',
      status: 'danger',
      createdAt: '2023-04-10T10:00:00Z',
      updatedAt: '2023-04-10T10:00:00Z'
    }
  ];
  
  res.wechatPaginated(
    materials,
    {
      page: parseInt(page),
      limit: parseInt(limit),
      total: materials.length
    }
  );
});

/**
 * 创建物料 (V2标准)
 * POST /api/v2/materials
 */
app.post('/api/v2/materials', (req, res) => {
  const {
    name,
    category,
    spec,
    stock,
    unit,
    minStock,
    unitPrice,
    supplier,
    description
  } = req.body;

  // 计算状态
  let status = 'normal';
  const stockNum = parseInt(stock) || 0;
  const minStockNum = parseInt(minStock) || 20;

  if (stockNum <= 0) {
    status = 'danger';
  } else if (stockNum <= minStockNum) {
    status = 'warning';
  }

  const newMaterial = {
    id: Date.now(),
    name: name,
    category: category,
    spec: spec,
    stock: stockNum,
    unit: unit || '个',
    minStock: minStockNum,
    unitPrice: parseFloat(unitPrice) || 0,
    supplier: supplier || '',
    description: description || '',
    status: status,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  res.wechatSuccess(newMaterial, '物料创建成功');
});

/**
 * 获取OA统计信息 (V2标准)
 * GET /api/v2/oa/statistics
 */
app.get('/api/v2/oa/statistics', (req, res) => {
  res.wechatSuccess({
    pendingApprovals: 5,
    completedApprovals: 28,
    urgentApprovals: 2,
    totalUsers: 15,
    activeUsers: 12,
    recentActivity: {
      approvals: 3,
      reimbursements: 2,
      reports: 1
    }
  });
});

/**
 * 获取待处理审批 (V2标准)
 * GET /api/v2/oa/approvals/pending
 */
app.get('/api/v2/oa/approvals/pending', (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  const approvals = [
    {
      id: 1,
      title: '报销申请',
      businessType: 'reimbursement',
      applicantName: 'Demo User',
      amount: 500,
      isUrgent: false,
      createdAt: new Date().toISOString(),
      status: 'pending'
    }
  ];
  
  res.wechatPaginated(
    approvals,
    {
      page: parseInt(page),
      limit: parseInt(limit),
      total: approvals.length
    }
  );
});

/**
 * 获取审批历史 (V2标准)
 * GET /api/v2/oa/approvals/history
 */
app.get('/api/v2/oa/approvals/history', (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  const history = [
    {
      id: 1,
      title: '费用报销申请',
      businessType: 'expense',
      applicantName: 'Demo User',
      status: 'approved',
      approvalTime: new Date().toISOString(),
      createdAt: new Date().toISOString()
    }
  ];
  
  res.wechatPaginated(
    history,
    {
      page: parseInt(page),
      limit: parseInt(limit),
      total: history.length
    }
  );
});

/**
 * 获取紧急审批 (V2标准)
 * GET /api/v2/oa/approvals/urgent
 */
app.get('/api/v2/oa/approvals/urgent', (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  const urgentApprovals = [
    {
      id: 1,
      title: '紧急采购申请',
      businessType: 'purchase',
      applicantName: 'Demo User',
      amount: 10000,
      isUrgent: true,
      priority: 'urgent',
      urgentReason: '设备故障需要紧急采购配件',
      createdAt: new Date().toISOString(),
      status: 'pending'
    },
    {
      id: 2,
      title: '特急报销申请',
      businessType: 'reimbursement',
      applicantName: 'Admin User',
      amount: 5000,
      isUrgent: true,
      priority: 'critical',
      urgentReason: '客户投诉紧急处理费用',
      createdAt: new Date().toISOString(),
      status: 'pending'
    }
  ];
  
  res.wechatPaginated(
    urgentApprovals,
    {
      page: parseInt(page),
      limit: parseInt(limit),
      total: urgentApprovals.length
    }
  );
});

/**
 * 获取权限用户列表 (V2标准)
 * GET /api/v2/oa/permissions/users
 */
app.get('/api/v2/oa/permissions/users', (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  
  const users = [
    {
      id: 1,
      name: 'Demo User',
      department: 'IT',
      role: 'manager',
      status: 'active',
      permissions: ['all']
    }
  ];
  
  res.wechatPaginated(
    users,
    {
      page: parseInt(page),
      limit: parseInt(limit),
      total: users.length
    }
  );
});

// ============================================================================
// V1 API兼容性支持 (向后兼容，但建议迁移到V2)
// ============================================================================

// V1用户信息接口兼容
app.get('/api/v1/auth/userinfo', (req, res) => {
  res.wechatSuccess({
    id: 1,
    name: '李经理',
    farmName: '智慧生态养鹅基地', 
    role: 'admin',
    avatar: '/images/default_avatar.png',
    phone: '138****8888',
    email: '<EMAIL>',
    inventoryCount: 1250,
    healthRate: 95,
    environmentStatus: '优'
  });
});

// V1公告接口兼容
app.get('/api/v1/home/<USER>', (req, res) => {
  const announcements = [
    {
      id: 1,
      title: '冬季养鹅注意事项及防疫措施',
      publishTime: '12-15',
      isImportant: true,
      content: '随着气温降低，需要加强鹅群的保暖措施...',
      createdAt: '2024-12-15T10:00:00Z'
    },
    {
      id: 2,
      title: '新品种鹅苗到货通知',
      publishTime: '12-14', 
      isImportant: false,
      content: '优质白鹅苗已到货，数量有限...',
      createdAt: '2024-12-14T09:00:00Z'
    },
    {
      id: 3,
      title: '养殖技术培训通知',
      publishTime: '12-13',
      isImportant: false,
      content: '本周六将举行养殖技术培训...',
      createdAt: '2024-12-13T15:00:00Z'
    }
  ];
  
  res.wechatSuccess(announcements);
});

// 应用微信错误处理中间件
app.use(wechatErrorHandler);

// 404处理 - 符合微信API规范
app.use((req, res) => {
  res.wechatError(
    WECHAT_ERROR_CODES.RESOURCE_NOT_FOUND,
    'API接口不存在',
    {
      path: req.originalUrl,
      method: req.method,
      available_versions: ['v1', 'v2'],
      recommended_version: 'v2'
    }
  );
});

// 启动服务器
app.listen(PORT, () => {
});
