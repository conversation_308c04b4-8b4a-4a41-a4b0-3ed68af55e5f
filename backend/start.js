/**
 * 智慧养鹅企业级SaaS管理系统启动脚本
 * Smart Goose Enterprise SaaS Management System Startup Script
 */

require("dotenv").config();
const path = require("path");
const express = require("express");
const http = require("http");
const { sequelize, testConnection } = require("./config/database");

// 创建API服务器
const apiApp = express();
const apiPort = process.env.PORT || 3000;

// 创建管理后台服务器
const adminApp = express();
const adminPort = process.env.ADMIN_PORT || 3001;

// 启动API服务器
const startApiServer = async () => {
  try {
    // 测试数据库连接
    await testConnection();

    // 配置API服务器
    require("./server")(apiApp);

    // 启动HTTP服务器
    const apiServer = http.createServer(apiApp);

    apiServer.listen(apiPort, () => {
      console.log(`🚀 API服务器运行在端口 ${apiPort}`);
      console.log(`📊 API文档: http://localhost:${apiPort}/api-docs`);
    });

    // 处理未捕获的异常
    process.on("uncaughtException", (error) => {
      console.error("❌ 未捕获的异常:", error);
    });

    // 处理未处理的Promise拒绝
    process.on("unhandledRejection", (reason, promise) => {
      console.error("❌ 未处理的Promise拒绝:", reason);
    });

    return apiServer;
  } catch (error) {
    console.error("❌ API服务器启动失败:", error);
    process.exit(1);
  }
};

// 启动管理后台服务器
const startAdminServer = async () => {
  try {
    // 配置管理后台服务器
    adminApp.set("views", path.join(__dirname, "admin/views"));
    adminApp.set("view engine", "ejs");
    adminApp.use(express.static(path.join(__dirname, "admin/public")));

    // 加载管理后台路由
    require("./admin/app")(adminApp);

    // 启动HTTP服务器
    const adminServer = http.createServer(adminApp);

    adminServer.listen(adminPort, () => {
      console.log(`🎛️ 管理后台运行在端口 ${adminPort}`);
      console.log(`🌐 管理界面: http://localhost:${adminPort}`);
    });

    return adminServer;
  } catch (error) {
    console.error("❌ 管理后台启动失败:", error);
    process.exit(1);
  }
};

// 主函数
const main = async () => {
  console.log('🚀 启动智慧养鹅全栈系统...');

  try {
    // 启动API服务器
    await startApiServer();

    // 启动管理后台服务器
    await startAdminServer();

    console.log('🎉 系统启动完成!');
  } catch (error) {
    console.error("❌ 系统启动失败:", error);
    process.exit(1);
  }
};

// 执行主函数
main();
