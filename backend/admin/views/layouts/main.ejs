<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %>
  </title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
  <!-- Custom CSS -->
  <style>
    .sidebar {
      background: #343a40;
      min-height: 100vh;
    }

    .sidebar .nav-link {
      color: #adb5bd;
      border-radius: 0.375rem;
      margin: 0.25rem 0;
    }

    .sidebar .nav-link:hover,
    .sidebar .nav-link.active {
      background: #495057;
      color: #fff;
    }

    .main-content {
      min-height: 100vh;
      background: #f8f9fa;
    }

    .navbar-brand {
      font-weight: 600;
    }

    .card {
      border: none;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .nav-header {
      font-weight: 600;
      color: #6c757d !important;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 1px solid #495057;
      padding-bottom: 5px;
    }
  </style>
</head>

<body>
  <div class="container-fluid">
    <div class="row">
      <!-- 侧边栏 -->
      <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
        <div class="position-sticky pt-3">
          <!-- Logo - 移除智慧养鹅字样 -->
          <div class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none">
            <span class="fs-4 px-3">
              <!-- 移除了图标和文字 -->
            </span>
          </div>

          <hr class="text-white-50">

          <!-- 导航菜单 -->
          <ul class="nav flex-column">
            <li class="nav-item">
              <div class="nav-header text-muted small mb-2 px-3">注意：这是演示版本</div>
            </li>
            <li class="nav-item">
              <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/dashboard">
                <i class="bi bi-speedometer2 me-2"></i>
                仪表板
              </a>
            </li>
            <li class="nav-item">
              <div class="nav-header text-muted small mb-2 px-3 mt-4">重定向到SaaS平台</div>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/saas-admin/dashboard" target="_blank">
                <i class="bi bi-building me-2"></i>
                SaaS平台管理
                <i class="bi bi-box-arrow-up-right ms-1 small"></i>
              </a>
            </li>
            <li class="nav-item">
              <div class="nav-header text-muted small mb-2 px-3 mt-4">工具和文档</div>
            </li>
            <li class="nav-item">
              <a class="nav-link <%= currentPage === 'api-docs' ? 'active' : '' %>" href="/api-docs">
                <i class="bi bi-book me-2"></i>
                API文档
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <%= currentPage === 'icons' ? 'active' : '' %>" href="/icons">
                <i class="bi bi-image me-2"></i>
                图标管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link <%= currentPage === 'settings' ? 'active' : '' %>" href="/settings">
                <i class="bi bi-gear me-2"></i>
                系统设置
              </a>
            </li>
          </ul>

          <hr class="text-white-50">

          <!-- 用户信息 -->
          <% if (user) { %>
            <div class="dropdown px-3">
              <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle"
                id="dropdownUser" data-bs-toggle="dropdown">
                <i class="bi bi-person-circle me-2"></i>
                <%= user.username %>
              </a>
              <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                <li><a class="dropdown-item" href="/settings">设置</a></li>
                <li>
                  <hr class="dropdown-divider">
                </li>
                <li><a class="dropdown-item" href="/logout">退出登录</a></li>
              </ul>
            </div>
            <% } %>
        </div>
      </nav>

      <!-- 主内容区 -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
        <!-- 顶部导航 -->
        <div
          class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
              <% if (currentPage && currentPage !=='dashboard' ) { %>
                <li class="breadcrumb-item active" aria-current="page">
                  <%= currentPage==='api-docs' ? 'API文档' : currentPage==='users' ? '用户管理' : currentPage==='flocks'
                    ? '鹅群管理' : currentPage==='production' ? '生产管理' : currentPage==='health' ? '健康管理' :
                    currentPage==='settings' ? '系统设置' : currentPage==='icons' ? '图标管理' : currentPage %>
                </li>
                <% } %>
            </ol>
          </nav>

          <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
              <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshPage()">
                <i class="bi bi-arrow-clockwise"></i>
                刷新
              </button>
            </div>
            <% if (user && user.role==='admin' ) { %>
              <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                  <i class="bi bi-tools"></i>
                  工具
                </button>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="/api-docs">API文档</a></li>
                  <li><a class="dropdown-item" href="/api-docs/swagger" target="_blank">Swagger UI</a></li>
                  <li>
                    <hr class="dropdown-divider">
                  </li>
                  <li><a class="dropdown-item" href="/settings/system">系统设置</a></li>
                </ul>
              </div>
              <% } %>
          </div>
        </div>

        <!-- 页面内容 -->
        <div class="content">
          <!-- 这里会被子模板替换 -->
        </div>
      </main>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

  <!-- 全局JavaScript -->
  <script>
    // 刷新页面
    function refreshPage() {
      window.location.reload();
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
      }[type] || 'alert-info';

      const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

      // 插入到页内容顶部
      const content = document.querySelector('.content');
      if (content) {
        content.insertAdjacentHTML('afterbegin', alertHtml);
      }
    }

    // AJAX请求助手
    function apiRequest(url, options = {}) {
      const defaultOptions = {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      };

      return fetch(url, { ...defaultOptions, ...options })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          return response.json();
        });
    }

    // 确认对话框
    function confirmAction(message, callback) {
      if (confirm(message)) {
        callback();
      }
    }

    // 格式化日期
    function formatDate(dateString) {
      return new Date(dateString).toLocaleString('zh-CN');
    }

    // 格式化数字
    function formatNumber(number) {
      return new Intl.NumberFormat('zh-CN').format(number);
    }
  </script>
</body>

</html>