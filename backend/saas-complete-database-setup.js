#!/usr/bin/env node
/**
 * 智慧养鹅SaaS平台 - 完整多租户数据库架构
 * 包含平台管理和租户数据管理的完整逻辑
 */

const mysql = require("mysql2/promise");
const bcrypt = require("bcryptjs");

class SaaSPlatformDatabaseArchitecture {
  constructor() {
    this.rootConfig = {
      host: "localhost",
      port: 3306,
      user: "root",
      password: "",
      charset: "utf8mb4",
    };

    this.platformDbConfig = {
      host: "localhost", 
      port: 3306,
      user: "saas_admin",
      password: "saas_admin_2024!",
      database: "smart_goose_saas_platform",
      charset: "utf8mb4",
    };
  }

  async createPlatformDatabase() {
    let connection;
    try {
      connection = await mysql.createConnection(this.rootConfig);
      
      // 创建SaaS平台数据库
      await connection.execute(
        `CREATE DATABASE IF NOT EXISTS smart_goose_saas_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`
      );
      
      // 创建SaaS管理用户
      try {
        await connection.execute(
          `CREATE USER IF NOT EXISTS 'saas_admin'@'localhost' IDENTIFIED BY 'saas_admin_2024!'`
        );
      } catch (error) {
        if (!error.message.includes("already exists")) {
          console.log("⚠️ 用户可能已存在");
        }
      }
      
      // 授权
      await connection.execute(
        `GRANT ALL PRIVILEGES ON smart_goose_saas_platform.* TO 'saas_admin'@'localhost'`
      );
      await connection.execute(`FLUSH PRIVILEGES`);
      
      console.log("✅ SaaS平台数据库创建成功");
    } catch (error) {
      console.error("❌ SaaS平台数据库创建失败:", error.message);
      throw error;
    } finally {
      if (connection) await connection.end();
    }
  }

  async createPlatformTables() {
    let connection;
    try {
      connection = await mysql.createConnection(this.platformDbConfig);

      // 1. 平台管理员表
      const createPlatformAdminsTable = `
        CREATE TABLE IF NOT EXISTS platform_admins (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) UNIQUE NOT NULL,
          email VARCHAR(100) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          name VARCHAR(100),
          role ENUM('super_admin', 'admin', 'operator', 'support') DEFAULT 'operator',
          permissions JSON,
          status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
          last_login TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_username (username),
          INDEX idx_email (email),
          INDEX idx_status_role (status, role)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 2. 租户表 (SaaS核心表)
      const createTenantsTable = `
        CREATE TABLE IF NOT EXISTS tenants (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_code VARCHAR(50) UNIQUE NOT NULL COMMENT '租户代码',
          name VARCHAR(100) NOT NULL COMMENT '租户名称',
          display_name VARCHAR(100) COMMENT '显示名称',
          domain VARCHAR(100) UNIQUE COMMENT '独立域名',
          subdomain VARCHAR(50) UNIQUE COMMENT '子域名',
          database_name VARCHAR(100) UNIQUE NOT NULL COMMENT '租户数据库名',
          contact_name VARCHAR(100) COMMENT '联系人姓名',
          contact_email VARCHAR(100) COMMENT '联系人邮箱',
          contact_phone VARCHAR(20) COMMENT '联系人电话',
          address TEXT COMMENT '地址',
          subscription_plan ENUM('trial', 'basic', 'standard', 'premium', 'enterprise') DEFAULT 'trial',
          subscription_status ENUM('active', 'expired', 'suspended', 'cancelled') DEFAULT 'active',
          subscription_start_date DATE,
          subscription_end_date DATE,
          max_users INT DEFAULT 10 COMMENT '最大用户数',
          max_farms INT DEFAULT 1 COMMENT '最大养殖场数',
          max_flocks INT DEFAULT 50 COMMENT '最大鹅群数',
          storage_quota BIGINT DEFAULT 1073741824 COMMENT '存储配额(字节)',
          api_quota INT DEFAULT 10000 COMMENT 'API调用配额',
          custom_settings JSON COMMENT '自定义配置',
          billing_info JSON COMMENT '计费信息',
          status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_tenant_code (tenant_code),
          INDEX idx_subscription (subscription_plan, subscription_status),
          INDEX idx_status (status),
          INDEX idx_domain (domain),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 3. 订阅计划表
      const createSubscriptionPlansTable = `
        CREATE TABLE IF NOT EXISTS subscription_plans (
          id INT AUTO_INCREMENT PRIMARY KEY,
          plan_code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          display_name VARCHAR(100),
          description TEXT,
          price DECIMAL(10,2) DEFAULT 0.00,
          billing_cycle ENUM('monthly', 'quarterly', 'yearly') DEFAULT 'monthly',
          max_users INT DEFAULT 10,
          max_farms INT DEFAULT 1,
          max_flocks INT DEFAULT 50,
          storage_quota BIGINT DEFAULT 1073741824,
          api_quota INT DEFAULT 10000,
          features JSON COMMENT '功能特性',
          is_active BOOLEAN DEFAULT true,
          sort_order INT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_plan_code (plan_code),
          INDEX idx_active_sort (is_active, sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 4. 租户用户表 (每个租户的管理员)
      const createTenantUsersTable = `
        CREATE TABLE IF NOT EXISTS tenant_users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id INT NOT NULL,
          username VARCHAR(50) NOT NULL,
          email VARCHAR(100),
          password VARCHAR(255) NOT NULL,
          name VARCHAR(100),
          role ENUM('owner', 'admin', 'manager', 'user') DEFAULT 'user',
          permissions JSON,
          avatar VARCHAR(255),
          phone VARCHAR(20),
          status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
          last_login TIMESTAMP NULL,
          email_verified_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
          UNIQUE KEY unique_tenant_username (tenant_id, username),
          UNIQUE KEY unique_tenant_email (tenant_id, email),
          INDEX idx_tenant_status (tenant_id, status),
          INDEX idx_role (role)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 5. 系统配置表
      const createSystemConfigTable = `
        CREATE TABLE IF NOT EXISTS system_config (
          id INT AUTO_INCREMENT PRIMARY KEY,
          config_key VARCHAR(100) UNIQUE NOT NULL,
          config_value TEXT,
          config_type ENUM('string', 'number', 'boolean', 'json', 'array') DEFAULT 'string',
          category VARCHAR(50) DEFAULT 'general',
          description TEXT,
          is_public BOOLEAN DEFAULT false COMMENT '是否对租户公开',
          is_editable BOOLEAN DEFAULT true COMMENT '是否可编辑',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_category (category),
          INDEX idx_public (is_public)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 6. 平台日志表
      const createPlatformLogsTable = `
        CREATE TABLE IF NOT EXISTS platform_logs (
          id BIGINT AUTO_INCREMENT PRIMARY KEY,
          tenant_id INT NULL,
          user_id INT NULL,
          user_type ENUM('platform_admin', 'tenant_user') DEFAULT 'tenant_user',
          action VARCHAR(100) NOT NULL,
          module VARCHAR(50),
          details JSON,
          ip_address VARCHAR(45),
          user_agent TEXT,
          status ENUM('success', 'error', 'warning') DEFAULT 'success',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE SET NULL,
          INDEX idx_tenant_created (tenant_id, created_at),
          INDEX idx_action_module (action, module),
          INDEX idx_status (status),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 7. 使用统计表
      const createUsageStatsTable = `
        CREATE TABLE IF NOT EXISTS usage_stats (
          id BIGINT AUTO_INCREMENT PRIMARY KEY,
          tenant_id INT NOT NULL,
          stat_type ENUM('api_calls', 'storage_used', 'active_users', 'data_records') NOT NULL,
          stat_value BIGINT DEFAULT 0,
          stat_date DATE NOT NULL,
          additional_data JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
          UNIQUE KEY unique_tenant_stat_date (tenant_id, stat_type, stat_date),
          INDEX idx_tenant_type_date (tenant_id, stat_type, stat_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 8. 支付记录表
      const createPaymentsTable = `
        CREATE TABLE IF NOT EXISTS payments (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id INT NOT NULL,
          subscription_plan_id INT,
          amount DECIMAL(10,2) NOT NULL,
          currency VARCHAR(3) DEFAULT 'CNY',
          payment_method ENUM('alipay', 'wechat', 'bank_transfer', 'credit_card') NOT NULL,
          payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
          transaction_id VARCHAR(100),
          payment_gateway_response JSON,
          billing_period_start DATE,
          billing_period_end DATE,
          invoice_number VARCHAR(50),
          notes TEXT,
          paid_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
          FOREIGN KEY (subscription_plan_id) REFERENCES subscription_plans(id),
          INDEX idx_tenant_status (tenant_id, payment_status),
          INDEX idx_transaction_id (transaction_id),
          INDEX idx_billing_period (billing_period_start, billing_period_end)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 9. 平台通知表
      const createNotificationsTable = `
        CREATE TABLE IF NOT EXISTS notifications (
          id INT AUTO_INCREMENT PRIMARY KEY,
          target_type ENUM('all_tenants', 'specific_tenant', 'platform_admin') NOT NULL,
          target_id INT NULL COMMENT '当target_type为specific_tenant时，存储tenant_id',
          title VARCHAR(200) NOT NULL,
          content TEXT,
          notification_type ENUM('system', 'billing', 'feature', 'maintenance', 'security') DEFAULT 'system',
          priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
          status ENUM('draft', 'sent', 'scheduled') DEFAULT 'draft',
          scheduled_at TIMESTAMP NULL,
          sent_at TIMESTAMP NULL,
          read_count INT DEFAULT 0,
          created_by INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (created_by) REFERENCES platform_admins(id) ON DELETE SET NULL,
          INDEX idx_target_type_id (target_type, target_id),
          INDEX idx_status_scheduled (status, scheduled_at),
          INDEX idx_priority (priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 10. API密钥表
      const createApiKeysTable = `
        CREATE TABLE IF NOT EXISTS api_keys (
          id INT AUTO_INCREMENT PRIMARY KEY,
          tenant_id INT NOT NULL,
          key_name VARCHAR(100) NOT NULL,
          api_key VARCHAR(100) UNIQUE NOT NULL,
          api_secret VARCHAR(100) NOT NULL,
          permissions JSON COMMENT 'API权限',
          rate_limit INT DEFAULT 1000 COMMENT '每小时请求限制',
          allowed_ips TEXT COMMENT '允许的IP地址',
          is_active BOOLEAN DEFAULT true,
          expires_at TIMESTAMP NULL,
          last_used_at TIMESTAMP NULL,
          usage_count BIGINT DEFAULT 0,
          created_by INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
          FOREIGN KEY (created_by) REFERENCES tenant_users(id) ON DELETE SET NULL,
          INDEX idx_tenant_active (tenant_id, is_active),
          INDEX idx_api_key (api_key),
          INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `;

      // 执行表创建
      const tables = [
        { name: 'platform_admins', sql: createPlatformAdminsTable },
        { name: 'tenants', sql: createTenantsTable },
        { name: 'subscription_plans', sql: createSubscriptionPlansTable },
        { name: 'tenant_users', sql: createTenantUsersTable },
        { name: 'system_config', sql: createSystemConfigTable },
        { name: 'platform_logs', sql: createPlatformLogsTable },
        { name: 'usage_stats', sql: createUsageStatsTable },
        { name: 'payments', sql: createPaymentsTable },
        { name: 'notifications', sql: createNotificationsTable },
        { name: 'api_keys', sql: createApiKeysTable }
      ];

      for (const table of tables) {
        await connection.execute(table.sql);
        console.log(`✅ 创建表 ${table.name} 成功`);
      }

      console.log("✅ 所有平台表创建成功");
    } catch (error) {
      console.error("❌ 创建平台表失败:", error.message);
      throw error;
    } finally {
      if (connection) await connection.end();
    }
  }

  async insertPlatformInitialData() {
    let connection;
    try {
      connection = await mysql.createConnection(this.platformDbConfig);

      // 创建超级管理员
      const superAdminPassword = await bcrypt.hash("admin123", 10);
      const adminPassword = await bcrypt.hash("admin456", 10);

      const insertPlatformAdmins = `
        INSERT INTO platform_admins (username, email, password, name, role, permissions, status) VALUES
        ('super_admin', '<EMAIL>', ?, '超级管理员', 'super_admin', 
         '{"all": true, "users": "*", "tenants": "*", "billing": "*", "system": "*"}', 'active'),
        ('platform_admin', '<EMAIL>', ?, '平台管理员', 'admin',
         '{"tenants": ["read", "create", "update"], "billing": ["read"], "system": ["read"]}', 'active')
        ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
      `;

      await connection.execute(insertPlatformAdmins, [superAdminPassword, adminPassword]);

      // 创建订阅计划
      const insertSubscriptionPlans = `
        INSERT INTO subscription_plans (plan_code, name, display_name, description, price, billing_cycle, 
                                      max_users, max_farms, max_flocks, storage_quota, api_quota, features) VALUES
        ('trial', 'trial', '试用版', '7天免费试用', 0.00, 'monthly', 3, 1, 10, 536870912, 1000, 
         '{"basic_features": true, "advanced_analytics": false, "api_access": false, "priority_support": false}'),
        ('basic', 'basic', '基础版', '适合小型养殖场', 99.00, 'monthly', 10, 1, 50, 2147483648, 5000,
         '{"basic_features": true, "advanced_analytics": true, "api_access": false, "priority_support": false}'),
        ('standard', 'standard', '标准版', '适合中型养殖场', 299.00, 'monthly', 50, 3, 200, 10737418240, 20000,
         '{"basic_features": true, "advanced_analytics": true, "api_access": true, "priority_support": false}'),
        ('premium', 'premium', '高级版', '适合大型养殖场', 599.00, 'monthly', 200, 10, 1000, 53687091200, 100000,
         '{"basic_features": true, "advanced_analytics": true, "api_access": true, "priority_support": true}'),
        ('enterprise', 'enterprise', '企业版', '定制化解决方案', 1999.00, 'monthly', 1000, 50, 10000, 214748364800, 1000000,
         '{"basic_features": true, "advanced_analytics": true, "api_access": true, "priority_support": true, "custom_features": true}')
        ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
      `;

      await connection.execute(insertSubscriptionPlans);

      // 创建示例租户
      const insertTenants = `
        INSERT INTO tenants (tenant_code, name, display_name, database_name, contact_name, contact_email, 
                           subscription_plan, subscription_status, subscription_start_date, subscription_end_date,
                           max_users, max_farms, max_flocks, storage_quota, api_quota) VALUES
        ('DEMO001', '示例养殖场A', '绿野仙鹅养殖场', 'tenant_demo001_db', '张三', '<EMAIL>', 
         'standard', 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 50, 3, 200, 10737418240, 20000),
        ('DEMO002', '示例养殖场B', '白云山养鹅基地', 'tenant_demo002_db', '李四', '<EMAIL>',
         'premium', 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 200, 10, 1000, 53687091200, 100000),
        ('DEMO003', '示例养殖场C', '黄河畔养鹅合作社', 'tenant_demo003_db', '王五', '<EMAIL>',
         'enterprise', 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 1000, 50, 10000, 214748364800, 1000000)
        ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
      `;

      await connection.execute(insertTenants);

      // 为每个租户创建管理员账户
      const demoPassword = await bcrypt.hash("demo123", 10);
      const insertTenantUsers = `
        INSERT INTO tenant_users (tenant_id, username, email, password, name, role, status) 
        SELECT t.id, 'admin', CONCAT('admin@', LOWER(t.tenant_code), '.com'), ?, 
               CONCAT(t.display_name, '管理员'), 'owner', 'active'
        FROM tenants t 
        WHERE t.tenant_code IN ('DEMO001', 'DEMO002', 'DEMO003')
        ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
      `;

      await connection.execute(insertTenantUsers, [demoPassword]);

      // 创建系统配置
      const insertSystemConfig = `
        INSERT INTO system_config (config_key, config_value, config_type, category, description, is_public) VALUES
        ('platform.name', '智慧养鹅SaaS平台', 'string', 'general', '平台名称', true),
        ('platform.version', '1.0.0', 'string', 'general', '平台版本', true),
        ('platform.maintenance_mode', 'false', 'boolean', 'system', '维护模式', false),
        ('tenant.max_trial_days', '7', 'number', 'billing', '试用期天数', false),
        ('api.rate_limit_default', '1000', 'number', 'api', '默认API限制', false),
        ('notification.email_enabled', 'true', 'boolean', 'notification', '邮件通知开关', false),
        ('security.password_min_length', '6', 'number', 'security', '密码最小长度', true),
        ('storage.upload_max_size', '10485760', 'number', 'storage', '文件上传大小限制(字节)', false)
        ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
      `;

      await connection.execute(insertSystemConfig);

      console.log("✅ 平台初始数据插入成功");
    } catch (error) {
      console.error("❌ 插入平台初始数据失败:", error.message);
      throw error;
    } finally {
      if (connection) await connection.end();
    }
  }

  // 为租户创建独立数据库
  async createTenantDatabase(tenantCode) {
    let rootConnection;
    let tenantConnection;
    
    try {
      rootConnection = await mysql.createConnection(this.rootConfig);
      
      const dbName = `tenant_${tenantCode.toLowerCase()}_db`;
      const dbUser = `tenant_${tenantCode.toLowerCase()}`;
      const dbPassword = `${tenantCode.toLowerCase()}_2024!`;
      
      // 创建租户数据库
      await rootConnection.execute(`CREATE DATABASE IF NOT EXISTS ${dbName} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      
      // 创建租户用户
      try {
        await rootConnection.execute(`CREATE USER IF NOT EXISTS '${dbUser}'@'localhost' IDENTIFIED BY '${dbPassword}'`);
      } catch (error) {
        if (!error.message.includes("already exists")) {
          console.log(`⚠️ 租户用户 ${dbUser} 可能已存在`);
        }
      }
      
      // 授权
      await rootConnection.execute(`GRANT ALL PRIVILEGES ON ${dbName}.* TO '${dbUser}'@'localhost'`);
      await rootConnection.execute(`FLUSH PRIVILEGES`);
      
      // 连接到租户数据库创建业务表
      tenantConnection = await mysql.createConnection({
        ...this.rootConfig,
        database: dbName
      });
      
      // 租户业务表结构
      const createTenantTables = [
        // 用户表
        `CREATE TABLE IF NOT EXISTS users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100),
          phone VARCHAR(20),
          name VARCHAR(100),
          role ENUM('admin', 'manager', 'operator', 'viewer') DEFAULT 'viewer',
          department VARCHAR(100),
          avatar VARCHAR(255),
          status ENUM('active', 'inactive') DEFAULT 'active',
          last_login TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_username (username),
          INDEX idx_status (status),
          INDEX idx_role (role)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

        // 养殖场表
        `CREATE TABLE IF NOT EXISTS farms (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          address TEXT,
          area DECIMAL(10,2) COMMENT '面积(平方米)',
          capacity INT COMMENT '容量(只)',
          manager_id INT,
          contact_phone VARCHAR(20),
          coordinates JSON COMMENT 'GPS坐标',
          status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
          INDEX idx_status (status),
          INDEX idx_manager (manager_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

        // 鹅群表
        `CREATE TABLE IF NOT EXISTS flocks (
          id INT AUTO_INCREMENT PRIMARY KEY,
          farm_id INT NOT NULL,
          name VARCHAR(100) NOT NULL,
          breed VARCHAR(50) COMMENT '品种',
          count INT DEFAULT 0 COMMENT '数量',
          birth_date DATE COMMENT '出生日期',
          source VARCHAR(100) COMMENT '来源',
          status ENUM('healthy', 'sick', 'quarantine', 'sold') DEFAULT 'healthy',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (farm_id) REFERENCES farms(id) ON DELETE CASCADE,
          INDEX idx_farm_status (farm_id, status),
          INDEX idx_breed (breed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

        // 健康记录表
        `CREATE TABLE IF NOT EXISTS health_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          flock_id INT NOT NULL,
          user_id INT NOT NULL,
          record_date DATE NOT NULL,
          health_status ENUM('healthy', 'sick', 'recovering', 'dead') DEFAULT 'healthy',
          symptoms TEXT,
          diagnosis TEXT,
          treatment TEXT,
          medication VARCHAR(200),
          dosage VARCHAR(100),
          temperature DECIMAL(4,1),
          weight DECIMAL(6,2),
          notes TEXT,
          images JSON COMMENT '图片路径',
          next_check_date DATE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id),
          INDEX idx_flock_date (flock_id, record_date),
          INDEX idx_health_status (health_status),
          INDEX idx_next_check (next_check_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

        // 生产记录表
        `CREATE TABLE IF NOT EXISTS production_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          flock_id INT NOT NULL,
          user_id INT NOT NULL,
          record_date DATE NOT NULL,
          egg_count INT DEFAULT 0,
          egg_weight DECIMAL(6,2),
          feed_consumption DECIMAL(8,2),
          water_consumption DECIMAL(8,2),
          mortality_count INT DEFAULT 0,
          temperature DECIMAL(5,2),
          humidity DECIMAL(5,2),
          weather VARCHAR(50),
          notes TEXT,
          images JSON,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (flock_id) REFERENCES flocks(id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id),
          INDEX idx_flock_date (flock_id, record_date),
          INDEX idx_record_date (record_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

        // 库存表
        `CREATE TABLE IF NOT EXISTS inventory (
          id INT AUTO_INCREMENT PRIMARY KEY,
          farm_id INT,
          category ENUM('feed', 'medicine', 'equipment', 'supplies') NOT NULL,
          item_name VARCHAR(100) NOT NULL,
          brand VARCHAR(100),
          specification VARCHAR(100),
          unit VARCHAR(20) DEFAULT '个',
          current_stock DECIMAL(10,2) DEFAULT 0,
          min_stock DECIMAL(10,2) DEFAULT 0,
          max_stock DECIMAL(10,2) DEFAULT 0,
          unit_price DECIMAL(10,2),
          supplier VARCHAR(100),
          expiry_date DATE,
          storage_location VARCHAR(100),
          status ENUM('normal', 'low_stock', 'expired', 'discontinued') DEFAULT 'normal',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (farm_id) REFERENCES farms(id) ON DELETE SET NULL,
          INDEX idx_farm_category (farm_id, category),
          INDEX idx_status (status),
          INDEX idx_expiry_date (expiry_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

        // 库存变动记录表
        `CREATE TABLE IF NOT EXISTS inventory_transactions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          inventory_id INT NOT NULL,
          user_id INT NOT NULL,
          transaction_type ENUM('in', 'out', 'adjust', 'expired', 'damaged') NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2),
          total_amount DECIMAL(10,2),
          reason VARCHAR(200),
          reference_no VARCHAR(100),
          transaction_date DATE NOT NULL,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE,
          FOREIGN KEY (user_id) REFERENCES users(id),
          INDEX idx_inventory_date (inventory_id, transaction_date),
          INDEX idx_transaction_type (transaction_type),
          INDEX idx_transaction_date (transaction_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

        // 财务记录表
        `CREATE TABLE IF NOT EXISTS financial_records (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          category ENUM('income', 'expense') NOT NULL,
          type VARCHAR(50) NOT NULL COMMENT '收入/支出类型',
          amount DECIMAL(12,2) NOT NULL,
          description VARCHAR(200),
          reference_no VARCHAR(100),
          transaction_date DATE NOT NULL,
          payment_method ENUM('cash', 'bank_transfer', 'alipay', 'wechat', 'other') DEFAULT 'cash',
          related_id INT COMMENT '关联记录ID',
          related_type VARCHAR(50) COMMENT '关联记录类型',
          attachments JSON COMMENT '附件',
          tags VARCHAR(500) COMMENT '标签',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id),
          INDEX idx_category_date (category, transaction_date),
          INDEX idx_type (type),
          INDEX idx_transaction_date (transaction_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
      ];

      // 创建所有租户业务表
      for (const sql of createTenantTables) {
        await tenantConnection.execute(sql);
      }
      
      // 插入租户初始数据
      const adminPassword = await bcrypt.hash("admin123", 10);
      await tenantConnection.execute(
        `INSERT INTO users (username, password, email, name, role, status) VALUES
         ('admin', ?, '<EMAIL>', '系统管理员', 'admin', 'active')
         ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP`,
        [adminPassword]
      );

      console.log(`✅ 租户 ${tenantCode} 的数据库创建成功`);
      return { dbName, dbUser, dbPassword };
      
    } catch (error) {
      console.error(`❌ 创建租户 ${tenantCode} 数据库失败:`, error.message);
      throw error;
    } finally {
      if (rootConnection) await rootConnection.end();
      if (tenantConnection) await tenantConnection.end();
    }
  }

  async setupCompleteSaaSPlatform() {
    console.log('🚀 开始设置完整SaaS平台架构...\n');

    try {
      // 1. 创建平台数据库
      console.log('1️⃣ 创建SaaS平台数据库...');
      await this.createPlatformDatabase();

      // 2. 创建平台表结构
      console.log('\n2️⃣ 创建平台表结构...');
      await this.createPlatformTables();

      // 3. 插入平台初始数据
      console.log('\n3️⃣ 插入平台初始数据...');
      await this.insertPlatformInitialData();

      // 4. 为示例租户创建独立数据库
      console.log('\n4️⃣ 为示例租户创建独立数据库...');
      const tenants = ['DEMO001', 'DEMO002', 'DEMO003'];
      for (const tenantCode of tenants) {
        await this.createTenantDatabase(tenantCode);
      }

      console.log('\n🎉 SaaS平台架构设置完成!');
      console.log('\n📊 系统配置摘要:');
      console.log('┌─────────────────────────────────────┐');
      console.log('│  智慧养鹅SaaS平台 - 系统配置摘要      │');
      console.log('├─────────────────────────────────────┤');
      console.log('│ 平台数据库: smart_goose_saas_platform │');
      console.log('│ 平台管理员: super_admin/admin123      │');
      console.log('│ 订阅计划: 5个(试用版-企业版)          │');
      console.log('│ 示例租户: 3个(DEMO001-003)            │');
      console.log('│ 租户数据库: 独立隔离                   │');
      console.log('│ 多租户架构: ✅ 完整支持              │');
      console.log('└─────────────────────────────────────┘');

    } catch (error) {
      console.error("\n❌ SaaS平台架构设置失败:", error.message);
      process.exit(1);
    }
  }
}

// 运行设置
if (require.main === module) {
  const setup = new SaaSPlatformDatabaseArchitecture();
  setup.setupCompleteSaaSPlatform().catch(console.error);
}

module.exports = SaaSPlatformDatabaseArchitecture;