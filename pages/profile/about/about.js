// pages/profile/about/about.js
Page({
  data: {
    // 应用信息
    appInfo: {
      name: '管理系统',
      version: '2.1.0',
      buildNumber: '20241201',
      description: '专业的智能化管理解决方案，集健康监测、环境管理、生产统计于一体'
    },

    // 产品特色
    features: [
      {
        id: 1,
        icon: '🩺',
        title: 'AI智能诊断',
        description: '基于深度学习的疾病识别和健康预警系统'
      },
      {
        id: 2,
        icon: '🌡️',
        title: '环境监控',
        description: '实时监测温湿度、空气质量等关键环境指标'
      },
      {
        id: 3,
        icon: '📊',
        title: '数据分析',
        description: '全面的生产数据统计分析和可视化报表'
      },
      {
        id: 4,
        icon: '📱',
        title: '移动管理',
        description: '随时随地查看养殖场状况，移动办公更便捷'
      },
      {
        id: 5,
        icon: '🔔',
        title: '智能预警',
        description: '异常情况及时推送，确保养殖安全'
      },
      {
        id: 6,
        icon: '☁️',
        title: '云端同步',
        description: '数据云端存储，多设备同步，安全可靠'
      }
    ],

    // 公司信息
    companyInfo: {
      name: '智慧农业科技有限公司',
      description: '专注于智慧农业解决方案的高新技术企业，致力于用科技改变传统农业',
      foundedYear: '2020年',
      address: '北京市海淀区中关村软件园',
      email: '<EMAIL>',
      website: 'www.zhihuiyange.com'
    },

    // 技术团队
    teamMembers: [
      {
        id: 1,
        name: '张博士',
        role: '技术总监',
        avatar: '/images/default_avatar.png',
        description: '10年AI算法经验'
      },
      {
        id: 2,
        name: '李工程师',
        role: '前端开发',
        avatar: '/images/default_avatar.png',
        description: '资深前端架构师'
      },
      {
        id: 3,
        name: '王工程师',
        role: '后端开发',
        avatar: '/images/default_avatar.png',
        description: '云计算专家'
      },
      {
        id: 4,
        name: '赵专家',
        role: '农业顾问',
        avatar: '/images/default_avatar.png',
        description: '20年养殖经验'
      }
    ],

    // 联系信息
    contactInfo: {
      phone: '************',
      email: '<EMAIL>',
      workTime: '周一至周五 9:00-18:00',
      address: '北京市海淀区中关村软件园二期'
    },

    // 更新日志
    changelog: [
      {
        version: '2.1.0',
        date: '2024-12-01',
        changes: [
          '新增AI智能诊断功能',
          '优化环境监控界面',
          '修复数据同步问题',
          '提升系统稳定性'
        ]
      },
      {
        version: '2.0.5',
        date: '2024-11-15',
        changes: [
          '新增财务管理模块',
          '优化报表生成速度',
          '修复已知Bug'
        ]
      },
      {
        version: '2.0.0',
        date: '2024-10-01',
        changes: [
          '全新UI设计',
          '重构核心架构',
          '新增OA办公功能',
          '提升用户体验'
        ]
      }
    ]
  },

  onLoad: function (options) {
    // 页面加载时获取最新版本信息
    this.checkVersion();
  },

  onShow: function () {
    // 页面显示
  },

  // 检查版本更新
  checkVersion: function() {
    // 模拟版本检查
    const currentVersion = this.data.appInfo.version;
    try { const logger = require('../../../utils/logger.js'); logger.info && logger.info('当前版本', currentVersion); } catch(_) {}
    
    // 这里可以调用API检查是否有新版本
    // this.checkForUpdates();
  },

  // 拨打客服电话
  onCall: function() {
    wx.makePhoneCall({
      phoneNumber: this.data.contactInfo.phone,
      success: () => {
        try { const logger = require('../../../utils/logger.js'); logger.info && logger.info('拨打电话成功'); } catch(_) {}
      },
      fail: (err) => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
        try { const logger = require('../../../utils/logger.js'); logger.error && logger.error('拨打电话失败', err); } catch(_) {}
      }
    });
  },

  // 复制邮箱地址
  onCopyEmail: function() {
    wx.setClipboardData({
      data: this.data.contactInfo.email,
      success: () => {
        wx.showToast({
          title: '邮箱地址已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 查看隐私政策
  onViewPrivacyPolicy: function() {
    wx.navigateTo({
      url: '/pages/profile/legal/privacy/privacy',
      fail: () => {
        wx.showModal({
          title: '隐私政策',
          content: '我们重视您的隐私权益，详细的隐私政策请访问官网查看。',
          showCancel: false
        });
      }
    });
  },

  // 查看用户协议
  onViewUserAgreement: function() {
    wx.navigateTo({
      url: '/pages/profile/legal/agreement/agreement',
      fail: () => {
        wx.showModal({
          title: '用户协议',
          content: '使用本应用即表示您同意我们的用户服务协议，详细内容请访问官网查看。',
          showCancel: false
        });
      }
    });
  },

  // 查看开源许可
  onViewOpenSource: function() {
    wx.navigateTo({
      url: '/pages/profile/legal/opensource/opensource',
      fail: () => {
        wx.showModal({
          title: '开源许可',
          content: '本应用使用了多个开源组件，感谢开源社区的贡献。详细信息请访问官网查看。',
          showCancel: false
        });
      }
    });
  },

  // 意见反馈
  onFeedback: function() {
    wx.navigateTo({
      url: '/pages/profile/feedback/feedback'
    });
  },

  // 帮助中心
  onHelp: function() {
    wx.navigateTo({
      url: '/pages/profile/help/help'
    });
  },

  // 检查更新
  checkForUpdates: function() {
    wx.showLoading({
      title: '检查更新中...'
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟没有更新
      wx.showModal({
        title: '检查更新',
        content: '当前已是最新版本',
        showCancel: false
      });
    }, 1500);
  },

  // 分享应用
  onShareApp: function() {
    return {
      title: '智慧养鹅管理系统',
      desc: '专业的智能化养鹅管理解决方案',
      path: '/pages/home/<USER>'
    };
  },

  // 评价应用
  onRateApp: function() {
    wx.showModal({
      title: '应用评价',
      content: '如果您觉得我们的应用不错，请给我们一个好评吧！',
      confirmText: '去评价',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到应用商店评价页面
          wx.showToast({
            title: '感谢您的支持！',
            icon: 'success'
          });
        }
      }
    });
  },

  // 查看系统信息
  onViewSystemInfo: function() {
    wx.getSystemInfo({
      success: (res) => {
        const systemInfo = `
设备型号：${res.model}
系统版本：${res.system}
微信版本：${res.version}
屏幕尺寸：${res.screenWidth}x${res.screenHeight}
        `;
        
        wx.showModal({
          title: '系统信息',
          content: systemInfo.trim(),
          showCancel: false
        });
      }
    });
  }
});