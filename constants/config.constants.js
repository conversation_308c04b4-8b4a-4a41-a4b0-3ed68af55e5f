/**
 * 应用配置常量
 * 统一管理应用级别的配置信息
 */

// ===== 应用基础信息 =====
const APP_INFO = {
  NAME: '管理系统',
  VERSION: '2.6.0',
  BUILD_VERSION: '2.6.0.20250131',
  DESCRIPTION: '基于AI技术的智能化管理平台',
  AUTHOR: '管理系统团队',
  COPYRIGHT: '© 2024 管理系统',

  // 小程序信息
  MINIPROGRAM: {
    APP_ID: 'wx1234567890abcdef',  // 替换为实际AppID
    NAME: '管理系统',
    SHORT_NAME: '管理助手',
    SLOGAN: '让管理更智慧'
  },
  
  // 联系信息
  CONTACT: {
    SUPPORT_EMAIL: '<EMAIL>',
    SUPPORT_PHONE: '************',
    WEBSITE: 'https://www.management.com',
    QQ_GROUP: '123456789',
    WECHAT_GROUP: 'management_support'
  }
};

// ===== 功能开关配置 =====
const FEATURE_FLAGS = {
  // AI功能开关
  AI_FEATURES: {
    ENABLED: true,
    CHAT: true,                    // AI对话
    IMAGE_RECOGNITION: true,       // 图像识别
    HEALTH_DIAGNOSIS: true,        // 健康诊断
    INVENTORY_COUNT: true,         // 库存盘点
    FINANCE_ANALYSIS: false        // 财务分析 (开发中)
  },
  
  // 实验性功能
  EXPERIMENTAL: {
    VOICE_ASSISTANT: false,        // 语音助手
    AR_CAMERA: false,              // AR相机
    BLOCKCHAIN_TRACE: false,       // 区块链溯源
    IOT_INTEGRATION: false         // IoT设备集成
  },
  
  // 第三方集成
  INTEGRATIONS: {
    WEATHER_API: true,             // 天气API
    MAP_SERVICE: true,             // 地图服务
    PAYMENT_GATEWAY: true,         // 支付网关
    SMS_SERVICE: true,             // 短信服务
    EMAIL_SERVICE: false           // 邮件服务
  },
  
  // 高级功能
  ADVANCED: {
    MULTI_TENANT: true,            // 多租户
    DATA_EXPORT: true,             // 数据导出
    BATCH_OPERATIONS: true,        // 批量操作
    ADVANCED_ANALYTICS: false,     // 高级分析
    CUSTOM_REPORTS: false          // 自定义报表
  }
};

// ===== 性能配置 =====
const PERFORMANCE_CONFIG = {
  // 缓存配置
  CACHE: {
    ENABLED: true,
    DEFAULT_TTL: 300000,          // 默认缓存时间 5分钟
    MAX_SIZE: 100,                // 最大缓存条目数
    STORAGE_TYPE: 'memory'        // memory | localStorage
  },
  
  // 分页配置
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,        // 默认每页条数
    MAX_PAGE_SIZE: 100,           // 最大每页条数
    PREFETCH_PAGES: 1             // 预加载页数
  },
  
  // 图片配置
  IMAGE: {
    MAX_SIZE: 10 * 1024 * 1024,   // 最大图片大小 10MB
    QUALITY: 80,                  // 压缩质量
    MAX_WIDTH: 1920,              // 最大宽度
    MAX_HEIGHT: 1080,             // 最大高度
    FORMATS: ['jpg', 'jpeg', 'png', 'webp'] // 支持格式
  },
  
  // 请求配置
  REQUEST: {
    TIMEOUT: 10000,               // 请求超时 10秒
    RETRY_COUNT: 2,               // 重试次数
    CONCURRENT_LIMIT: 5,          // 并发限制
    RATE_LIMIT: 100               // 每分钟请求限制
  }
};

// ===== 界面配置 =====
const UI_CONFIG = {
  // 主题配置
  THEME: {
    DEFAULT: 'light',             // 默认主题
    AVAILABLE: ['light', 'dark', 'auto'], // 可用主题
    CUSTOM_COLORS: true           // 支持自定义颜色
  },
  
  // 布局配置
  LAYOUT: {
    SIDEBAR_WIDTH: 280,           // 侧边栏宽度 (rpx)
    HEADER_HEIGHT: 120,           // 头部高度 (rpx)
    TAB_BAR_HEIGHT: 100,          // 标签栏高度 (rpx)
    SAFE_AREA_PADDING: true       // 安全区域适配
  },
  
  // 动画配置
  ANIMATION: {
    ENABLED: true,                // 启用动画
    DURATION: 300,                // 默认动画时长
    EASING: 'ease-out',           // 缓动函数
    REDUCE_MOTION: false          // 减少动画(无障碍)
  },
  
  // 加载配置
  LOADING: {
    SHOW_LOADING: true,           // 显示加载动画
    MIN_LOADING_TIME: 500,        // 最小加载时间
    SKELETON_ENABLED: true,       // 骨架屏
    SHIMMER_EFFECT: true          // 光效动画
  }
};

// ===== 安全配置 =====
const SECURITY_CONFIG = {
  // 认证配置
  AUTH: {
    TOKEN_STORAGE_KEY: 'access_token',
    REFRESH_TOKEN_KEY: 'refresh_token',
    USER_INFO_KEY: 'user_info',
    SESSION_TIMEOUT: 7200000,     // 会话超时 2小时
    AUTO_REFRESH: true,           // 自动刷新token
    SECURE_STORAGE: true          // 安全存储
  },
  
  // 密码策略
  PASSWORD: {
    MIN_LENGTH: 6,                // 最小长度
    MAX_LENGTH: 20,               // 最大长度
    REQUIRE_NUMBERS: true,        // 需要数字
    REQUIRE_LETTERS: true,        // 需要字母
    REQUIRE_SYMBOLS: false,       // 需要符号
    HISTORY_COUNT: 5              // 密码历史记录数
  },
  
  // 数据保护
  DATA_PROTECTION: {
    ENCRYPT_SENSITIVE: true,      // 加密敏感数据
    MASK_PHONE: true,             // 手机号脱敏
    MASK_EMAIL: true,             // 邮箱脱敏
    LOG_SENSITIVE: false          // 记录敏感操作
  }
};

// ===== 业务规则配置 =====
const BUSINESS_RULES = {
  // 鹅群管理规则
  FLOCK: {
    MAX_FLOCKS_PER_USER: 50,      // 每用户最大鹅群数
    MIN_FLOCK_SIZE: 1,            // 最小鹅群规模
    MAX_FLOCK_SIZE: 100000,       // 最大鹅群规模
    AUTO_BATCH_NUMBER: true,      // 自动生成批次号
    REQUIRE_LOCATION: false       // 是否必须填写位置
  },
  
  // 健康管理规则
  HEALTH: {
    MAX_RECORDS_PER_DAY: 100,     // 每日最大记录数
    REQUIRE_PHOTOS: false,        // 是否必须上传照片
    AUTO_REMIND_CHECKUP: true,    // 自动提醒体检
    CHECKUP_INTERVAL_DAYS: 30,    // 体检间隔天数
    VACCINATION_REMINDER: true    // 疫苗提醒
  },
  
  // 生产管理规则
  PRODUCTION: {
    MAX_RECORDS_PER_DAY: 500,     // 每日最大记录数
    REQUIRE_WEATHER: true,        // 是否必须记录天气
    AUTO_CALCULATE_STATS: true,   // 自动计算统计数据
    ALLOW_FUTURE_DATES: false,    // 允许未来日期
    DATA_RETENTION_DAYS: 1095     // 数据保留天数 (3年)
  },
  
  // 库存管理规则
  INVENTORY: {
    LOW_STOCK_ALERT: true,        // 低库存警报
    AUTO_REORDER: false,          // 自动重新订购
    EXPIRY_ALERT_DAYS: 30,        // 过期提醒天数
    REQUIRE_SUPPLIER: false,      // 是否必须填写供应商
    BATCH_TRACKING: true          // 批次追踪
  }
};

// ===== 第三方服务配置 =====
const THIRD_PARTY_CONFIG = {
  // 地图服务
  MAP: {
    PROVIDER: 'tencent',          // 腾讯地图
    API_KEY: '',                  // API密钥
    DEFAULT_ZOOM: 15,             // 默认缩放级别
    ENABLE_LOCATION: true         // 启用定位
  },
  
  // 天气服务
  WEATHER: {
    PROVIDER: 'qweather',         // 和风天气
    API_KEY: '',                  // API密钥
    UPDATE_INTERVAL: 3600000,     // 更新间隔 1小时
    CACHE_DURATION: 1800000       // 缓存时长 30分钟
  },
  
  // 支付服务
  PAYMENT: {
    WECHAT_PAY: {
      ENABLED: true,
      APP_ID: '',
      MCH_ID: '',
      API_KEY: ''
    },
    ALIPAY: {
      ENABLED: false,
      APP_ID: '',
      PRIVATE_KEY: ''
    }
  },
  
  // 短信服务
  SMS: {
    PROVIDER: 'aliyun',           // 阿里云短信
    ACCESS_KEY: '',
    SECRET_KEY: '',
    SIGN_NAME: '智慧养鹅',
    TEMPLATE_CODE: 'SMS_123456789'
  }
};

// ===== 调试配置 =====
const DEBUG_CONFIG = {
  // 日志配置
  LOGGING: {
    ENABLED: true,                // 启用日志
    LEVEL: 'info',                // 日志级别 debug|info|warn|error
    CONSOLE_OUTPUT: true,         // 控制台输出
    REMOTE_LOGGING: false,        // 远程日志
    MAX_LOG_SIZE: 1000,           // 最大日志条数
    INCLUDE_STACK_TRACE: true     // 包含堆栈跟踪
  },
  
  // 开发工具
  DEV_TOOLS: {
    VCONSOLE_ENABLED: false,      // vConsole调试工具
    MOCK_DATA: false,             // 模拟数据
    API_INTERCEPTOR: false,       // API拦截器
    PERFORMANCE_MONITOR: true     // 性能监控
  },
  
  // 测试配置
  TESTING: {
    UNIT_TESTS: false,            // 单元测试
    E2E_TESTS: false,             // 端到端测试
    COVERAGE_REPORT: false,       // 覆盖率报告
    MOCK_APIS: false              // 模拟API
  }
};

// ===== 多语言配置 =====
const I18N_CONFIG = {
  DEFAULT_LANGUAGE: 'zh-CN',      // 默认语言
  SUPPORTED_LANGUAGES: ['zh-CN', 'zh-TW', 'en-US'], // 支持语言
  FALLBACK_LANGUAGE: 'zh-CN',     // 回退语言
  AUTO_DETECT: true,              // 自动检测
  CACHE_TRANSLATIONS: true,       // 缓存翻译
  NAMESPACE_SEPARATOR: '.',       // 命名空间分隔符
  KEY_SEPARATOR: '.',             // 键分隔符
  INTERPOLATION: {
    PREFIX: '{{',
    SUFFIX: '}}'
  }
};

// ===== 工具函数 =====
const CONFIG_UTILS = {
  /**
   * 获取功能开关状态
   * @param {string} feature 功能路径 (如: 'AI_FEATURES.CHAT')
   * @returns {boolean} 开关状态
   */
  isFeatureEnabled: (feature) => {
    const parts = feature.split('.');
    let current = FEATURE_FLAGS;
    
    for (const part of parts) {
      if (current[part] === undefined) return false;
      current = current[part];
    }
    
    return Boolean(current);
  },
  
  /**
   * 获取应用版本信息
   * @returns {Object} 版本信息
   */
  getVersionInfo: () => ({
    version: APP_INFO.VERSION,
    buildVersion: APP_INFO.BUILD_VERSION,
    buildDate: new Date().toISOString()
  }),
  
  /**
   * 检查是否为开发环境
   * @returns {boolean}
   */
  isDevelopment: () => {
    // 在小程序中可以通过 __wxConfig 或其他方式判断
    return DEBUG_CONFIG.LOGGING.LEVEL === 'debug';
  },
  
  /**
   * 获取安全配置
   * @param {string} key 配置键
   * @returns {*} 配置值
   */
  getSecurityConfig: (key) => {
    return SECURITY_CONFIG[key];
  }
};

// 导出所有配置常量
module.exports = {
  APP_INFO,
  FEATURE_FLAGS,
  PERFORMANCE_CONFIG,
  UI_CONFIG,
  SECURITY_CONFIG,
  BUSINESS_RULES,
  THIRD_PARTY_CONFIG,
  DEBUG_CONFIG,
  I18N_CONFIG,
  CONFIG_UTILS
};