/**
 * 智慧养鹅SaaS平台 - 统一常量配置
 * 版本: 2.0.0
 * 说明: 整合所有常量配置，确保版本唯一性
 */

// ==================== 平台基础配置 ====================
const PLATFORM_CONFIG = {
  NAME: '智慧养鹅SaaS平台',
  VERSION: '2.0.0',
  ENVIRONMENT: process.env.NODE_ENV || 'development',
  
  // 服务端口配置
  PORTS: {
    MAIN_BACKEND: process.env.MAIN_PORT || 3000,
    ADMIN_BACKEND: process.env.ADMIN_PORT || 4000,
    WEBSOCKET: process.env.WS_PORT || 8080
  },

  // 数据库配置
  DATABASE: {
    PLATFORM_DB: 'smart_goose_saas_platform',
    TENANT_DB_PREFIX: 'tenant_',
    TENANT_DB_SUFFIX: '_db',
    CHARSET: 'utf8mb4',
    COLLATE: 'utf8mb4_unicode_ci'
  },

  // 多租户配置
  MULTI_TENANT: {
    ISOLATION_LEVEL: 'database', // database | schema | row
    MAX_TENANTS: 1000,
    TENANT_CODE_LENGTH: 8,
    TENANT_CODE_PREFIX: 'T'
  }
};

// ==================== API接口配置 ====================
const API_CONFIG = {
  VERSION: 'v2',
  BASE_PATH: '/api/v2',
  
  // 统一API端点
  ENDPOINTS: {
    // 平台管理API
    PLATFORM: {
      TENANTS: '/api/v2/platform/tenants',
      USERS: '/api/v2/platform/users',
      BILLING: '/api/v2/platform/billing',
      STATS: '/api/v2/platform/stats',
      CONFIG: '/api/v2/platform/config'
    },

    // 租户业务API
    TENANT: {
      AUTH: '/api/v2/tenant/auth',
      FARMS: '/api/v2/tenant/farms',
      FLOCKS: '/api/v2/tenant/flocks',
      HEALTH: '/api/v2/tenant/health',
      PRODUCTION: '/api/v2/tenant/production',
      INVENTORY: '/api/v2/tenant/inventory',
      FINANCE: '/api/v2/tenant/finance',
      USERS: '/api/v2/tenant/users'
    },

    // 微信小程序API
    MINIPROGRAM: {
      LOGIN: '/api/v2/mp/login',
      USER_INFO: '/api/v2/mp/userinfo',
      UPLOAD: '/api/v2/mp/upload'
    }
  },

  // 请求限制
  RATE_LIMIT: {
    DEFAULT: 1000, // 每小时请求数
    PREMIUM: 10000,
    ENTERPRISE: 100000,
    WINDOW_MS: 60 * 60 * 1000 // 1小时
  },

  // 响应格式
  RESPONSE_FORMAT: {
    SUCCESS: { success: true, data: null, message: 'ok' },
    ERROR: { success: false, error: null, message: 'error' }
  }
};

// ==================== 订阅计划配置 ====================
const SUBSCRIPTION_PLANS = {
  TRIAL: {
    code: 'trial',
    name: '试用版',
    price: 0,
    duration_days: 7,
    features: {
      max_users: 3,
      max_farms: 1,
      max_flocks: 10,
      storage_gb: 0.5,
      api_calls_per_hour: 100,
      advanced_analytics: false,
      priority_support: false
    }
  },
  BASIC: {
    code: 'basic',
    name: '基础版',
    price: 99,
    billing_cycle: 'monthly',
    features: {
      max_users: 10,
      max_farms: 1,
      max_flocks: 50,
      storage_gb: 2,
      api_calls_per_hour: 500,
      advanced_analytics: true,
      priority_support: false
    }
  },
  STANDARD: {
    code: 'standard',
    name: '标准版',
    price: 299,
    billing_cycle: 'monthly',
    features: {
      max_users: 50,
      max_farms: 3,
      max_flocks: 200,
      storage_gb: 10,
      api_calls_per_hour: 2000,
      advanced_analytics: true,
      priority_support: false
    }
  },
  PREMIUM: {
    code: 'premium',
    name: '高级版', 
    price: 599,
    billing_cycle: 'monthly',
    features: {
      max_users: 200,
      max_farms: 10,
      max_flocks: 1000,
      storage_gb: 50,
      api_calls_per_hour: 10000,
      advanced_analytics: true,
      priority_support: true
    }
  },
  ENTERPRISE: {
    code: 'enterprise',
    name: '企业版',
    price: 1999,
    billing_cycle: 'monthly',
    features: {
      max_users: 1000,
      max_farms: 50,
      max_flocks: 10000,
      storage_gb: 200,
      api_calls_per_hour: 100000,
      advanced_analytics: true,
      priority_support: true,
      custom_features: true
    }
  }
};

// ==================== 业务常量 ====================
const BUSINESS_CONSTANTS = {
  // 用户角色
  USER_ROLES: {
    PLATFORM_ADMIN: 'platform_admin',
    TENANT_OWNER: 'owner',
    TENANT_ADMIN: 'admin', 
    TENANT_MANAGER: 'manager',
    TENANT_OPERATOR: 'operator',
    TENANT_VIEWER: 'viewer'
  },

  // 租户状态
  TENANT_STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    SUSPENDED: 'suspended',
    DELETED: 'deleted'
  },

  // 订阅状态
  SUBSCRIPTION_STATUS: {
    ACTIVE: 'active',
    EXPIRED: 'expired',
    SUSPENDED: 'suspended',
    CANCELLED: 'cancelled'
  },

  // 支付状态
  PAYMENT_STATUS: {
    PENDING: 'pending',
    COMPLETED: 'completed',
    FAILED: 'failed',
    REFUNDED: 'refunded'
  },

  // 鹅群健康状态
  FLOCK_HEALTH_STATUS: {
    HEALTHY: 'healthy',
    SICK: 'sick',
    QUARANTINE: 'quarantine',
    RECOVERING: 'recovering',
    DEAD: 'dead'
  },

  // 库存操作类型
  INVENTORY_TRANSACTION_TYPES: {
    IN: 'in',           // 入库
    OUT: 'out',         // 出库  
    ADJUST: 'adjust',   // 调整
    EXPIRED: 'expired', // 过期
    DAMAGED: 'damaged'  // 损坏
  },

  // 财务记录类型
  FINANCIAL_CATEGORIES: {
    INCOME: 'income',   // 收入
    EXPENSE: 'expense'  // 支出
  }
};

// ==================== UI界面配置 ====================
const UI_CONFIG = {
  // 分页配置
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    SHOW_TOTAL: true,
    SHOW_QUICK_JUMPER: true
  },

  // 表格配置
  TABLE: {
    DEFAULT_SORT: 'created_at',
    DEFAULT_ORDER: 'desc',
    SHOW_SELECTION: true,
    SHOW_EXPANSION: false
  },

  // 上传配置
  UPLOAD: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    UPLOAD_DIR: 'uploads'
  },

  // 主题配置
  THEME: {
    PRIMARY_COLOR: '#1890ff',
    SUCCESS_COLOR: '#52c41a',
    WARNING_COLOR: '#faad14',
    ERROR_COLOR: '#ff4d4f'
  }
};

// ==================== 安全配置 ====================
const SECURITY_CONFIG = {
  // 密码要求
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: false,
    REQUIRE_LOWERCASE: false,
    REQUIRE_NUMBERS: true,
    REQUIRE_SYMBOLS: false
  },

  // 会话配置
  SESSION: {
    SECRET: process.env.SESSION_SECRET || 'smart-goose-saas-secret-2024',
    TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
    ROLLING: true
  },

  // API密钥配置
  API_KEY: {
    PREFIX: 'sk_',
    LENGTH: 48,
    SECRET_LENGTH: 64,
    DEFAULT_EXPIRY_DAYS: 365
  }
};

// ==================== 错误码配置 ====================
const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: { code: 'E0001', message: '未知错误' },
  INVALID_PARAMS: { code: 'E0002', message: '参数错误' },
  UNAUTHORIZED: { code: 'E0003', message: '未授权访问' },
  FORBIDDEN: { code: 'E0004', message: '权限不足' },
  NOT_FOUND: { code: 'E0005', message: '资源不存在' },
  
  // 租户相关错误
  TENANT_NOT_FOUND: { code: 'T0001', message: '租户不存在' },
  TENANT_INACTIVE: { code: 'T0002', message: '租户未激活' },
  TENANT_SUSPENDED: { code: 'T0003', message: '租户已暂停' },
  TENANT_QUOTA_EXCEEDED: { code: 'T0004', message: '租户配额超限' },
  
  // 用户相关错误
  USER_NOT_FOUND: { code: 'U0001', message: '用户不存在' },
  INVALID_CREDENTIALS: { code: 'U0002', message: '用户名或密码错误' },
  USER_INACTIVE: { code: 'U0003', message: '用户未激活' },
  
  // 业务相关错误
  FLOCK_NOT_FOUND: { code: 'B0001', message: '鹅群不存在' },
  FARM_NOT_FOUND: { code: 'B0002', message: '养殖场不存在' },
  INSUFFICIENT_INVENTORY: { code: 'B0003', message: '库存不足' },
  
  // 系统相关错误
  DATABASE_ERROR: { code: 'S0001', message: '数据库错误' },
  NETWORK_ERROR: { code: 'S0002', message: '网络错误' },
  SERVICE_UNAVAILABLE: { code: 'S0003', message: '服务不可用' }
};

// ==================== 微信小程序配置 ====================
const WECHAT_CONFIG = {
  APP_ID: process.env.WECHAT_APP_ID || '',
  APP_SECRET: process.env.WECHAT_APP_SECRET || '',
  
  // 小程序页面路径
  PAGES: {
    INDEX: 'pages/index/index',
    LOGIN: 'pages/auth/login',
    DASHBOARD: 'pages/dashboard/index',
    FARMS: 'pages/farms/index',
    FLOCKS: 'pages/flocks/index',
    HEALTH: 'pages/health/index',
    PRODUCTION: 'pages/production/index'
  },

  // 小程序权限
  SCOPES: {
    USER_INFO: 'scope.userInfo',
    USER_LOCATION: 'scope.userLocation',
    CAMERA: 'scope.camera',
    ALBUM: 'scope.writePhotosAlbum'
  }
};

// ==================== 导出配置 ====================
module.exports = {
  PLATFORM_CONFIG,
  API_CONFIG,
  SUBSCRIPTION_PLANS,
  BUSINESS_CONSTANTS,
  UI_CONFIG,
  SECURITY_CONFIG,
  ERROR_CODES,
  WECHAT_CONFIG,

  // 版本信息
  VERSION: '2.0.0',
  BUILD_DATE: new Date().toISOString(),
  
  // 兼容性函数
  getConfig: (section) => {
    const configs = {
      platform: PLATFORM_CONFIG,
      api: API_CONFIG,
      business: BUSINESS_CONSTANTS,
      ui: UI_CONFIG,
      security: SECURITY_CONFIG,
      wechat: WECHAT_CONFIG
    };
    return configs[section] || null;
  }
};