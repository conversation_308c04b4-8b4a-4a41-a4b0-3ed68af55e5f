/**
 * API相关常量配置
 * 统一管理API端点、版本、请求配置等
 */

// 环境配置
const ENV = {
  DEVELOPMENT: 'development',
  TESTING: 'testing', 
  STAGING: 'staging',
  PRODUCTION: 'production'
};

// 当前环境（根据实际部署调整）
const CURRENT_ENV = ENV.DEVELOPMENT;

// 环境对应的API基础地址
const BASE_URLS = {
  [ENV.DEVELOPMENT]: 'http://localhost:3001',
  [ENV.TESTING]: 'https://test-api.zhihuiyange.com',
  [ENV.STAGING]: 'https://staging-api.zhihuiyange.com',
  [ENV.PRODUCTION]: 'https://api.zhihuiyange.com'
};

// API版本配置 - 支持微信小程序规范
const API_VERSIONS = {
  V1: '/api/v1',        // 兼容版本
  V2: '/api/v2',        // 标准版本（推荐）
  CURRENT: '/api/v2',   // 当前推荐版本
  TENANT: '/api/v1/tenant',  // 租户相关API（逐步迁移）
  ADMIN: '/api/v1/admin'     // 管理员API（逐步迁移）
};

// 获取当前环境的基础URL
const BASE_URL = BASE_URLS[CURRENT_ENV];

// 构建完整API路径的辅助函数
const buildApiUrl = (version, path) => `${BASE_URL}${version}${path}`;

// API端点配置
const ENDPOINTS = {
  // ===== 认证相关 (V2 - 微信小程序标准) =====
  AUTH: {
    LOGIN: buildApiUrl(API_VERSIONS.CURRENT, '/auth/login'),
    REGISTER: buildApiUrl(API_VERSIONS.CURRENT, '/auth/register'),
    LOGOUT: buildApiUrl(API_VERSIONS.CURRENT, '/auth/logout'),
    REFRESH_TOKEN: buildApiUrl(API_VERSIONS.CURRENT, '/auth/refresh'),
    USER_INFO: buildApiUrl(API_VERSIONS.CURRENT, '/auth/user-info'),      // 标准化命名
    CHANGE_PASSWORD: buildApiUrl(API_VERSIONS.CURRENT, '/auth/password'),
    VERIFY_CODE: buildApiUrl(API_VERSIONS.CURRENT, '/auth/verify-code'),
    RESET_PASSWORD: buildApiUrl(API_VERSIONS.CURRENT, '/auth/reset-password'),
    WECHAT_LOGIN: buildApiUrl(API_VERSIONS.CURRENT, '/auth/wechat-login')
  },

  // ===== 鹅群管理 (V2 - 新版TypeScript API) =====
  FLOCKS: {
    BASE: buildApiUrl(API_VERSIONS.V2, '/flocks'),
    LIST: buildApiUrl(API_VERSIONS.V2, '/flocks'),
    CREATE: buildApiUrl(API_VERSIONS.V2, '/flocks'),
    DETAIL: (id) => buildApiUrl(API_VERSIONS.V2, `/flocks/${id}`),
    UPDATE: (id) => buildApiUrl(API_VERSIONS.V2, `/flocks/${id}/update`),
    DELETE: (id) => buildApiUrl(API_VERSIONS.V2, `/flocks/${id}`),
    STATS: (id) => buildApiUrl(API_VERSIONS.V2, `/flocks/${id}/stats`),
    BATCH_OPERATIONS: buildApiUrl(API_VERSIONS.V2, '/flocks/batch')
  },

  // ===== 生产管理 (V2 - 包含健康监测功能) =====
  PRODUCTION: {
    RECORDS: buildApiUrl(API_VERSIONS.V2, '/production/records'),
    CREATE_RECORD: buildApiUrl(API_VERSIONS.V2, '/production/records'),
    RECORD_DETAIL: (id) => buildApiUrl(API_VERSIONS.V2, `/production/records/${id}`),
    UPDATE_RECORD: (id) => buildApiUrl(API_VERSIONS.V2, `/production/records/${id}`),
    DELETE_RECORD: (id) => buildApiUrl(API_VERSIONS.V2, `/production/records/${id}`),
    TRENDS: buildApiUrl(API_VERSIONS.V2, '/production/trends'),
    STATS: buildApiUrl(API_VERSIONS.V2, '/production/stats'),
    
    // 健康监测功能（整合到生产管理中）
    KNOWLEDGE: buildApiUrl(API_VERSIONS.V1, '/production/knowledge'),
    AI_DIAGNOSIS: buildApiUrl(API_VERSIONS.V1, '/production/ai-diagnosis'),
    REPORTS: buildApiUrl(API_VERSIONS.V1, '/production/reports'),
    
    // 生产相关子模块 (V1 - 兼容旧版)
    ENVIRONMENT: buildApiUrl(API_VERSIONS.V1, '/production/environment'),
    FINANCE: buildApiUrl(API_VERSIONS.V1, '/production/finance'),

    REIMBURSEMENT_REQUESTS: buildApiUrl(API_VERSIONS.V1, '/production/reimbursement-requests'),
    AI_RECOGNITION: buildApiUrl(API_VERSIONS.V1, '/production/ai-recognition'),
    AI_INVENTORY: buildApiUrl(API_VERSIONS.V1, '/production/ai-inventory')
  },

  // ===== 物料管理 (V2 - 统一库存模型，符合微信小程序标准) =====
  MATERIALS: {
    BASE: buildApiUrl(API_VERSIONS.CURRENT, '/materials'),
    LIST: buildApiUrl(API_VERSIONS.CURRENT, '/materials'),
    CREATE: buildApiUrl(API_VERSIONS.CURRENT, '/materials'),
    DETAIL: (id) => buildApiUrl(API_VERSIONS.CURRENT, `/materials/${id}`),
    UPDATE: (id) => buildApiUrl(API_VERSIONS.CURRENT, `/materials/${id}`),
    DELETE: (id) => buildApiUrl(API_VERSIONS.CURRENT, `/materials/${id}`),
    LOW_STOCK_ALERTS: buildApiUrl(API_VERSIONS.CURRENT, '/materials/low-stock'),
    CATEGORIES: buildApiUrl(API_VERSIONS.CURRENT, '/materials/categories'),
    BATCH_UPDATE: buildApiUrl(API_VERSIONS.CURRENT, '/materials/batch-update'),
    
    // V1 兼容性别名（逐步废弃）
    INVENTORY: buildApiUrl(API_VERSIONS.V2, '/inventory')
  },

  // ===== AI服务 =====
  AI: {
    CHAT: buildApiUrl(API_VERSIONS.V1, '/ai/chat'),
    IMAGE_RECOGNITION: buildApiUrl(API_VERSIONS.V1, '/ai/image'),
    KNOWLEDGE_QA: buildApiUrl(API_VERSIONS.V1, '/ai/knowledge-qa'),
    FINANCE_ANALYSIS: buildApiUrl(API_VERSIONS.V1, '/ai/finance'),
    CONTENT_RECOMMEND: buildApiUrl(API_VERSIONS.V1, '/ai/recommend'),
    PRODUCTION_DIAGNOSIS: buildApiUrl(API_VERSIONS.V1, '/ai/production'),
    CONFIG: buildApiUrl(API_VERSIONS.V1, '/ai/config'),
    STATISTICS: buildApiUrl(API_VERSIONS.V1, '/ai/statistics')
  },

  // ===== 首页相关 (V2 - 微信小程序标准) =====
  HOME: {
    DASHBOARD: buildApiUrl(API_VERSIONS.CURRENT, '/home/<USER>'),        // 主要仪表板数据
    ANNOUNCEMENTS: buildApiUrl(API_VERSIONS.CURRENT, '/home/<USER>'),
    WEATHER: buildApiUrl(API_VERSIONS.CURRENT, '/home/<USER>'),
    QUICK_STATS: buildApiUrl(API_VERSIONS.CURRENT, '/home/<USER>'),          // 标准化命名
    
    // V1 兼容性别名（逐步废弃）
    DATA: buildApiUrl(API_VERSIONS.V1, '/home/<USER>')
  },

  // ===== 个人中心 =====
  PROFILE: {
    SETTINGS: buildApiUrl(API_VERSIONS.V1, '/profile/settings'),
    HELP: buildApiUrl(API_VERSIONS.V1, '/profile/help'),
    FEEDBACK: buildApiUrl(API_VERSIONS.V1, '/profile/feedback'),
    ABOUT: buildApiUrl(API_VERSIONS.V1, '/profile/about')
  },

  // ===== 商城相关 =====
  SHOP: {
    PRODUCTS: buildApiUrl(API_VERSIONS.V1, '/shop/products'),
    CATEGORIES: buildApiUrl(API_VERSIONS.V1, '/shop/categories'),
    CART: buildApiUrl(API_VERSIONS.V1, '/shop/cart'),
    ORDERS: buildApiUrl(API_VERSIONS.V1, '/shop/orders'),
    PAYMENT: buildApiUrl(API_VERSIONS.V1, '/shop/payment')
  },

  // ===== 系统设置 =====
  SYSTEM: {
    SETTINGS: buildApiUrl(API_VERSIONS.V1, '/settings'),
    FEEDBACK: buildApiUrl(API_VERSIONS.V1, '/feedback'),
    VERSION_CHECK: buildApiUrl(API_VERSIONS.V1, '/system/version'),
    CONFIG: buildApiUrl(API_VERSIONS.V1, '/system/config')
  },

  // ===== 管理功能相关 =====
  MANAGEMENT: {
    // 管理仪表板
    OVERVIEW: buildApiUrl(API_VERSIONS.TENANT, '/management/overview'),
    PENDING_TASKS: buildApiUrl(API_VERSIONS.TENANT, '/management/pending-tasks'),
    RECENT_ACTIVITIES: buildApiUrl(API_VERSIONS.TENANT, '/management/recent-activities'),
    
    // 实时监控
    MONITOR: buildApiUrl(API_VERSIONS.TENANT, '/management/monitor'),
    ALERTS: buildApiUrl(API_VERSIONS.TENANT, '/management/alerts'),
    EQUIPMENT_STATUS: buildApiUrl(API_VERSIONS.TENANT, '/management/equipment'),
    
    // 报表数据
    REPORTS: buildApiUrl(API_VERSIONS.TENANT, '/management/reports'),
    EXPORT: buildApiUrl(API_VERSIONS.TENANT, '/management/export'),
    
    // 系统设置
    SETTINGS: buildApiUrl(API_VERSIONS.TENANT, '/management/settings'),
    PERMISSIONS: buildApiUrl(API_VERSIONS.TENANT, '/management/permissions')
  },

  // ===== 员工管理相关 =====
  STAFF: {
    LIST: buildApiUrl(API_VERSIONS.TENANT, '/staff'),
    DETAIL: buildApiUrl(API_VERSIONS.TENANT, '/staff/detail'),
    CREATE: buildApiUrl(API_VERSIONS.TENANT, '/staff'),
    UPDATE: buildApiUrl(API_VERSIONS.TENANT, '/staff'),
    DELETE: buildApiUrl(API_VERSIONS.TENANT, '/staff'),
    STATS: buildApiUrl(API_VERSIONS.TENANT, '/staff/stats'),
    ATTENDANCE: buildApiUrl(API_VERSIONS.TENANT, '/staff/attendance'),
    PERMISSIONS: buildApiUrl(API_VERSIONS.TENANT, '/staff/permissions'),
    SEARCH: buildApiUrl(API_VERSIONS.TENANT, '/staff/search'),
    EXPORT: buildApiUrl(API_VERSIONS.TENANT, '/staff/export')
  },

  // ===== 审批流程相关 =====
  APPROVAL: {
    LIST: buildApiUrl(API_VERSIONS.TENANT, '/approvals'),
    DETAIL: buildApiUrl(API_VERSIONS.TENANT, '/approvals/detail'),
    SUBMIT: buildApiUrl(API_VERSIONS.TENANT, '/approvals'),
    APPROVE: buildApiUrl(API_VERSIONS.TENANT, '/approvals/approve'),
    REJECT: buildApiUrl(API_VERSIONS.TENANT, '/approvals/reject'),
    WITHDRAW: buildApiUrl(API_VERSIONS.TENANT, '/approvals/withdraw'),
    HISTORY: buildApiUrl(API_VERSIONS.TENANT, '/approvals/history'),
    STATS: buildApiUrl(API_VERSIONS.TENANT, '/approvals/stats')
  },

  // ===== SAAS平台管理相关 =====
  SAAS_PLATFORM: {
    // 租户管理
    TENANTS: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenants'),
    TENANT_DETAIL: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenants/detail'),
    TENANT_CREATE: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenants'),
    TENANT_UPDATE: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenants'),
    TENANT_DELETE: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenants'),
    TENANT_SUSPEND: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenants/suspend'),
    TENANT_ACTIVATE: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenants/activate'),
    
    // 跨租户数据查看 - 核心功能
    CROSS_TENANT_DATA: buildApiUrl(API_VERSIONS.ADMIN, '/saas/cross-tenant-data'),
    TENANT_BUSINESS_OVERVIEW: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenant-business'),
    TENANT_PRODUCTION_DATA: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenant-production'),
    TENANT_FINANCIAL_DATA: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenant-financial'),
    TENANT_STAFF_DATA: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenant-staff'),
    
    // 平台统计和分析
    PLATFORM_STATS: buildApiUrl(API_VERSIONS.ADMIN, '/saas/platform-stats'),
    USAGE_ANALYTICS: buildApiUrl(API_VERSIONS.ADMIN, '/saas/usage-analytics'),
    REVENUE_ANALYTICS: buildApiUrl(API_VERSIONS.ADMIN, '/saas/revenue-analytics'),
    TENANT_COMPARISON: buildApiUrl(API_VERSIONS.ADMIN, '/saas/tenant-comparison'),
    
    // 订阅管理
    SUBSCRIPTIONS: buildApiUrl(API_VERSIONS.ADMIN, '/saas/subscriptions'),
    SUBSCRIPTION_PLANS: buildApiUrl(API_VERSIONS.ADMIN, '/saas/subscription-plans'),
    BILLING: buildApiUrl(API_VERSIONS.ADMIN, '/saas/billing'),
    
    // 系统监控
    SYSTEM_HEALTH: buildApiUrl(API_VERSIONS.ADMIN, '/saas/system-health'),
    API_USAGE: buildApiUrl(API_VERSIONS.ADMIN, '/saas/api-usage'),
    ERROR_LOGS: buildApiUrl(API_VERSIONS.ADMIN, '/saas/error-logs')
  },

  // ===== 租户管理 =====
  TENANT: {
    INFO: buildApiUrl(API_VERSIONS.TENANT, '/info'),
    CONFIG: buildApiUrl(API_VERSIONS.TENANT, '/config'),
    SWITCH: buildApiUrl(API_VERSIONS.TENANT, '/switch')
  },

  // ===== OA办公自动化系统 =====
  OA: {
    // 工作流模板
    WORKFLOWS: {
      TEMPLATES: buildApiUrl(API_VERSIONS.V1, '/oa/workflows/templates'),
      TEMPLATE_DETAIL: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/workflows/templates/${id}`),
      TEMPLATE_STATISTICS: buildApiUrl(API_VERSIONS.V1, '/oa/workflows/templates/statistics')
    },

    // 报销管理
    REIMBURSEMENT: {
      LIST: buildApiUrl(API_VERSIONS.V1, '/oa/reimbursements'),
      CREATE: buildApiUrl(API_VERSIONS.V1, '/oa/reimbursements'),
      DETAIL: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/reimbursements/${id}`),
      UPDATE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/reimbursements/${id}`),
      DELETE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/reimbursements/${id}`),
      APPROVE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/reimbursements/${id}/approve`),
      STATISTICS: buildApiUrl(API_VERSIONS.V1, '/oa/reimbursements/statistics')
    },





    // 审批流程 (V2 - 微信小程序标准)
    APPROVALS: {
      LIST: buildApiUrl(API_VERSIONS.CURRENT, '/oa/approvals'),              // 标准化列表接口
      PENDING: buildApiUrl(API_VERSIONS.CURRENT, '/oa/approvals/pending'),
      URGENT: buildApiUrl(API_VERSIONS.CURRENT, '/oa/approvals/urgent'),
      HISTORY: buildApiUrl(API_VERSIONS.CURRENT, '/oa/approvals/history'),
      DETAIL: (id) => buildApiUrl(API_VERSIONS.CURRENT, `/oa/approvals/${id}`),
      APPROVE: (id) => buildApiUrl(API_VERSIONS.CURRENT, `/oa/approvals/${id}/approve`),
      REJECT: (id) => buildApiUrl(API_VERSIONS.CURRENT, `/oa/approvals/${id}/reject`),
      
      // V1 兼容性接口
      APPROVE_V1: buildApiUrl(API_VERSIONS.V1, '/oa/approvals/approve'),
      REJECT_V1: buildApiUrl(API_VERSIONS.V1, '/oa/approvals/reject')
    },

    // 财务管理
    FINANCE: {
      OVERVIEW: buildApiUrl(API_VERSIONS.V1, '/finance/overview'),
      REPORTS: buildApiUrl(API_VERSIONS.V1, '/oa/finance/reports'),
      EXPORT: buildApiUrl(API_VERSIONS.V1, '/oa/finance/export'),
      STATS: buildApiUrl(API_VERSIONS.V1, '/oa/finance/stats'),
      ACTIVITIES: buildApiUrl(API_VERSIONS.V1, '/oa/finance/activities'),
      ALERTS: buildApiUrl(API_VERSIONS.V1, '/oa/finance/alerts'),
      ALERTS_HANDLE: buildApiUrl(API_VERSIONS.V1, '/oa/finance/alerts/handle')
    },

    // 权限管理
    PERMISSIONS: {
      USERS: buildApiUrl(API_VERSIONS.V1, '/oa/permissions/users'),
      USERS_STATS: buildApiUrl(API_VERSIONS.V1, '/oa/permissions/users/statistics'),
      USERS_ROLES: (userId) => buildApiUrl(API_VERSIONS.V1, `/oa/permissions/users/${userId}/roles`),
      ROLES: buildApiUrl(API_VERSIONS.V1, '/oa/permissions/roles'),
      ROLE_PERMISSIONS: (roleId) => buildApiUrl(API_VERSIONS.V1, `/oa/permissions/roles/${roleId}/permissions`),
      ROLE_STATUS: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/permissions/roles/${id}/status`),
      ROLE_PERMISSIONS_SAVE: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/permissions/roles/${id}/permissions`),
      PERMISSIONS_ROOT: buildApiUrl(API_VERSIONS.V1, '/oa/permissions'),
      DEPARTMENTS: buildApiUrl(API_VERSIONS.V1, '/oa/departments')
    },

    // 通知中心
    NOTIFICATIONS: {
      LIST: buildApiUrl(API_VERSIONS.V1, '/oa/notifications'),
      READ: (id) => buildApiUrl(API_VERSIONS.V1, `/oa/notifications/${id}/read`),
      READ_ALL: buildApiUrl(API_VERSIONS.V1, '/oa/notifications/read-all')
    },

    // OA仪表板
    DASHBOARD: {
      STATISTICS: buildApiUrl(API_VERSIONS.V1, '/oa/dashboard/statistics'),
      RECENT_ACTIVITIES: buildApiUrl(API_VERSIONS.V1, '/oa/dashboard/recent-activities')
    }
  }
};

// HTTP状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
};

// 请求方法
const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
};

// 请求头常量
const HEADERS = {
  CONTENT_TYPE: {
    JSON: 'application/json',
    FORM: 'application/x-www-form-urlencoded',
    MULTIPART: 'multipart/form-data'
  },
  AUTHORIZATION: 'Authorization',
  TENANT_CODE: 'X-Tenant-Code',
  CLIENT_VERSION: 'X-Client-Version',
  PLATFORM: 'X-Platform',
  REQUEST_ID: 'X-Request-ID'
};

// 错误类型
const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR'
};

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_TYPES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ERROR_TYPES.SERVER_ERROR]: '服务器异常，请稍后重试',
  [ERROR_TYPES.AUTH_ERROR]: '登录已过期，请重新登录',
  [ERROR_TYPES.BUSINESS_ERROR]: '操作失败，请稍后重试',
  [ERROR_TYPES.VALIDATION_ERROR]: '输入数据有误，请检查后重试'
};

// 请求配置常量
const REQUEST_CONFIG = {
  TIMEOUT: 10000,           // 请求超时时间(ms)
  RETRY_COUNT: 2,           // 重试次数
  RETRY_DELAY: 1000,        // 重试延迟(ms)
  MAX_CONCURRENT: 5,        // 最大并发请求数
  CACHE_TIME: 300000        // 缓存时间(ms) - 5分钟
};

// 向后兼容：HEALTH 别名指向 PRODUCTION（健康监测现在是生产管理的一部分）
ENDPOINTS.HEALTH = ENDPOINTS.PRODUCTION;

// 导出所有API相关常量
module.exports = {
  ENV,
  CURRENT_ENV,
  BASE_URLS,
  BASE_URL,
  API_VERSIONS,
  ENDPOINTS,
  HTTP_STATUS,
  HTTP_METHODS,
  HEADERS,
  ERROR_TYPES,
  ERROR_MESSAGES,
  REQUEST_CONFIG,
  
  // 工具函数
  buildApiUrl
};