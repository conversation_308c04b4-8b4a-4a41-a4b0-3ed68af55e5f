# Page snapshot

```yaml
- generic [ref=e3]:
  - generic [ref=e4]:
    - generic [ref=e5]:
      - img "智慧养鹅" [ref=e6]
      - heading "智慧养鹅SAAS" [level=3] [ref=e7]
      - paragraph [ref=e8]: 管理平台登录
    - generic [ref=e9]:
      - generic [ref=e10]:
        - generic [ref=e11]: 
        - text: /Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/layouts/main.ejs:386 384|
        - 'button ">> 386| 用户头像 388| 389| <%= user ? user.name || user.username : ''管理员'' %> user is not defined" [ref=e12] [cursor=pointer]':
          - text: ">> 386|"
          - img "用户头像" [ref=e13] [cursor=pointer]
          - text: 388|
          - generic [ref=e14] [cursor=pointer]: "389| <%= user ? user.name || user.username : '管理员' %> user is not defined"
      - generic [ref=e15]:
        - generic [ref=e16]:
          - textbox " 用户名/邮箱" [ref=e17]: admin
          - generic:
            - generic: 
            - text: 用户名/邮箱
        - generic [ref=e18]:
          - textbox " 密码" [ref=e19]: admin123
          - generic:
            - generic: 
            - text: 密码
        - generic [ref=e20]:
          - checkbox "记住我" [ref=e21]
          - generic [ref=e22]: 记住我
        - button "登录" [ref=e23] [cursor=pointer]:
          - generic [ref=e24] [cursor=pointer]: 登录
      - generic [ref=e26]: "默认账号: admin / admin123"
  - generic [ref=e28]:
    - link "忘记密码？" [ref=e29] [cursor=pointer]:
      - /url: "#"
    - text: ·
    - link "技术支持" [ref=e30] [cursor=pointer]:
      - /url: "#"
```