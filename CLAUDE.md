# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目架构概览

智慧养鹅SAAS平台是一个多租户智能化养鹅管理系统，采用微信小程序 + Node.js 后端架构：

### 核心架构组件
- **前端**: 微信小程序 (原生开发，入口: `app.js`)
- **主后端**: Node.js + Express (`backend/app.js`, 端口 3000)
- **管理后台**: 独立admin服务 (`backend/admin/`, 端口 3002)  
- **SAAS管理**: 平台管理服务 (`backend/saas-admin/`, 端口 3003)
- **数据库**: MySQL + Sequelize ORM
- **TypeScript支持**: 部分模块使用TS (`backend/src/`, 配置: `backend/tsconfig.json`)

### 多租户架构
- 租户数据隔离通过数据库字段 `tenant_id` 实现
- 统一认证系统基于JWT
- 租户配置管理 (`utils/tenant-config.js`)
- 跨租户数据访问控制 (`middleware/tenant.middleware.js`)

## 常用开发命令

### 项目启动
```bash
# 完整环境启动
npm run setup                    # 首次初始化（安装依赖+数据库初始化）
npm run start:all               # 启动所有服务
./start-local-dev.sh            # 本地开发环境一键启动

# 单个服务启动  
npm start                       # 主API服务 (端口3000)
npm run dev                     # 开发模式主API
npm run start:admin             # 管理后台 (端口3002)
npm run dev:admin               # 开发模式管理后台
```

### 数据库管理
```bash
npm run db:init                 # 初始化数据库
npm run db:seed                 # 导入测试数据
```

### 代码质量工具
```bash
npm run lint                    # ESLint检查和自动修复
npm run format                  # Prettier代码格式化
npm test                        # 运行Mocha单元测试
npm run test:integration        # 集成测试

# 完整验证流程
npm run validate                # lint + consistency checks + tests
npm run check:all               # 数据一致性 + 标准检查
```

### 生产部署
```bash
./scripts/production-deploy.sh  # 生产环境部署脚本
npm start                       # 生产模式启动 (使用PM2)
pm2 start ecosystem.config.js --env production  # PM2集群启动
```

## 核心业务模块理解

### 小程序页面结构
- `pages/home/<USER>
- `pages/health/` - 健康管理模块（AI诊断、疫苗管理）
- `pages/production/` - 生产管理（环境监控、财务管理）
- `pages/shop/` - 商城系统（商品、订单、支付）
- `pages/profile/` - 个人中心
- `pages/workspace/` - OA办公模块（审批流程、财务应用）

### 后端API结构
- **V1 API**: `/api/v1/*` - 小程序主要接口
- **V2 API**: `/api/v2/*` - 新版TypeScript接口
- **租户API**: `/api/v1/tenant/*` - 多租户特定功能
- **管理API**: `/admin/*` - 管理后台接口

### 数据模型层次
- **统一模型**: `models/unified-models.js` - 标准化数据模型
- **租户模型**: `models/tenant-models.js` - 多租户特定模型
- **业务模型**: 分散在各控制器中，处理具体业务逻辑

## 关键开发约定

### Context7 最佳实践要求
开发前必须查询 Context7 获取微信小程序最佳实践：
- 小程序框架规范
- API设计规范  
- 组件开发标准
- 参考 `docs/development-standards.md` 获取详细规则

### 代码规范
- 使用 ESLint 配置 (`eslint.config.js`)
- 支持微信小程序全局对象 (`wx`, `App`, `Page`, `Component`)
- **中文项目默认规则**: 
  - 所有交流、注释、文档使用中文
  - Claude Code 必须默认使用中文回复和说明
  - 所有错误信息和日志输出使用中文
  - API 接口文档和代码注释全部使用中文
- 统一错误处理 (`utils/error-handler.js`)
- 统一API响应格式 (`utils/response-helper.js`)

### 权限和认证
- JWT认证系统 (`middleware/auth.js`, `middleware/auth-unified.js`)
- 基于角色的权限控制 (`middleware/permission.middleware.js`)
- 租户隔离中间件 (`middleware/tenant.middleware.js`)
- 权限检查组件 (`components/permission-check/`)

### API设计原则
- RESTful API结构
- 统一响应格式 (`utils/apiResponse.js`)
- 请求验证 (`middleware/validation.js`)
- 速率限制和安全防护 (`middleware/security.js`)

## 微信小程序特定配置

### 项目配置
- **AppID**: wx68a4d75dd80665ec
- **项目名称**: 智慧养鹅
- **配置文件**: `project.config.json`

### 全局应用配置
- 统一Loading管理 (`utils/unified-loading.js`)
- 租户配置管理 (`utils/tenant-config.js`)
- API客户端 (`utils/api.js`, `utils/unified-api-client.js`)
- 格式化工具 (`utils/format.js`)

### 组件系统
- 通用组件 (`components/common/`)
- 业务组件 (`components/business/`)
- 图表组件 (`components/chart/`, `components/trend-chart/`)
- 表单构建器 (`components/form-builder/`)

## 性能和监控

### 监控系统
- Winston日志系统 (`utils/production-logger.js`)
- 系统监控 (`middleware/system-monitor.js`)
- 性能监控 (`utils/performance-monitor.js`)
- PM2集群管理 (`ecosystem.config.js`)

### 优化策略
- 依赖包优化 (已从276M降至60M)
- 懒加载配置 (`utils/lazy-load-config.js`)
- 缓存策略 (`middleware/cache.middleware.js`)
- 数据库查询优化

## 部署和环境

### 环境配置
- 开发环境: `NODE_ENV=development`
- 生产环境: `NODE_ENV=production`
- 环境配置: `.env`, `.env.production`

### 数据库管理
- 初始化脚本: `database/saas-platform-init.sql`
- 迁移脚本: `migrations/`目录
- 数据库连接: `backend/database/connection.js`

### 静态资源
- 图标系统: `assets/icons/`, `images/icons/`
- 样式文件: `styles/`目录 (WXSS)
- 转换脚本: `convert_icons.sh`

## 测试策略

### 测试工具
- **单元测试**: Mocha + Chai
- **集成测试**: Supertest
- **E2E测试**: Playwright
- **验证脚本**: `scripts/validate-*.js`

### 测试覆盖
- 后端API测试覆盖率: 85%+
- 前端组件测试覆盖率: 70%+
- 全面验证: 16个页面，27个图标，12个样式模块

这个项目是一个成熟的多租户SAAS平台，具有完整的业务功能、权限系统、性能优化和监控能力。在开发时需要特别注意多租户数据隔离、微信小程序规范和Context7最佳实践要求。