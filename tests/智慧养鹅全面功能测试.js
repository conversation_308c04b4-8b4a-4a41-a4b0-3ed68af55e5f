const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅SAAS系统全面功能测试
 * 测试后台管理中心的每个功能模块、按钮和交互链路
 */

test.describe('智慧养鹅后台管理中心全面测试', () => {
  let page;
  const ADMIN_URL = 'http://localhost:4000';
  const API_URL = 'http://localhost:3001';
  
  // 测试用户凭据
  const TEST_CREDENTIALS = {
    username: 'admin',
    password: 'admin123'
  };

  test.beforeAll(async ({ browser }) => {
    page = await browser.newPage();
    
    // 设置超时时间
    page.setDefaultTimeout(10000);
    
    // 监听网络请求
    page.on('response', response => {
      if (response.status() >= 400) {
        console.log(`❌ HTTP错误 ${response.status()}: ${response.url()}`);
      }
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
      console.log(`❌ 页面错误: ${error.message}`);
    });
  });

  test.afterAll(async () => {
    await page?.close();
  });

  test('1. 验证服务状态和健康检查', async () => {
    console.log('🔍 开始验证服务状态...');
    
    // 检查后端API健康状态
    const apiResponse = await page.request.get(`${API_URL}/api/health`);
    expect(apiResponse.status()).toBe(200);
    
    const healthData = await apiResponse.json();
    console.log('✅ 后端API健康状态:', healthData);
    
    // 检查管理后台可访问性
    const response = await page.goto(ADMIN_URL);
    expect(response.status()).toBe(200);
    console.log('✅ 管理后台访问正常');
  });

  test('2. 登录功能测试', async () => {
    console.log('🔍 开始测试登录功能...');
    
    await page.goto(ADMIN_URL);
    
    // 查找登录表单
    const hasLoginForm = await page.locator('input[name="username"], input[type="text"]').count() > 0;
    
    if (hasLoginForm) {
      // 填写登录凭据
      await page.fill('input[name="username"], input[type="text"]', TEST_CREDENTIALS.username);
      await page.fill('input[name="password"], input[type="password"]', TEST_CREDENTIALS.password);
      
      // 点击登录按钮
      await page.click('button[type="submit"], .btn-login, .login-btn');
      
      // 等待页面跳转或响应
      await page.waitForTimeout(2000);
      
      console.log('✅ 登录操作已执行');
    } else {
      console.log('ℹ️ 未发现登录表单，可能已登录或无需登录');
    }
    
    // 检查当前页面状态
    const currentUrl = page.url();
    const pageTitle = await page.title();
    console.log(`📍 当前页面: ${currentUrl}`);
    console.log(`📝 页面标题: ${pageTitle}`);
  });

  test('3. 仪表板模块测试', async () => {
    console.log('🔍 开始测试仪表板模块...');
    
    // 寻找仪表板相关元素
    const dashboardElements = [
      '.dashboard',
      '.main-content',
      '[data-module="dashboard"]',
      '.overview',
      '.statistics'
    ];
    
    let foundDashboard = false;
    for (const selector of dashboardElements) {
      const element = await page.locator(selector).count();
      if (element > 0) {
        console.log(`✅ 发现仪表板元素: ${selector}`);
        foundDashboard = true;
        break;
      }
    }
    
    if (!foundDashboard) {
      console.log('⚠️ 未发现仪表板元素');
    }
    
    // 检查统计数据卡片
    const statCards = await page.locator('.card, .stat-card, .info-box, .metric-card').count();
    console.log(`📊 发现统计卡片数量: ${statCards}`);
    
    if (statCards > 0) {
      await page.locator('.card, .stat-card, .info-box, .metric-card').first().click();
      await page.waitForTimeout(1000);
      console.log('✅ 统计卡片点击测试完成');
    }
  });

  test('4. 用户管理模块测试', async () => {
    console.log('🔍 开始测试用户管理模块...');
    
    // 寻找用户管理入口
    const userManagementSelectors = [
      'a[href*="user"]',
      'a[href*="member"]',
      '.nav-link:has-text("用户")',
      '.menu-item:has-text("用户管理")',
      '[data-module="users"]'
    ];
    
    let userModuleFound = false;
    for (const selector of userManagementSelectors) {
      try {
        const element = await page.locator(selector).count();
        if (element > 0) {
          await page.locator(selector).first().click();
          await page.waitForTimeout(2000);
          console.log(`✅ 成功进入用户管理模块: ${selector}`);
          userModuleFound = true;
          break;
        }
      } catch (error) {
        continue;
      }
    }
    
    if (userModuleFound) {
      // 测试用户列表
      const userTable = await page.locator('table, .user-list, .data-table').count();
      console.log(`📋 用户列表表格数量: ${userTable}`);
      
      // 测试添加用户按钮
      const addButtons = ['.btn-add', '.add-btn', 'button:has-text("添加")', 'button:has-text("新增")'];
      for (const btnSelector of addButtons) {
        const buttonCount = await page.locator(btnSelector).count();
        if (buttonCount > 0) {
          await page.locator(btnSelector).first().click();
          await page.waitForTimeout(1000);
          console.log(`✅ 添加用户按钮测试完成: ${btnSelector}`);
          
          // 关闭可能打开的模态框
          const closeButtons = ['.close', '.btn-close', '.modal-close', '[data-dismiss="modal"]'];
          for (const closeBtn of closeButtons) {
            const closeCount = await page.locator(closeBtn).count();
            if (closeCount > 0) {
              await page.locator(closeBtn).first().click();
              break;
            }
          }
          break;
        }
      }
    } else {
      console.log('⚠️ 未找到用户管理模块');
    }
  });

  test('5. 鹅群管理模块测试', async () => {
    console.log('🔍 开始测试鹅群管理模块...');
    
    const flockSelectors = [
      'a[href*="flock"]',
      'a[href*="goose"]',
      '.nav-link:has-text("鹅群")',
      '.menu-item:has-text("鹅群管理")',
      '[data-module="flocks"]'
    ];
    
    let flockModuleFound = false;
    for (const selector of flockSelectors) {
      try {
        const element = await page.locator(selector).count();
        if (element > 0) {
          await page.locator(selector).first().click();
          await page.waitForTimeout(2000);
          console.log(`✅ 成功进入鹅群管理模块: ${selector}`);
          flockModuleFound = true;
          break;
        }
      } catch (error) {
        continue;
      }
    }
    
    if (flockModuleFound) {
      // 测试鹅群相关按钮
      const actionButtons = await page.locator('button, .btn, .action-btn').count();
      console.log(`🔘 发现动作按钮数量: ${actionButtons}`);
      
      // 测试前几个按钮
      const buttons = await page.locator('button:visible, .btn:visible').all();
      for (let i = 0; i < Math.min(3, buttons.length); i++) {
        try {
          await buttons[i].click();
          await page.waitForTimeout(1000);
          console.log(`✅ 按钮 ${i + 1} 点击测试完成`);
        } catch (error) {
          console.log(`⚠️ 按钮 ${i + 1} 点击失败: ${error.message}`);
        }
      }
    }
  });

  test('6. 生产记录模块测试', async () => {
    console.log('🔍 开始测试生产记录模块...');
    
    const productionSelectors = [
      'a[href*="production"]',
      'a[href*="record"]',
      '.nav-link:has-text("生产")',
      '.menu-item:has-text("生产记录")',
      '[data-module="production"]'
    ];
    
    for (const selector of productionSelectors) {
      try {
        const element = await page.locator(selector).count();
        if (element > 0) {
          await page.locator(selector).first().click();
          await page.waitForTimeout(2000);
          console.log(`✅ 成功进入生产记录模块: ${selector}`);
          
          // 测试数据表格
          const tables = await page.locator('table').count();
          console.log(`📊 数据表格数量: ${tables}`);
          
          break;
        }
      } catch (error) {
        continue;
      }
    }
  });

  test('7. 健康管理模块测试', async () => {
    console.log('🔍 开始测试健康管理模块...');
    
    const healthSelectors = [
      'a[href*="health"]',
      'a[href*="medical"]',
      '.nav-link:has-text("健康")',
      '.menu-item:has-text("健康管理")',
      '[data-module="health"]'
    ];
    
    for (const selector of healthSelectors) {
      try {
        const element = await page.locator(selector).count();
        if (element > 0) {
          await page.locator(selector).first().click();
          await page.waitForTimeout(2000);
          console.log(`✅ 成功进入健康管理模块: ${selector}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }
  });

  test('8. 系统设置模块测试', async () => {
    console.log('🔍 开始测试系统设置模块...');
    
    const settingsSelectors = [
      'a[href*="setting"]',
      'a[href*="config"]',
      '.nav-link:has-text("设置")',
      '.menu-item:has-text("系统设置")',
      '[data-module="settings"]'
    ];
    
    for (const selector of settingsSelectors) {
      try {
        const element = await page.locator(selector).count();
        if (element > 0) {
          await page.locator(selector).first().click();
          await page.waitForTimeout(2000);
          console.log(`✅ 成功进入系统设置模块: ${selector}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }
  });

  test('9. API端点测试', async () => {
    console.log('🔍 开始测试API端点...');
    
    const apiEndpoints = [
      '/api/health',
      '/api/v1/users',
      '/api/v1/flocks',
      '/api/v1/production-records',
      '/api/v1/health'
    ];
    
    const results = [];
    
    for (const endpoint of apiEndpoints) {
      try {
        const response = await page.request.get(`${API_URL}${endpoint}`);
        const status = response.status();
        const result = {
          endpoint,
          status,
          success: status < 400
        };
        
        if (result.success) {
          console.log(`✅ API ${endpoint}: ${status}`);
        } else {
          console.log(`❌ API ${endpoint}: ${status}`);
        }
        
        results.push(result);
      } catch (error) {
        console.log(`💥 API ${endpoint}: ${error.message}`);
        results.push({
          endpoint,
          status: 'error',
          success: false,
          error: error.message
        });
      }
    }
    
    console.log('\n📊 API测试结果汇总:');
    results.forEach(result => {
      const emoji = result.success ? '✅' : '❌';
      console.log(`${emoji} ${result.endpoint}: ${result.status}`);
    });
  });

  test('10. 响应式设计测试', async () => {
    console.log('🔍 开始测试响应式设计...');
    
    const viewports = [
      { width: 1920, height: 1080, name: '桌面端' },
      { width: 1024, height: 768, name: '平板端' },
      { width: 375, height: 667, name: '移动端' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      // 检查页面是否正常显示
      const body = await page.locator('body').isVisible();
      expect(body).toBe(true);
      
      console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}) 显示正常`);
    }
  });

  test('11. 综合交互流程测试', async () => {
    console.log('🔍 开始综合交互流程测试...');
    
    // 重置到桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    // 获取所有可见的链接和按钮
    const interactiveElements = await page.locator('a:visible, button:visible, [role="button"]:visible').all();
    console.log(`🔗 发现可交互元素数量: ${interactiveElements.length}`);
    
    let successfulClicks = 0;
    let failedClicks = 0;
    
    // 测试前10个元素
    for (let i = 0; i < Math.min(10, interactiveElements.length); i++) {
      try {
        const element = interactiveElements[i];
        const tagName = await element.evaluate(el => el.tagName.toLowerCase());
        const text = await element.textContent() || '';
        const href = await element.getAttribute('href') || '';
        
        console.log(`🎯 测试元素 ${i + 1}: ${tagName} "${text.trim().substring(0, 20)}..." ${href}`);
        
        await element.click();
        await page.waitForTimeout(1000);
        
        successfulClicks++;
        console.log(`✅ 元素 ${i + 1} 点击成功`);
      } catch (error) {
        failedClicks++;
        console.log(`❌ 元素 ${i + 1} 点击失败: ${error.message}`);
      }
    }
    
    console.log(`\n📊 交互测试结果:`);
    console.log(`✅ 成功: ${successfulClicks}`);
    console.log(`❌ 失败: ${failedClicks}`);
    console.log(`📈 成功率: ${((successfulClicks / (successfulClicks + failedClicks)) * 100).toFixed(1)}%`);
  });

  test('12. 表单功能测试', async () => {
    console.log('🔍 开始测试表单功能...');
    
    const forms = await page.locator('form').all();
    console.log(`📝 发现表单数量: ${forms.length}`);
    
    for (let i = 0; i < Math.min(3, forms.length); i++) {
      try {
        const form = forms[i];
        const inputs = await form.locator('input, textarea, select').all();
        
        console.log(`📝 表单 ${i + 1} 包含 ${inputs.length} 个输入字段`);
        
        // 填写测试数据
        for (const input of inputs) {
          const type = await input.getAttribute('type') || '';
          const name = await input.getAttribute('name') || '';
          
          if (type === 'text' || type === 'email') {
            await input.fill('<EMAIL>');
          } else if (type === 'password') {
            await input.fill('test123');
          } else if (type === 'number') {
            await input.fill('123');
          }
        }
        
        console.log(`✅ 表单 ${i + 1} 填写完成`);
      } catch (error) {
        console.log(`❌ 表单 ${i + 1} 测试失败: ${error.message}`);
      }
    }
  });
});

// 生成测试报告
test.afterAll(async () => {
  const reportData = {
    timestamp: new Date().toISOString(),
    testSuite: '智慧养鹅后台管理中心全面测试',
    summary: '已完成所有功能模块的基础测试',
    recommendation: '建议进一步完善具体业务流程的测试'
  };
  
  console.log('\n' + '='.repeat(60));
  console.log('📋 测试完成报告');
  console.log('='.repeat(60));
  console.log(`⏰ 测试时间: ${reportData.timestamp}`);
  console.log(`🎯 测试范围: ${reportData.testSuite}`);
  console.log(`📊 测试结果: ${reportData.summary}`);
  console.log(`💡 建议: ${reportData.recommendation}`);
  console.log('='.repeat(60));
});