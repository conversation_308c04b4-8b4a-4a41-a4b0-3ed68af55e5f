#!/usr/bin/env node

/**
 * 智慧养鹅SAAS系统健康检查工具
 * 用于验证系统的整体健康状态和功能完整性
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
  services: {
    backend: 'http://localhost:3001',
    saasAdmin: 'http://localhost:3002'
  },
  timeout: 10000,
  retries: 3
};

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 健康检查结果
const healthReport = {
  timestamp: new Date().toISOString(),
  services: {},
  apis: {},
  database: {},
  overall: {
    status: 'unknown',
    score: 0,
    issues: []
  }
};

// 服务健康检查
async function checkServiceHealth(name, url) {
  log(`\n🔍 检查服务: ${name} (${url})`, 'blue');
  
  try {
    const startTime = Date.now();
    const response = await axios.get(url, { 
      timeout: CONFIG.timeout,
      validateStatus: () => true // 接受所有状态码
    });
    const responseTime = Date.now() - startTime;
    
    const isHealthy = response.status >= 200 && response.status < 400;
    
    healthReport.services[name] = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      responseTime,
      statusCode: response.status,
      url
    };
    
    if (isHealthy) {
      log(`✅ ${name} 服务健康 (${response.status}, ${responseTime}ms)`, 'green');
      return true;
    } else {
      log(`❌ ${name} 服务异常 (${response.status}, ${responseTime}ms)`, 'red');
      healthReport.overall.issues.push(`${name} 服务返回状态码 ${response.status}`);
      return false;
    }
    
  } catch (error) {
    log(`❌ ${name} 服务连接失败: ${error.message}`, 'red');
    healthReport.services[name] = {
      status: 'error',
      error: error.message,
      url
    };
    healthReport.overall.issues.push(`${name} 服务连接失败: ${error.message}`);
    return false;
  }
}

// API端点检查
async function checkAPIEndpoints() {
  log('\n🔍 检查API端点', 'blue');
  
  const endpoints = [
    { name: '健康检查', url: `${CONFIG.services.backend}/api/health` },
    { name: '用户API', url: `${CONFIG.services.backend}/api/v1/users` },
    { name: '鹅群API', url: `${CONFIG.services.backend}/api/v1/flocks` },
    { name: '生产记录API', url: `${CONFIG.services.backend}/api/v1/production` }
  ];
  
  let healthyEndpoints = 0;
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(endpoint.url, { 
        timeout: 5000,
        validateStatus: () => true
      });
      
      // 对于需要认证的端点，401是正常的
      const isHealthy = response.status < 500 && response.status !== 404;
      
      healthReport.apis[endpoint.name] = {
        status: isHealthy ? 'accessible' : 'error',
        statusCode: response.status,
        url: endpoint.url
      };
      
      if (isHealthy) {
        log(`✅ ${endpoint.name} 可访问 (${response.status})`, 'green');
        healthyEndpoints++;
      } else {
        log(`❌ ${endpoint.name} 不可访问 (${response.status})`, 'red');
        healthReport.overall.issues.push(`${endpoint.name} API不可访问`);
      }
      
    } catch (error) {
      log(`❌ ${endpoint.name} 连接失败: ${error.message}`, 'red');
      healthReport.apis[endpoint.name] = {
        status: 'error',
        error: error.message,
        url: endpoint.url
      };
      healthReport.overall.issues.push(`${endpoint.name} API连接失败`);
    }
  }
  
  return healthyEndpoints / endpoints.length;
}

// 数据库连接检查
async function checkDatabaseConnection() {
  log('\n🔍 检查数据库连接', 'blue');
  
  try {
    // 通过后端API检查数据库连接
    const response = await axios.get(`${CONFIG.services.backend}/api/health`, {
      timeout: 5000
    });
    
    if (response.data && response.data.success) {
      log('✅ 数据库连接正常', 'green');
      healthReport.database = {
        status: 'connected',
        message: '通过API健康检查验证'
      };
      return true;
    } else {
      log('⚠️ 数据库连接状态未知', 'yellow');
      healthReport.database = {
        status: 'unknown',
        message: 'API健康检查未返回明确状态'
      };
      return false;
    }
    
  } catch (error) {
    log(`❌ 数据库连接检查失败: ${error.message}`, 'red');
    healthReport.database = {
      status: 'error',
      error: error.message
    };
    healthReport.overall.issues.push('数据库连接检查失败');
    return false;
  }
}

// 项目结构检查
async function checkProjectStructure() {
  log('\n🔍 检查项目结构', 'blue');
  
  const requiredPaths = [
    'backend/app.js',
    'backend/server.js',
    'backend/saas-admin/server.js',
    'pages/home/<USER>',
    'components',
    'constants',
    'utils',
    'tests/e2e',
    'tests/config'
  ];
  
  let validPaths = 0;
  
  for (const filePath of requiredPaths) {
    const fullPath = path.join(process.cwd(), '..', filePath);
    if (fs.existsSync(fullPath)) {
      log(`✅ ${filePath} 存在`, 'green');
      validPaths++;
    } else {
      log(`❌ ${filePath} 缺失`, 'red');
      healthReport.overall.issues.push(`项目文件缺失: ${filePath}`);
    }
  }
  
  return validPaths / requiredPaths.length;
}

// 生成健康报告
function generateHealthReport() {
  log('\n📊 生成健康报告', 'blue');
  
  // 计算总体健康分数
  const serviceHealth = Object.values(healthReport.services).filter(s => s.status === 'healthy').length / Object.keys(healthReport.services).length;
  const apiHealth = Object.values(healthReport.apis).filter(a => a.status === 'accessible').length / Object.keys(healthReport.apis).length;
  const dbHealth = healthReport.database.status === 'connected' ? 1 : 0;
  
  healthReport.overall.score = Math.round((serviceHealth + apiHealth + dbHealth) / 3 * 100);
  
  if (healthReport.overall.score >= 80) {
    healthReport.overall.status = 'healthy';
  } else if (healthReport.overall.score >= 60) {
    healthReport.overall.status = 'warning';
  } else {
    healthReport.overall.status = 'critical';
  }
  
  // 保存报告
  const reportPath = path.join(__dirname, 'system-health-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(healthReport, null, 2));
  
  // 显示总结
  log('\n' + '='.repeat(60), 'bold');
  log('📋 系统健康检查总结', 'bold');
  log('='.repeat(60), 'bold');
  
  const statusColor = healthReport.overall.status === 'healthy' ? 'green' : 
                     healthReport.overall.status === 'warning' ? 'yellow' : 'red';
  
  log(`总体状态: ${healthReport.overall.status.toUpperCase()}`, statusColor);
  log(`健康分数: ${healthReport.overall.score}%`, statusColor);
  log(`检查时间: ${healthReport.timestamp}`, 'blue');
  
  if (healthReport.overall.issues.length > 0) {
    log('\n⚠️ 发现的问题:', 'yellow');
    healthReport.overall.issues.forEach((issue, index) => {
      log(`${index + 1}. ${issue}`, 'yellow');
    });
  }
  
  log(`\n📄 详细报告已保存到: ${reportPath}`, 'blue');
  
  return healthReport.overall.score >= 60;
}

// 主函数
async function main() {
  log('🚀 开始智慧养鹅SAAS系统健康检查', 'bold');
  log(`检查时间: ${new Date().toLocaleString()}`, 'blue');
  
  try {
    // 1. 检查服务健康
    await checkServiceHealth('后端API服务', CONFIG.services.backend);
    await checkServiceHealth('SAAS管理后台', CONFIG.services.saasAdmin);
    
    // 2. 检查API端点
    await checkAPIEndpoints();
    
    // 3. 检查数据库连接
    await checkDatabaseConnection();
    
    // 4. 检查项目结构
    await checkProjectStructure();
    
    // 5. 生成健康报告
    const isHealthy = generateHealthReport();
    
    // 6. 退出状态
    process.exit(isHealthy ? 0 : 1);
    
  } catch (error) {
    log(`\n❌ 健康检查过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 运行健康检查
if (require.main === module) {
  main();
}

module.exports = { main, checkServiceHealth, checkAPIEndpoints };
