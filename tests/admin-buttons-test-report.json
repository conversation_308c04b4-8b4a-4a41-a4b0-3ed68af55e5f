{"summary": {"totalTests": 9, "passed": 4, "failed": 5, "duration": "19914ms", "timestamp": "2025-08-27T06:56:57.189Z"}, "results": [{"testName": "JS函数-全选租户函数", "status": "FAIL", "error": "函数不存在", "screenshot": null, "timestamp": "2025-08-27T06:57:06.708Z"}, {"testName": "JS函数-切换全选函数", "status": "FAIL", "error": "函数不存在", "screenshot": null, "timestamp": "2025-08-27T06:57:06.718Z"}, {"testName": "JS函数-获取选中租户ID函数", "status": "FAIL", "error": "函数不存在", "screenshot": null, "timestamp": "2025-08-27T06:57:06.719Z"}, {"testName": "JS函数-批量更新状态函数", "status": "FAIL", "error": "函数不存在", "screenshot": null, "timestamp": "2025-08-27T06:57:06.721Z"}, {"testName": "JS函数-导出租户函数", "status": "FAIL", "error": "函数不存在", "screenshot": null, "timestamp": "2025-08-27T06:57:06.722Z"}, {"testName": "控制台错误检查-http://localhost:4001", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:57:09.317Z"}, {"testName": "控制台错误检查-http://localhost:4001/tenants", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:57:11.915Z"}, {"testName": "控制台错误检查-http://localhost:4001/tenants/create", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:57:14.498Z"}, {"testName": "控制台错误检查-http://localhost:4001/tenants/subscriptions", "status": "PASS", "error": null, "screenshot": null, "timestamp": "2025-08-27T06:57:17.086Z"}]}