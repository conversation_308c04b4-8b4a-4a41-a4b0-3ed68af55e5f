const { test, expect } = require('@playwright/test');

// 智慧养鹅SAAS管理后台新架构全面测试
// 测试平台级和租户级管理功能的完整业务流程

test.describe('智慧养鹅SAAS系统新架构测试', () => {
  const baseURL = 'http://localhost:4000';
  
  test.beforeEach(async ({ page }) => {
    // 设置页面超时时间
    page.setDefaultTimeout(30000);
    
    // 访问登录页面
    await page.goto(baseURL);
    
    // 登录系统
    await page.fill('#username', 'admin');
    await page.fill('#password', 'admin123');
    await page.click('button[type="submit"]');
    
    // 等待跳转到仪表板
    await page.waitForURL('**/dashboard');
    await page.waitForLoadState('networkidle');
  });

  test('01. 平台级管理功能 - 仪表板加载', async ({ page }) => {
    // 验证仪表板页面加载成功
    await expect(page.locator('h4:has-text("平台仪表盘")')).toBeVisible();
    
    // 验证侧边栏平台级功能区域存在
    await expect(page.locator('text=平台级管理')).toBeVisible();
    
    // 验证关键统计数据显示
    await expect(page.locator('.content-wrapper')).toBeVisible();
    
    console.log('✅ 平台仪表板加载成功');
  });

  test('02. 平台级管理功能 - 租户管理', async ({ page }) => {
    // 访问租户管理页面
    await page.click('a[href="/tenants"]');
    await page.waitForLoadState('networkidle');
    
    // 验证租户列表页面
    await expect(page.locator('text=租户管理')).toBeVisible();
    await expect(page.locator('text=租户列表')).toBeVisible();
    
    // 验证租户表格或列表存在
    const hasTable = await page.locator('table').isVisible().catch(() => false);
    const hasCardList = await page.locator('.tenant-card').isVisible().catch(() => false);
    
    if (!hasTable && !hasCardList) {
      // 如果没有数据，应该显示空状态提示
      await expect(page.locator('text=暂无数据')).toBeVisible();
    }
    
    console.log('✅ 租户管理页面访问成功');
  });

  test('03. 平台级管理功能 - 今日鹅价管理', async ({ page }) => {
    // 访问今日鹅价页面
    await page.click('a[href="/goose-prices"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=今日鹅价')).toBeVisible();
    
    console.log('✅ 今日鹅价管理页面访问成功');
  });

  test('04. 平台级管理功能 - 平台公告管理', async ({ page }) => {
    // 访问公告管理页面
    await page.click('a[href="/announcements"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=平台公告')).toBeVisible();
    
    console.log('✅ 平台公告管理页面访问成功');
  });

  test('05. 平台级管理功能 - 知识库管理', async ({ page }) => {
    // 访问知识库页面
    await page.click('a[href="/knowledge"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=知识库')).toBeVisible();
    
    console.log('✅ 知识库管理页面访问成功');
  });

  test('06. 平台级管理功能 - 商城管理', async ({ page }) => {
    // 访问商城管理页面
    await page.click('a[href="/mall"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=商城管理')).toBeVisible();
    
    console.log('✅ 商城管理页面访问成功');
  });

  test('07. 平台级管理功能 - AI配置', async ({ page }) => {
    // 访问AI配置页面
    await page.click('a[href="/ai-config"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=AI配置')).toBeVisible();
    
    console.log('✅ AI配置页面访问成功');
  });

  test('08. 平台级管理功能 - 平台用户管理', async ({ page }) => {
    // 先点击租户管理展开子菜单
    const tenantMenu = page.locator('.nav-item:has-text("租户管理")');
    await tenantMenu.click();
    await page.waitForTimeout(500); // 等待子菜单展开
    
    // 访问平台用户页面
    await page.click('a[href="/platform-users"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=平台用户')).toBeVisible();
    
    console.log('✅ 平台用户管理页面访问成功');
  });

  test('09. 系统功能 - 性能监控', async ({ page }) => {
    // 访问性能监控页面
    await page.click('a[href="/monitoring"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=性能监控')).toBeVisible();
    
    console.log('✅ 性能监控页面访问成功');
  });

  test('10. 系统功能 - 报表管理', async ({ page }) => {
    // 访问报表管理页面
    await page.click('a[href="/reports"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=报表管理')).toBeVisible();
    
    console.log('✅ 报表管理页面访问成功');
  });

  test('11. 系统功能 - API文档', async ({ page }) => {
    // 访问API文档页面
    await page.click('a[href="/api-docs"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容（可能重定向到实际API文档）
    const currentURL = page.url();
    expect(currentURL).toContain('api');
    
    console.log('✅ API文档页面访问成功');
  });

  test('12. 系统功能 - 系统设置', async ({ page }) => {
    // 访问系统设置页面
    await page.click('a[href="/settings"]');
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    await expect(page.locator('text=系统设置')).toBeVisible();
    await expect(page.locator('text=基本设置')).toBeVisible();
    await expect(page.locator('text=安全设置')).toBeVisible();
    
    console.log('✅ 系统设置页面访问成功');
  });

  test('13. 导航状态测试 - 活动状态正确显示', async ({ page }) => {
    // 测试导航激活状态
    await page.click('a[href="/dashboard"]');
    await expect(page.locator('.nav-link[href="/dashboard"].active')).toBeVisible();
    
    await page.click('a[href="/tenants"]');
    await expect(page.locator('.nav-link[href="/tenants"].active')).toBeVisible();
    
    console.log('✅ 导航活动状态正常');
  });

  test('14. 权限验证 - 平台管理员权限', async ({ page }) => {
    // 验证所有平台级功能都可访问
    const platformRoutes = [
      '/tenants',
      '/goose-prices', 
      '/announcements',
      '/knowledge',
      '/mall',
      '/ai-config',
      '/settings'
    ];
    
    for (const route of platformRoutes) {
      await page.goto(baseURL + route);
      await page.waitForLoadState('networkidle');
      
      // 验证不是错误页面
      const hasError = await page.locator('text=权限不足').isVisible().catch(() => false);
      const has404 = await page.locator('text=页面不存在').isVisible().catch(() => false);
      
      expect(hasError).toBe(false);
      expect(has404).toBe(false);
    }
    
    console.log('✅ 平台管理员权限验证通过');
  });

  test('15. 响应式布局测试', async ({ page }) => {
    // 测试不同屏幕尺寸下的布局
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto(baseURL + '/dashboard');
    await expect(page.locator('.main-sidebar')).toBeVisible();
    
    // 测试移动端布局
    await page.setViewportSize({ width: 768, height: 600 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    console.log('✅ 响应式布局测试通过');
  });

  test('16. 侧边栏交互测试', async ({ page }) => {
    // 测试侧边栏展开/收起功能
    const sidebarToggle = page.locator('.sidebar-toggle');
    if (await sidebarToggle.isVisible()) {
      await sidebarToggle.click();
      await page.waitForTimeout(500);
    }
    
    // 测试子菜单展开
    const tenantMenu = page.locator('.nav-item:has-text("租户管理")');
    await tenantMenu.click();
    await expect(page.locator('.nav-treeview')).toBeVisible();
    
    console.log('✅ 侧边栏交互测试通过');
  });

  test('17. 数据加载状态测试', async ({ page }) => {
    // 测试页面加载状态
    await page.goto(baseURL + '/tenants');
    
    // 等待内容加载完成
    await page.waitForLoadState('networkidle');
    
    // 验证页面结构完整
    await expect(page.locator('.content-wrapper')).toBeVisible();
    await expect(page.locator('.main-header')).toBeVisible();
    await expect(page.locator('.main-sidebar')).toBeVisible();
    
    console.log('✅ 数据加载状态测试通过');
  });

  test('18. 搜索和筛选功能测试', async ({ page }) => {
    // 访问租户管理页面测试搜索功能
    await page.goto(baseURL + '/tenants');
    await page.waitForLoadState('networkidle');
    
    // 如果存在搜索框，测试搜索功能
    const searchInput = page.locator('input[type="search"], input[placeholder*="搜索"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('测试');
      await page.waitForTimeout(1000);
    }
    
    // 如果存在筛选下拉框，测试筛选功能
    const filterSelect = page.locator('select[name*="status"], select[name*="plan"]');
    if (await filterSelect.isVisible()) {
      await filterSelect.selectOption({ index: 1 });
      await page.waitForTimeout(1000);
    }
    
    console.log('✅ 搜索筛选功能测试通过');
  });

  test('19. 页面完整性验证', async ({ page }) => {
    const routes = [
      { path: '/dashboard', title: '平台仪表盘' },
      { path: '/tenants', title: '租户管理' },
      { path: '/goose-prices', title: '今日鹅价' },
      { path: '/announcements', title: '平台公告' },
      { path: '/knowledge', title: '知识库' },
      { path: '/mall', title: '商城管理' },
      { path: '/ai-config', title: 'AI配置' },
      { path: '/monitoring', title: '性能监控' },
      { path: '/reports', title: '报表管理' },
      { path: '/settings', title: '系统设置' }
    ];

    for (const route of routes) {
      await page.goto(baseURL + route.path);
      await page.waitForLoadState('networkidle');
      
      // 验证页面标题
      await expect(page.locator('title')).toContainText(route.title);
      
      // 验证页面基本结构
      await expect(page.locator('.content-wrapper')).toBeVisible();
      
      // 验证无JS错误
      const errors = [];
      page.on('pageerror', err => errors.push(err));
      expect(errors.length).toBe(0);
    }
    
    console.log('✅ 所有页面完整性验证通过');
  });

  test('20. 退出登录功能测试', async ({ page }) => {
    // 点击用户头像下拉菜单
    await page.click('.dropdown-toggle:has-text("admin")');
    
    // 点击退出登录
    await page.click('#logoutBtn');
    
    // 验证跳转到登录页面
    await page.waitForURL('**/login');
    await expect(page.locator('text=登录')).toBeVisible();
    
    console.log('✅ 退出登录功能测试通过');
  });
});

// 租户级管理功能测试（需要先进入租户上下文）
test.describe('租户级管理功能测试', () => {
  const baseURL = 'http://localhost:4000';
  
  test.beforeEach(async ({ page }) => {
    page.setDefaultTimeout(30000);
    await page.goto(baseURL);
    
    // 登录
    await page.fill('#username', 'admin');
    await page.fill('#password', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
    
    // 进入租户管理，选择一个租户（如果存在）
    await page.click('a[href="/tenants"]');
    await page.waitForLoadState('networkidle');
  });

  test('21. 租户级功能访问测试', async ({ page }) => {
    // 尝试直接访问租户级功能
    const tenantRoutes = [
      '/tenant/flocks',
      '/tenant/inventory', 
      '/tenant/health',
      '/tenant/production',
      '/tenant/finance'
    ];
    
    for (const route of tenantRoutes) {
      await page.goto(baseURL + route);
      await page.waitForLoadState('networkidle');
      
      // 验证页面响应（可能显示权限不足或租户选择提示）
      const hasContent = await page.locator('.content-wrapper').isVisible();
      const hasPermissionError = await page.locator('text=权限不足').isVisible().catch(() => false);
      const hasTenantError = await page.locator('text=请先选择要管理的租户').isVisible().catch(() => false);
      
      // 应该有其中一种状态
      expect(hasContent || hasPermissionError || hasTenantError).toBe(true);
    }
    
    console.log('✅ 租户级功能访问控制正常');
  });
});