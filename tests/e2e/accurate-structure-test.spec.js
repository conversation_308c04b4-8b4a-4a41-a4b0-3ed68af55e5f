const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅后台管理中心 - 基于实际结构的精确测试
 * 根据EJS模板的实际结构进行测试
 */

const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  credentials: {
    admin: { username: 'admin', password: 'admin123' }
  }
};

// 测试结果记录
let testResults = {
  modules: {},
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  issues: []
};

// 登录辅助函数
async function loginToSystem(page) {
  await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
  await page.waitForLoadState('networkidle');
  
  await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
  await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
  await page.click('button[type="submit"]');
  await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
  
  return true;
}

// 记录测试结果
function recordTestResult(module, testName, passed, details = '') {
  if (!testResults.modules[module]) {
    testResults.modules[module] = { passed: 0, failed: 0, tests: [] };
  }
  
  testResults.modules[module].tests.push({
    name: testName,
    passed: passed,
    details: details
  });
  
  if (passed) {
    testResults.modules[module].passed++;
    testResults.passedTests++;
    console.log(`✅ ${module} - ${testName}: ${details}`);
  } else {
    testResults.modules[module].failed++;
    testResults.failedTests++;
    testResults.issues.push(`${module} - ${testName}: ${details}`);
    console.log(`❌ ${module} - ${testName}: ${details}`);
  }
  
  testResults.totalTests++;
}

test.describe('智慧养鹅后台管理中心 - 基于实际结构的精确测试', () => {

  // 1. 健康管理模块精确结构测试
  test('健康管理模块 - 实际结构验证', async ({ page }) => {
    console.log('\n🏥 开始健康管理模块实际结构验证...');
    
    try {
      await loginToSystem(page);
      await page.goto(`${TEST_CONFIG.baseURL}/health`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // 1. 验证页面标题
      const pageTitle = page.locator('h1.h3.mb-0.text-gray-800');
      const titleText = await pageTitle.textContent();
      recordTestResult('健康管理', '页面标题', titleText === '健康管理', `标题: ${titleText}`);
      
      // 2. 验证顶部操作按钮（基于实际EJS结构）
      const vaccinationBtn = page.locator('button[data-bs-target="#vaccinationModal"]');
      const treatmentBtn = page.locator('button[data-bs-target="#treatmentModal"]');
      const inspectionBtn = page.locator('button[data-bs-target="#inspectionModal"]');
      
      const vaccinationExists = await vaccinationBtn.count() > 0;
      const treatmentExists = await treatmentBtn.count() > 0;
      const inspectionExists = await inspectionBtn.count() > 0;
      
      recordTestResult('健康管理', '疫苗接种按钮', vaccinationExists, '疫苗接种按钮存在');
      recordTestResult('健康管理', '疾病治疗按钮', treatmentExists, '疾病治疗按钮存在');
      recordTestResult('健康管理', '健康检查按钮', inspectionExists, '健康检查按钮存在');
      
      // 3. 验证统计卡片
      const healthyFlocks = page.locator('#healthyFlocks');
      const treatmentFlocks = page.locator('#treatmentFlocks');
      const vaccinationRate = page.locator('#vaccinationRate');
      const mortalityRate = page.locator('#mortalityRate');
      
      const healthyFlocksExists = await healthyFlocks.count() > 0;
      const treatmentFlocksExists = await treatmentFlocks.count() > 0;
      const vaccinationRateExists = await vaccinationRate.count() > 0;
      const mortalityRateExists = await mortalityRate.count() > 0;
      
      recordTestResult('健康管理', '健康鹅群统计', healthyFlocksExists, '健康鹅群统计卡片存在');
      recordTestResult('健康管理', '治疗中统计', treatmentFlocksExists, '治疗中统计卡片存在');
      recordTestResult('健康管理', '疫苗接种率统计', vaccinationRateExists, '疫苗接种率统计卡片存在');
      recordTestResult('健康管理', '死亡率统计', mortalityRateExists, '死亡率统计卡片存在');
      
      // 4. 验证筛选器
      const recordTypeFilter = page.locator('#recordTypeFilter');
      const flockFilter = page.locator('#flockFilter');
      const statusFilter = page.locator('#statusFilter');
      const startDate = page.locator('#startDate');
      
      const recordTypeFilterExists = await recordTypeFilter.count() > 0;
      const flockFilterExists = await flockFilter.count() > 0;
      const statusFilterExists = await statusFilter.count() > 0;
      const startDateExists = await startDate.count() > 0;
      
      recordTestResult('健康管理', '记录类型筛选器', recordTypeFilterExists, '记录类型筛选器存在');
      recordTestResult('健康管理', '鹅群筛选器', flockFilterExists, '鹅群筛选器存在');
      recordTestResult('健康管理', '状态筛选器', statusFilterExists, '状态筛选器存在');
      recordTestResult('健康管理', '日期筛选器', startDateExists, '日期筛选器存在');
      
      // 5. 验证健康记录表格
      const healthTable = page.locator('#healthTable');
      const healthTableExists = await healthTable.count() > 0;
      recordTestResult('健康管理', '健康记录表格', healthTableExists, '健康记录表格存在');
      
      if (healthTableExists) {
        // 验证表格表头
        const tableHeaders = await page.locator('#healthTable thead th').allTextContents();
        const expectedHeaders = ['日期', '鹅群', '记录类型', '状态/疾病', '处理措施', '兽医', '费用', '下次复查', '操作'];
        const headersMatch = expectedHeaders.every(header => tableHeaders.includes(header));
        recordTestResult('健康管理', '表格表头', headersMatch, `表头: ${tableHeaders.join(', ')}`);
      }
      
      // 6. 测试模态框打开功能
      if (inspectionExists) {
        await inspectionBtn.click();
        await page.waitForTimeout(1000);
        
        const inspectionModal = page.locator('#inspectionModal.show');
        const modalVisible = await inspectionModal.count() > 0;
        recordTestResult('健康管理', '健康检查模态框', modalVisible, '健康检查模态框可以打开');
        
        if (modalVisible) {
          // 关闭模态框
          const closeBtn = page.locator('#inspectionModal .btn-close');
          if (await closeBtn.count() > 0) {
            await closeBtn.click();
            await page.waitForTimeout(500);
          }
        }
      }
      
    } catch (error) {
      recordTestResult('健康管理', '模块测试异常', false, `异常: ${error.message}`);
    }
  });

  // 2. 生产管理模块精确结构测试
  test('生产管理模块 - 实际结构验证', async ({ page }) => {
    console.log('\n🏭 开始生产管理模块实际结构验证...');
    
    try {
      await loginToSystem(page);
      await page.goto(`${TEST_CONFIG.baseURL}/production`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // 1. 验证页面标题
      const pageTitle = page.locator('h1.h3.mb-0.text-gray-800');
      const titleText = await pageTitle.textContent();
      recordTestResult('生产管理', '页面标题', titleText === '生产管理', `标题: ${titleText}`);
      
      // 2. 验证顶部操作按钮
      const feedRecordBtn = page.locator('button[data-bs-target="#feedRecordModal"]');
      const eggProductionBtn = page.locator('button[data-bs-target="#eggProductionModal"]');
      
      const feedRecordExists = await feedRecordBtn.count() > 0;
      const eggProductionExists = await eggProductionBtn.count() > 0;
      
      recordTestResult('生产管理', '饲料投喂记录按钮', feedRecordExists, '饲料投喂记录按钮存在');
      recordTestResult('生产管理', '产蛋记录按钮', eggProductionExists, '产蛋记录按钮存在');
      
      // 3. 验证生产统计卡片
      const todayEggs = page.locator('#todayEggs');
      const monthEggs = page.locator('#monthEggs');
      const feedConsumption = page.locator('#feedConsumption');
      const eggRate = page.locator('#eggRate');
      
      const todayEggsExists = await todayEggs.count() > 0;
      const monthEggsExists = await monthEggs.count() > 0;
      const feedConsumptionExists = await feedConsumption.count() > 0;
      const eggRateExists = await eggRate.count() > 0;
      
      recordTestResult('生产管理', '今日产蛋量统计', todayEggsExists, '今日产蛋量统计卡片存在');
      recordTestResult('生产管理', '本月产蛋量统计', monthEggsExists, '本月产蛋量统计卡片存在');
      recordTestResult('生产管理', '饲料消耗统计', feedConsumptionExists, '饲料消耗统计卡片存在');
      recordTestResult('生产管理', '平均产蛋率统计', eggRateExists, '平均产蛋率统计卡片存在');
      
      // 4. 验证生产记录表格
      const productionTable = page.locator('#productionTable');
      const productionTableExists = await productionTable.count() > 0;
      recordTestResult('生产管理', '生产记录表格', productionTableExists, '生产记录表格存在');
      
      if (productionTableExists) {
        // 验证表格表头
        const tableHeaders = await page.locator('#productionTable thead th').allTextContents();
        const expectedHeaders = ['日期', '鹅群', '记录类型', '数量/重量', '单位', '品质等级', '记录员', '备注', '操作'];
        const headersMatch = expectedHeaders.every(header => tableHeaders.includes(header));
        recordTestResult('生产管理', '表格表头', headersMatch, `表头: ${tableHeaders.join(', ')}`);
      }
      
      // 5. 测试产蛋记录模态框
      if (eggProductionExists) {
        await eggProductionBtn.click();
        await page.waitForTimeout(1000);
        
        const eggProductionModal = page.locator('#eggProductionModal.show');
        const modalVisible = await eggProductionModal.count() > 0;
        recordTestResult('生产管理', '产蛋记录模态框', modalVisible, '产蛋记录模态框可以打开');
        
        if (modalVisible) {
          // 验证表单字段
          const eggFlock = page.locator('#eggFlock');
          const eggDate = page.locator('#eggDate');
          const eggCount = page.locator('#eggCount');
          const eggWeight = page.locator('#eggWeight');
          
          const eggFlockExists = await eggFlock.count() > 0;
          const eggDateExists = await eggDate.count() > 0;
          const eggCountExists = await eggCount.count() > 0;
          const eggWeightExists = await eggWeight.count() > 0;
          
          recordTestResult('生产管理', '鹅群选择字段', eggFlockExists, '鹅群选择字段存在');
          recordTestResult('生产管理', '记录日期字段', eggDateExists, '记录日期字段存在');
          recordTestResult('生产管理', '产蛋数量字段', eggCountExists, '产蛋数量字段存在');
          recordTestResult('生产管理', '总重量字段', eggWeightExists, '总重量字段存在');
          
          // 关闭模态框
          const closeBtn = page.locator('#eggProductionModal .btn-close');
          if (await closeBtn.count() > 0) {
            await closeBtn.click();
            await page.waitForTimeout(500);
          }
        }
      }
      
    } catch (error) {
      recordTestResult('生产管理', '模块测试异常', false, `异常: ${error.message}`);
    }
  });

  // 3. 用户管理模块结构验证
  test('用户管理模块 - 结构完整性验证', async ({ page }) => {
    console.log('\n👥 开始用户管理模块结构完整性验证...');
    
    try {
      await loginToSystem(page);
      await page.goto(`${TEST_CONFIG.baseURL}/users`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // 验证页面基本结构
      const pageContent = page.locator('.container-fluid, .content, .main-content');
      const pageContentExists = await pageContent.count() > 0;
      recordTestResult('用户管理', '页面容器', pageContentExists, '页面容器存在');
      
      // 验证表格存在
      const userTable = page.locator('table, .table, .data-table');
      const userTableExists = await userTable.count() > 0;
      recordTestResult('用户管理', '用户表格', userTableExists, '用户表格存在');
      
      // 验证搜索功能
      const searchInput = page.locator('input[type="search"], input[placeholder*="搜索"], .search-input');
      const searchExists = await searchInput.count() > 0;
      recordTestResult('用户管理', '搜索功能', searchExists, '搜索功能存在');
      
    } catch (error) {
      recordTestResult('用户管理', '模块测试异常', false, `异常: ${error.message}`);
    }
  });

  test.afterAll(async () => {
    console.log('\n📊 基于实际结构的测试结果汇总:');
    
    const totalTests = testResults.totalTests;
    const passedTests = testResults.passedTests;
    const failedTests = testResults.failedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
    
    console.log(`📊 总测试数: ${totalTests}`);
    console.log(`✅ 通过测试: ${passedTests}`);
    console.log(`❌ 失败测试: ${failedTests}`);
    console.log(`📈 成功率: ${successRate}%`);
    
    console.log('\n=== 各模块详细结果 ===');
    Object.keys(testResults.modules).forEach(module => {
      const moduleResult = testResults.modules[module];
      const moduleTotal = moduleResult.passed + moduleResult.failed;
      const moduleRate = moduleTotal > 0 ? ((moduleResult.passed / moduleTotal) * 100).toFixed(1) : 0;
      console.log(`\n${module}: ${moduleResult.passed}/${moduleTotal} (${moduleRate}%)`);
      
      moduleResult.tests.forEach(test => {
        const status = test.passed ? '✅' : '❌';
        console.log(`  ${status} ${test.name}: ${test.details}`);
      });
    });
    
    if (testResults.issues.length > 0) {
      console.log('\n=== 需要关注的问题 ===');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
  });

});
