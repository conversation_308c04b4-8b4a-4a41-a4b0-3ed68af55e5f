import { test, expect } from '@playwright/test';

test.describe('智慧养鹅SAAS租户功能快速验证', () => {
  
  test('租户级管理功能快速验证', async ({ page }) => {
    // 设置较短的超时时间
    test.setTimeout(90000);
    
    console.log('开始测试租户级管理功能...');
    
    // 1. 访问主页
    await page.goto('http://localhost:3002/', { waitUntil: 'domcontentloaded' });
    console.log('✓ 成功访问管理后台主页');
    
    // 检查页面是否正常加载
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    console.log('✓ 页面内容正常显示');
    
    // 2. 测试租户级功能模块的URL访问
    const tenantModules = [
      { name: '租户管理', url: '/tenants' },
      { name: '租户详情', url: '/tenants/details' },
      { name: '租户选择', url: '/tenant-select' },
      { name: '鹅群管理', url: '/flocks' },
      { name: '健康管理', url: '/health' },
      { name: '生产管理', url: '/production' },
      { name: '财务管理', url: '/finance' },
      { name: '库存管理', url: '/inventory' }
    ];
    
    let successCount = 0;
    let totalCount = tenantModules.length;
    
    for (const module of tenantModules) {
      try {
        console.log(`测试模块: ${module.name}`);
        
        // 访问模块页面
        await page.goto(`http://localhost:3002${module.url}`, { 
          waitUntil: 'domcontentloaded',
          timeout: 10000 
        });
        
        // 检查是否有严重错误
        const hasError = await page.locator('h1:has-text("Error"), .error-page, h1:has-text("500"), h1:has-text("404")').isVisible();
        
        if (!hasError) {
          // 检查页面是否有基本内容
          const hasBasicContent = await page.locator('body').isVisible();
          if (hasBasicContent) {
            console.log(`✓ ${module.name} 模块正常访问`);
            successCount++;
          } else {
            console.log(`⚠ ${module.name} 模块页面无内容`);
          }
        } else {
          console.log(`✗ ${module.name} 模块访问出错`);
        }
        
        // 短暂等待避免请求过快
        await page.waitForTimeout(500);
        
      } catch (error) {
        console.log(`✗ ${module.name} 模块测试失败: ${error.message}`);
      }
    }
    
    console.log(`\n租户功能模块测试结果: ${successCount}/${totalCount} 个模块正常访问`);
    
    // 至少要有一半以上的模块能正常访问
    expect(successCount).toBeGreaterThan(totalCount / 2);
    
    // 3. 测试租户管理页面的基本功能
    console.log('\n测试租户管理页面基本功能...');
    
    await page.goto('http://localhost:3002/tenants', { waitUntil: 'domcontentloaded' });
    
    // 检查是否有租户相关的元素
    const tenantElements = await page.locator('table, .tenant-list, .tenant-card, .card, .list-group').count();
    if (tenantElements > 0) {
      console.log('✓ 租户管理页面包含相关元素');
    } else {
      console.log('⚠ 租户管理页面缺少相关元素');
    }
    
    // 检查是否有操作按钮
    const actionButtons = await page.locator('button, .btn, a.btn').count();
    if (actionButtons > 0) {
      console.log('✓ 租户管理页面包含操作按钮');
    } else {
      console.log('⚠ 租户管理页面缺少操作按钮');
    }
    
    // 4. 测试租户切换功能
    console.log('\n测试租户切换功能...');
    
    try {
      await page.goto('http://localhost:3002/tenant-select', { 
        waitUntil: 'domcontentloaded',
        timeout: 10000 
      });
      
      const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
      
      if (!hasError) {
        console.log('✓ 租户选择页面可访问');
        
        // 检查是否有租户选择相关的元素
        const selectElements = await page.locator('table, .tenant-list, .tenant-card, .list-group, select').count();
        if (selectElements > 0) {
          console.log('✓ 租户选择页面包含选择元素');
        } else {
          console.log('⚠ 租户选择页面缺少选择元素');
        }
      } else {
        console.log('⚠ 租户选择页面访问失败');
      }
    } catch (error) {
      console.log(`⚠ 租户选择功能测试失败: ${error.message}`);
    }
    
    // 5. 测试租户级数据管理功能
    console.log('\n测试租户级数据管理功能...');
    
    const dataModules = [
      { name: '鹅群数据', url: '/flocks' },
      { name: '健康数据', url: '/health' },
      { name: '生产数据', url: '/production' }
    ];
    
    let dataModuleSuccess = 0;
    
    for (const module of dataModules) {
      try {
        await page.goto(`http://localhost:3002${module.url}`, { 
          waitUntil: 'domcontentloaded',
          timeout: 8000 
        });
        
        const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
        
        if (!hasError) {
          const hasDataElements = await page.locator('table, .data-list, .data-card, .chart, .stats').count();
          if (hasDataElements > 0) {
            console.log(`✓ ${module.name} 管理功能正常`);
            dataModuleSuccess++;
          } else {
            console.log(`⚠ ${module.name} 管理功能缺少数据元素`);
          }
        } else {
          console.log(`✗ ${module.name} 管理功能访问失败`);
        }
        
        await page.waitForTimeout(500);
        
      } catch (error) {
        console.log(`✗ ${module.name} 管理功能测试失败: ${error.message}`);
      }
    }
    
    console.log(`租户级数据管理功能测试结果: ${dataModuleSuccess}/${dataModules.length} 个功能正常`);
    
    // 6. 测试权限和安全性
    console.log('\n测试权限和安全性...');
    
    // 测试一些可能需要特殊权限的页面
    const securePages = [
      '/admin',
      '/system/config',
      '/tenant/admin'
    ];
    
    let securePageTests = 0;
    
    for (const securePage of securePages) {
      try {
        await page.goto(`http://localhost:3002${securePage}`, { 
          waitUntil: 'domcontentloaded',
          timeout: 5000 
        });
        
        // 检查是否有权限控制
        const hasPermissionControl = await page.locator('text=权限, text=unauthorized, text=login, text=登录').isVisible();
        
        if (hasPermissionControl) {
          console.log(`✓ 页面 ${securePage} 有权限控制`);
          securePageTests++;
        } else {
          console.log(`⚠ 页面 ${securePage} 可能缺少权限控制`);
        }
        
      } catch (error) {
        console.log(`⚠ 安全页面 ${securePage} 测试失败: ${error.message}`);
      }
    }
    
    console.log(`权限控制测试结果: ${securePageTests}/${securePages.length} 个页面有权限控制`);
    
    // 7. 综合评估
    console.log('\n=== 租户级管理功能测试总结 ===');
    console.log(`功能模块可访问性: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
    console.log(`数据管理功能: ${dataModuleSuccess}/${dataModules.length} (${Math.round(dataModuleSuccess/dataModules.length*100)}%)`);
    console.log(`权限控制: ${securePageTests}/${securePages.length} (${Math.round(securePageTests/securePages.length*100)}%)`);
    
    // 计算总体健康度
    const totalScore = (successCount/totalCount + dataModuleSuccess/dataModules.length + securePageTests/securePages.length) / 3;
    const healthPercentage = Math.round(totalScore * 100);
    
    console.log(`总体健康度: ${healthPercentage}%`);
    
    if (healthPercentage >= 70) {
      console.log('✅ 租户级管理功能整体状态良好');
    } else if (healthPercentage >= 50) {
      console.log('⚠ 租户级管理功能需要改进');
    } else {
      console.log('❌ 租户级管理功能存在严重问题');
    }
    
    // 至少要有60%的总体健康度
    expect(totalScore).toBeGreaterThan(0.6);
    
    console.log('\n✅ 租户级管理功能快速验证完成');
  });
});
