import { test, expect } from '@playwright/test';

test.describe('智慧养鹅SAAS管理后台快速功能验证', () => {
  
  test('管理后台基本功能验证', async ({ page }) => {
    // 设置较短的超时时间
    test.setTimeout(60000);
    
    console.log('开始测试管理后台基本功能...');
    
    // 1. 访问主页
    await page.goto('http://localhost:3002/', { waitUntil: 'domcontentloaded' });
    console.log('✓ 成功访问管理后台主页');
    
    // 检查页面是否正常加载
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    console.log('✓ 页面内容正常显示');
    
    // 2. 测试各个功能模块的URL访问
    const modules = [
      { name: '仪表盘', url: '/dashboard' },
      { name: '租户管理', url: '/tenants' },
      { name: '今日鹅价', url: '/goose-prices' },
      { name: '平台公告', url: '/announcements' },
      { name: '知识库', url: '/knowledge' },
      { name: '商城管理', url: '/mall' },
      { name: 'AI配置', url: '/ai-config' },
      { name: '系统设置', url: '/system' }
    ];
    
    let successCount = 0;
    let totalCount = modules.length;
    
    for (const module of modules) {
      try {
        console.log(`测试模块: ${module.name}`);
        
        // 访问模块页面
        await page.goto(`http://localhost:3002${module.url}`, { 
          waitUntil: 'domcontentloaded',
          timeout: 10000 
        });
        
        // 检查是否有严重错误
        const hasError = await page.locator('h1:has-text("Error"), .error-page, h1:has-text("500"), h1:has-text("404")').isVisible();
        
        if (!hasError) {
          // 检查页面是否有基本内容
          const hasBasicContent = await page.locator('body').isVisible();
          if (hasBasicContent) {
            console.log(`✓ ${module.name} 模块正常访问`);
            successCount++;
          } else {
            console.log(`⚠ ${module.name} 模块页面无内容`);
          }
        } else {
          console.log(`✗ ${module.name} 模块访问出错`);
        }
        
        // 短暂等待避免请求过快
        await page.waitForTimeout(500);
        
      } catch (error) {
        console.log(`✗ ${module.name} 模块测试失败: ${error.message}`);
      }
    }
    
    console.log(`\n功能模块测试结果: ${successCount}/${totalCount} 个模块正常访问`);
    
    // 至少要有一半以上的模块能正常访问
    expect(successCount).toBeGreaterThan(totalCount / 2);
    
    // 3. 测试页面响应性
    console.log('\n测试页面响应性...');
    
    await page.goto('http://localhost:3002/', { waitUntil: 'domcontentloaded' });
    
    // 测试不同视口大小
    const viewports = [
      { width: 1920, height: 1080, name: '桌面' },
      { width: 768, height: 1024, name: '平板' },
      { width: 375, height: 667, name: '手机' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(1000);
      
      const isVisible = await page.locator('body').isVisible();
      expect(isVisible).toBeTruthy();
      console.log(`✓ ${viewport.name}视图 (${viewport.width}x${viewport.height}) 正常显示`);
    }
    
    console.log('\n✅ 管理后台基本功能验证完成');
  });
  
  test('API健康检查', async ({ page }) => {
    test.setTimeout(30000);
    
    console.log('开始API健康检查...');
    
    // 检查后端API是否正常
    try {
      const response = await page.request.get('http://localhost:3001/api/health');
      const status = response.status();
      
      console.log(`后端API状态码: ${status}`);
      expect(status).toBe(200);
      
      const data = await response.json();
      console.log('后端API响应:', data);
      
      expect(data.success).toBeTruthy();
      console.log('✓ 后端API健康检查通过');
      
    } catch (error) {
      console.log(`✗ 后端API健康检查失败: ${error.message}`);
      throw error;
    }
  });
  
  test('数据库连接测试', async ({ page }) => {
    test.setTimeout(30000);
    
    console.log('开始数据库连接测试...');
    
    // 通过访问需要数据库的页面来测试数据库连接
    try {
      await page.goto('http://localhost:3002/tenants', { 
        waitUntil: 'domcontentloaded',
        timeout: 15000 
      });
      
      // 检查是否有数据库连接错误
      const hasDbError = await page.locator('text=database, text=connection, text=MySQL').isVisible();
      
      if (!hasDbError) {
        console.log('✓ 数据库连接正常');
      } else {
        console.log('⚠ 可能存在数据库连接问题');
      }
      
      // 检查页面是否正常加载
      const hasContent = await page.locator('body').isVisible();
      expect(hasContent).toBeTruthy();
      
    } catch (error) {
      console.log(`数据库连接测试出现问题: ${error.message}`);
      // 不抛出错误，因为这可能是正常的（如果数据库为空）
    }
  });
});
