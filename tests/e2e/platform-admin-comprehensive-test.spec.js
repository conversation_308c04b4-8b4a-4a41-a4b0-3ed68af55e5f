import { test, expect } from '@playwright/test';

test.describe('智慧养鹅SAAS平台级管理后台功能测试', () => {
  
  // 测试前的登录
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3002/');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 如果重定向到登录页面，进行登录
    if (page.url().includes('/login')) {
      await page.fill('input[name="username"], input[type="text"]', 'super_admin');
      await page.fill('input[name="password"], input[type="password"]', 'admin123');
      
      // 查找并点击登录按钮
      const loginButton = page.locator('button[type="submit"], button:has-text("登录"), .btn-primary');
      await loginButton.first().click();
      
      // 等待登录完成
      await page.waitForLoadState('networkidle');
    }
  });

  test('1. 今日鹅价管理功能测试', async ({ page }) => {
    // 导航到鹅价管理页面
    await page.goto('http://localhost:3002/goose-prices');
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否正常加载
    const pageTitle = await page.title();
    console.log('鹅价管理页面标题:', pageTitle);
    
    // 检查是否有错误页面
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找鹅价相关的元素
    const priceElements = await page.locator('table, .price-list, .price-card, h1, h2, h3').count();
    expect(priceElements).toBeGreaterThan(0);
  });

  test('2. 平台公告管理功能测试', async ({ page }) => {
    // 导航到公告管理页面
    await page.goto('http://localhost:3002/announcements');
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找公告相关的元素
    const announcementElements = await page.locator('table, .announcement-list, .announcement-card, h1, h2, h3').count();
    expect(announcementElements).toBeGreaterThan(0);
  });

  test('3. 知识库管理功能测试', async ({ page }) => {
    // 导航到知识库管理页面
    await page.goto('http://localhost:3002/knowledge');
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找知识库相关的元素
    const knowledgeElements = await page.locator('table, .knowledge-list, .knowledge-card, h1, h2, h3').count();
    expect(knowledgeElements).toBeGreaterThan(0);
  });

  test('4. 商城模块管理功能测试', async ({ page }) => {
    // 导航到商城管理页面
    await page.goto('http://localhost:3002/mall');
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找商城相关的元素
    const mallElements = await page.locator('table, .mall-list, .product-list, h1, h2, h3').count();
    expect(mallElements).toBeGreaterThan(0);
  });

  test('5. 租户管理功能测试', async ({ page }) => {
    // 导航到租户管理页面
    await page.goto('http://localhost:3002/tenants');
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找租户相关的元素
    const tenantElements = await page.locator('table, .tenant-list, .tenant-card, h1, h2, h3').count();
    expect(tenantElements).toBeGreaterThan(0);
    
    // 测试租户创建按钮（如果存在）
    const createButton = page.locator('button:has-text("新增"), button:has-text("创建"), .btn-primary');
    if (await createButton.first().isVisible()) {
      console.log('找到租户创建按钮');
    }
  });

  test('6. AI大模型配置功能测试', async ({ page }) => {
    // 导航到AI配置页面
    await page.goto('http://localhost:3002/ai-config');
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找AI配置相关的元素
    const aiElements = await page.locator('form, .config-form, .ai-config, h1, h2, h3').count();
    expect(aiElements).toBeGreaterThan(0);
  });

  test('7. 系统设置功能测试', async ({ page }) => {
    // 导航到系统设置页面
    await page.goto('http://localhost:3002/system');
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找系统设置相关的元素
    const systemElements = await page.locator('form, .settings-form, .system-config, h1, h2, h3').count();
    expect(systemElements).toBeGreaterThan(0);
  });

  test('8. 导航菜单功能测试', async ({ page }) => {
    await page.goto('http://localhost:3002/');
    await page.waitForLoadState('networkidle');
    
    // 测试侧边栏导航
    const navigationLinks = [
      { url: '/dashboard', name: '仪表盘' },
      { url: '/tenants', name: '租户管理' },
      { url: '/goose-prices', name: '鹅价管理' },
      { url: '/announcements', name: '公告管理' },
      { url: '/knowledge', name: '知识库' },
      { url: '/mall', name: '商城管理' },
      { url: '/ai-config', name: 'AI配置' },
      { url: '/system', name: '系统设置' }
    ];
    
    for (const link of navigationLinks) {
      console.log(`测试导航到: ${link.name} (${link.url})`);
      
      // 尝试通过URL直接访问
      await page.goto(`http://localhost:3002${link.url}`);
      await page.waitForLoadState('networkidle');
      
      // 检查页面是否正常加载
      const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
      expect(hasError).toBeFalsy();
      
      // 检查页面有内容
      const hasContent = await page.locator('body').isVisible();
      expect(hasContent).toBeTruthy();
    }
  });

  test('9. 响应式设计测试', async ({ page }) => {
    await page.goto('http://localhost:3002/');
    await page.waitForLoadState('networkidle');
    
    // 测试桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForLoadState('networkidle');
    let hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForLoadState('networkidle');
    hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 测试手机视图
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForLoadState('networkidle');
    hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
  });

  test('10. 页面性能测试', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('http://localhost:3002/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    console.log(`页面加载时间: ${loadTime}ms`);
    
    // 页面加载时间应该在合理范围内（10秒内）
    expect(loadTime).toBeLessThan(10000);
    
    // 检查页面是否有内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
  });
});
