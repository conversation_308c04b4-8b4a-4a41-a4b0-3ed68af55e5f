import { test, expect } from '@playwright/test';

test.describe('智慧养鹅SAAS租户级管理功能测试', () => {
  
  // 测试前的登录
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3002/');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 如果重定向到登录页面，进行登录
    if (page.url().includes('/login')) {
      await page.fill('input[name="username"], input[type="text"]', 'super_admin');
      await page.fill('input[name="password"], input[type="password"]', 'admin123');
      
      // 查找并点击登录按钮
      const loginButton = page.locator('button[type="submit"], button:has-text("登录"), .btn-primary');
      await loginButton.first().click();
      
      // 等待登录完成
      await page.waitForLoadState('networkidle');
    }
  });

  test('1. 租户管理页面基本功能测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户管理页面...');
    
    // 访问租户管理页面
    await page.goto('http://localhost:3002/tenants', { waitUntil: 'domcontentloaded' });
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    console.log('✓ 租户管理页面正常加载');
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找租户相关的元素
    const tenantElements = await page.locator('table, .tenant-list, .tenant-card, h1, h2, h3, .card').count();
    expect(tenantElements).toBeGreaterThan(0);
    console.log('✓ 租户管理页面包含相关元素');
    
    // 检查是否有租户创建按钮
    const createButton = page.locator('button:has-text("新增"), button:has-text("创建"), button:has-text("添加"), .btn-primary');
    const hasCreateButton = await createButton.first().isVisible();
    if (hasCreateButton) {
      console.log('✓ 找到租户创建按钮');
    } else {
      console.log('⚠ 未找到租户创建按钮');
    }
  });

  test('2. 租户详情页面测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户详情功能...');
    
    // 访问租户管理页面
    await page.goto('http://localhost:3002/tenants', { waitUntil: 'domcontentloaded' });
    
    // 查找租户详情链接或按钮
    const detailsLink = page.locator('a:has-text("详情"), a:has-text("查看"), button:has-text("详情"), .btn-info');
    const hasDetailsLink = await detailsLink.first().isVisible();
    
    if (hasDetailsLink) {
      console.log('✓ 找到租户详情链接');
      
      // 点击第一个详情链接
      await detailsLink.first().click();
      await page.waitForLoadState('networkidle');
      
      // 检查详情页面是否正常加载
      const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
      expect(hasError).toBeFalsy();
      console.log('✓ 租户详情页面正常加载');
      
    } else {
      console.log('⚠ 未找到租户详情链接，跳过详情页面测试');
    }
  });

  test('3. 租户切换功能测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户切换功能...');
    
    // 访问租户选择页面
    await page.goto('http://localhost:3002/tenant-select', { waitUntil: 'domcontentloaded' });
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    
    if (!hasError) {
      console.log('✓ 租户选择页面正常加载');
      
      // 检查是否有租户列表
      const tenantList = await page.locator('table, .tenant-list, .tenant-card, .list-group').count();
      if (tenantList > 0) {
        console.log('✓ 租户选择页面包含租户列表');
        
        // 查找切换按钮
        const switchButton = page.locator('button:has-text("选择"), button:has-text("切换"), .btn-primary');
        const hasSwitchButton = await switchButton.first().isVisible();
        
        if (hasSwitchButton) {
          console.log('✓ 找到租户切换按钮');
        } else {
          console.log('⚠ 未找到租户切换按钮');
        }
      } else {
        console.log('⚠ 租户选择页面无租户列表');
      }
    } else {
      console.log('⚠ 租户选择页面访问失败，可能功能未实现');
    }
  });

  test('4. 租户级鹅群管理功能测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户级鹅群管理功能...');
    
    // 访问鹅群管理页面
    await page.goto('http://localhost:3002/flocks', { waitUntil: 'domcontentloaded' });
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    console.log('✓ 鹅群管理页面正常加载');
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找鹅群相关的元素
    const flockElements = await page.locator('table, .flock-list, .flock-card, h1, h2, h3, .card').count();
    expect(flockElements).toBeGreaterThan(0);
    console.log('✓ 鹅群管理页面包含相关元素');
  });

  test('5. 租户级健康记录管理功能测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户级健康记录管理功能...');
    
    // 访问健康记录管理页面
    await page.goto('http://localhost:3002/health', { waitUntil: 'domcontentloaded' });
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    console.log('✓ 健康记录管理页面正常加载');
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找健康记录相关的元素
    const healthElements = await page.locator('table, .health-list, .health-card, h1, h2, h3, .card').count();
    expect(healthElements).toBeGreaterThan(0);
    console.log('✓ 健康记录管理页面包含相关元素');
  });

  test('6. 租户级生产记录管理功能测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户级生产记录管理功能...');
    
    // 访问生产记录管理页面
    await page.goto('http://localhost:3002/production', { waitUntil: 'domcontentloaded' });
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    console.log('✓ 生产记录管理页面正常加载');
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找生产记录相关的元素
    const productionElements = await page.locator('table, .production-list, .production-card, h1, h2, h3, .card').count();
    expect(productionElements).toBeGreaterThan(0);
    console.log('✓ 生产记录管理页面包含相关元素');
  });

  test('7. 租户级财务管理功能测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户级财务管理功能...');
    
    // 访问财务管理页面
    await page.goto('http://localhost:3002/finance', { waitUntil: 'domcontentloaded' });
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    expect(hasError).toBeFalsy();
    console.log('✓ 财务管理页面正常加载');
    
    // 检查页面内容
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // 查找财务管理相关的元素
    const financeElements = await page.locator('table, .finance-list, .finance-card, h1, h2, h3, .card').count();
    expect(financeElements).toBeGreaterThan(0);
    console.log('✓ 财务管理页面包含相关元素');
  });

  test('8. 租户权限隔离测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户权限隔离...');
    
    // 测试不同租户页面的访问权限
    const tenantPages = [
      '/tenants/1/dashboard',
      '/tenants/2/dashboard',
      '/tenant/1/flocks',
      '/tenant/2/flocks'
    ];
    
    let accessiblePages = 0;
    let totalPages = tenantPages.length;
    
    for (const pagePath of tenantPages) {
      try {
        await page.goto(`http://localhost:3002${pagePath}`, { 
          waitUntil: 'domcontentloaded',
          timeout: 10000 
        });
        
        // 检查是否有权限错误
        const hasPermissionError = await page.locator('text=权限, text=unauthorized, text=403, text=forbidden').isVisible();
        
        if (!hasPermissionError) {
          const hasContent = await page.locator('body').isVisible();
          if (hasContent) {
            accessiblePages++;
            console.log(`✓ 页面 ${pagePath} 可访问`);
          }
        } else {
          console.log(`⚠ 页面 ${pagePath} 权限受限`);
        }
        
      } catch (error) {
        console.log(`⚠ 页面 ${pagePath} 访问失败: ${error.message}`);
      }
    }
    
    console.log(`权限隔离测试结果: ${accessiblePages}/${totalPages} 个页面可访问`);
  });

  test('9. 租户数据统计功能测试', async ({ page }) => {
    test.setTimeout(60000);
    
    console.log('开始测试租户数据统计功能...');
    
    // 访问租户统计页面
    await page.goto('http://localhost:3002/tenant-stats', { waitUntil: 'domcontentloaded' });
    
    // 检查页面是否正常加载
    const hasError = await page.locator('h1:has-text("Error"), .error-page').isVisible();
    
    if (!hasError) {
      console.log('✓ 租户统计页面正常加载');
      
      // 查找统计相关的元素
      const statsElements = await page.locator('.stats, .statistics, .chart, .dashboard, .metric').count();
      if (statsElements > 0) {
        console.log('✓ 租户统计页面包含统计元素');
      } else {
        console.log('⚠ 租户统计页面无统计元素');
      }
    } else {
      console.log('⚠ 租户统计页面访问失败，可能功能未实现');
    }
  });

  test('10. 综合功能验证', async ({ page }) => {
    test.setTimeout(90000);
    
    console.log('开始综合功能验证...');
    
    // 测试主要租户管理功能的连通性
    const mainFunctions = [
      { name: '租户管理', url: '/tenants' },
      { name: '鹅群管理', url: '/flocks' },
      { name: '健康管理', url: '/health' },
      { name: '生产管理', url: '/production' },
      { name: '财务管理', url: '/finance' }
    ];
    
    let workingFunctions = 0;
    let totalFunctions = mainFunctions.length;
    
    for (const func of mainFunctions) {
      try {
        console.log(`测试功能: ${func.name}`);
        
        await page.goto(`http://localhost:3002${func.url}`, { 
          waitUntil: 'domcontentloaded',
          timeout: 15000 
        });
        
        // 检查是否有严重错误
        const hasError = await page.locator('h1:has-text("Error"), .error-page, h1:has-text("500")').isVisible();
        
        if (!hasError) {
          const hasContent = await page.locator('body').isVisible();
          if (hasContent) {
            workingFunctions++;
            console.log(`✓ ${func.name} 功能正常`);
          }
        } else {
          console.log(`✗ ${func.name} 功能异常`);
        }
        
        await page.waitForTimeout(1000);
        
      } catch (error) {
        console.log(`✗ ${func.name} 功能测试失败: ${error.message}`);
      }
    }
    
    console.log(`\n综合功能验证结果: ${workingFunctions}/${totalFunctions} 个功能正常`);
    
    // 至少要有80%的功能正常工作
    expect(workingFunctions).toBeGreaterThan(totalFunctions * 0.8);
    
    console.log('\n✅ 租户级管理功能测试完成');
  });
});
