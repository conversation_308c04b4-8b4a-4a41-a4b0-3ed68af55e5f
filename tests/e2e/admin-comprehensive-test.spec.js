// @ts-check
const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅SaaS管理后台全功能模块测试
 * 详细测试每个功能模块、页面和子页面的实现情况
 */
test.describe('管理后台全功能测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 设置测试超时
    test.setTimeout(120000);
    
    // 访问管理后台首页
    await page.goto('/');
    
    // 等待页面加载
    await page.waitForTimeout(2000);
  });

  test('01. 访问管理后台首页和基础检查', async ({ page }) => {
    console.log('🔍 测试: 访问管理后台首页...');
    
    // 检查页面标题
    const title = await page.title();
    console.log(`页面标题: ${title}`);
    
    // 检查是否被重定向到登录页
    const currentUrl = page.url();
    console.log(`当前URL: ${currentUrl}`);
    
    // 截图记录
    await page.screenshot({ path: 'test-results/01-homepage.png' });
    
    // 检查页面基本元素
    const bodyText = await page.textContent('body');
    console.log('页面内容预览:', bodyText.substring(0, 200));
  });

  test('02. 用户认证和登录功能测试', async ({ page }) => {
    console.log('🔍 测试: 用户认证和登录功能...');
    
    // 尝试找到登录表单
    await page.waitForTimeout(2000);
    
    // 检查是否有登录相关元素
    const loginElements = await page.$$('input[type="text"], input[type="email"], input[type="password"], button');
    console.log(`找到 ${loginElements.length} 个表单元素`);
    
    // 查找可能的登录输入框
    const inputs = await page.$$('input');
    for (let i = 0; i < inputs.length; i++) {
      const inputType = await inputs[i].getAttribute('type');
      const inputName = await inputs[i].getAttribute('name');
      const inputId = await inputs[i].getAttribute('id');
      const inputPlaceholder = await inputs[i].getAttribute('placeholder');
      console.log(`输入框 ${i+1}: type=${inputType}, name=${inputName}, id=${inputId}, placeholder=${inputPlaceholder}`);
    }
    
    // 查找可能的登录按钮
    const buttons = await page.$$('button, input[type="submit"]');
    for (let i = 0; i < buttons.length; i++) {
      const buttonText = await buttons[i].textContent();
      const buttonType = await buttons[i].getAttribute('type');
      console.log(`按钮 ${i+1}: text="${buttonText}", type=${buttonType}`);
    }
    
    // 截图记录登录页面
    await page.screenshot({ path: 'test-results/02-login-page.png' });
    
    // 尝试模拟登录（如果有表单）
    const usernameInput = page.locator('input[type="text"], input[type="email"], input[name*="user"], input[name*="email"], input[id*="user"], input[id*="email"]').first();
    const passwordInput = page.locator('input[type="password"]').first();
    const loginButton = page.locator('button:has-text("登录"), button:has-text("Login"), input[type="submit"]').first();
    
    if (await usernameInput.count() > 0 && await passwordInput.count() > 0) {
      console.log('找到登录表单，尝试登录...');
      
      // 测试用户名和密码
      await usernameInput.fill('admin');
      await passwordInput.fill('admin123');
      
      if (await loginButton.count() > 0) {
        await loginButton.click();
        await page.waitForTimeout(3000);
        
        // 检查登录后的页面
        const afterLoginUrl = page.url();
        console.log(`登录后URL: ${afterLoginUrl}`);
        
        await page.screenshot({ path: 'test-results/02-after-login.png' });
      }
    } else {
      console.log('未找到标准登录表单');
    }
  });

  test('03. 主导航和菜单结构测试', async ({ page }) => {
    console.log('🔍 测试: 主导航和菜单结构...');
    
    await page.waitForTimeout(2000);
    
    // 查找导航菜单
    const navElements = await page.$$('nav, .navbar, .nav, .menu, .sidebar, ul li a');
    console.log(`找到 ${navElements.length} 个导航相关元素`);
    
    // 分析页面中的所有链接
    const links = await page.$$('a');
    console.log(`页面共有 ${links.length} 个链接`);
    
    const linkInfo = [];
    for (let i = 0; i < Math.min(links.length, 20); i++) {
      const href = await links[i].getAttribute('href');
      const text = await links[i].textContent();
      const className = await links[i].getAttribute('class');
      linkInfo.push({ href, text: text?.trim(), className });
    }
    
    console.log('主要链接信息:', linkInfo.filter(link => link.text && link.text.length > 0));
    
    // 截图记录导航结构
    await page.screenshot({ path: 'test-results/03-navigation.png' });
  });

  test('04. 租户管理功能模块测试', async ({ page }) => {
    console.log('🔍 测试: 租户管理功能模块...');
    
    await page.waitForTimeout(2000);
    
    // 查找租户相关的链接和按钮
    const tenantLinks = await page.$$('a[href*="tenant"], button:has-text("租户"), a:has-text("租户"), a:has-text("tenant")');
    console.log(`找到 ${tenantLinks.length} 个租户相关元素`);
    
    // 尝试访问租户管理页面
    const tenantPaths = ['/tenant', '/tenants', '/tenant/list', '/admin/tenant'];
    
    for (const path of tenantPaths) {
      try {
        console.log(`尝试访问: ${path}`);
        await page.goto(path);
        await page.waitForTimeout(2000);
        
        const currentUrl = page.url();
        const pageContent = await page.textContent('body');
        
        console.log(`访问 ${path} - 当前URL: ${currentUrl}`);
        console.log(`页面内容预览: ${pageContent.substring(0, 150)}`);
        
        // 截图记录
        await page.screenshot({ path: `test-results/04-tenant-${path.replace(/\//g, '_')}.png` });
        
        // 查找租户管理相关元素
        const tables = await page.$$('table, .table');
        const forms = await page.$$('form');
        const buttons = await page.$$('button');
        
        console.log(`  - 表格: ${tables.length} 个`);
        console.log(`  - 表单: ${forms.length} 个`);
        console.log(`  - 按钮: ${buttons.length} 个`);
        
        if (tables.length > 0) {
          // 分析表格内容
          const tableHeaders = await page.$$('th, .table-header');
          const headerTexts = [];
          for (const header of tableHeaders) {
            const text = await header.textContent();
            if (text && text.trim()) {
              headerTexts.push(text.trim());
            }
          }
          console.log(`  - 表格头部: [${headerTexts.join(', ')}]`);
        }
        
      } catch (error) {
        console.log(`无法访问 ${path}: ${error.message}`);
      }
    }
  });

  test('05. 用户管理功能模块测试', async ({ page }) => {
    console.log('🔍 测试: 用户管理功能模块...');
    
    const userPaths = ['/user', '/users', '/user/list', '/admin/user', '/admin/users'];
    
    for (const path of userPaths) {
      try {
        console.log(`尝试访问: ${path}`);
        await page.goto(path);
        await page.waitForTimeout(2000);
        
        const currentUrl = page.url();
        const pageContent = await page.textContent('body');
        
        console.log(`访问 ${path} - 当前URL: ${currentUrl}`);
        
        // 检查是否是有效的用户管理页面
        const hasUserContent = pageContent.includes('用户') || 
                              pageContent.includes('User') || 
                              pageContent.includes('管理') ||
                              pageContent.includes('列表');
        
        if (hasUserContent) {
          console.log(`✅ 发现用户管理相关内容`);
          
          // 截图记录
          await page.screenshot({ path: `test-results/05-user-${path.replace(/\//g, '_')}.png` });
          
          // 分析页面功能
          const addButtons = await page.$$('button:has-text("添加"), button:has-text("新增"), button:has-text("Add"), .btn-add, .add-btn');
          const editButtons = await page.$$('button:has-text("编辑"), button:has-text("Edit"), .btn-edit, .edit-btn');
          const deleteButtons = await page.$$('button:has-text("删除"), button:has-text("Delete"), .btn-delete, .delete-btn');
          
          console.log(`  - 添加按钮: ${addButtons.length} 个`);
          console.log(`  - 编辑按钮: ${editButtons.length} 个`);
          console.log(`  - 删除按钮: ${deleteButtons.length} 个`);
          
          // 查找搜索功能
          const searchInputs = await page.$$('input[type="search"], input[placeholder*="搜索"], input[placeholder*="search"], .search-input');
          console.log(`  - 搜索输入框: ${searchInputs.length} 个`);
        }
        
      } catch (error) {
        console.log(`无法访问 ${path}: ${error.message}`);
      }
    }
  });

  test('06. 系统配置功能模块测试', async ({ page }) => {
    console.log('🔍 测试: 系统配置功能模块...');
    
    const configPaths = ['/config', '/settings', '/system', '/admin/config', '/admin/settings'];
    
    for (const path of configPaths) {
      try {
        console.log(`尝试访问: ${path}`);
        await page.goto(path);
        await page.waitForTimeout(2000);
        
        const pageContent = await page.textContent('body');
        const hasConfigContent = pageContent.includes('配置') || 
                                pageContent.includes('设置') || 
                                pageContent.includes('Config') ||
                                pageContent.includes('Settings');
        
        if (hasConfigContent) {
          console.log(`✅ 发现系统配置相关内容`);
          
          // 截图记录
          await page.screenshot({ path: `test-results/06-config-${path.replace(/\//g, '_')}.png` });
          
          // 分析配置项
          const inputs = await page.$$('input, select, textarea');
          const labels = await page.$$('label');
          
          console.log(`  - 配置输入框: ${inputs.length} 个`);
          console.log(`  - 配置标签: ${labels.length} 个`);
          
          // 获取前几个配置项的信息
          for (let i = 0; i < Math.min(labels.length, 10); i++) {
            const labelText = await labels[i].textContent();
            if (labelText && labelText.trim()) {
              console.log(`  - 配置项: ${labelText.trim()}`);
            }
          }
        }
        
      } catch (error) {
        console.log(`无法访问 ${path}: ${error.message}`);
      }
    }
  });

  test('07. 数据统计和报表功能测试', async ({ page }) => {
    console.log('🔍 测试: 数据统计和报表功能...');
    
    const statsPaths = ['/stats', '/statistics', '/reports', '/dashboard', '/admin/stats', '/admin/dashboard'];
    
    for (const path of statsPaths) {
      try {
        console.log(`尝试访问: ${path}`);
        await page.goto(path);
        await page.waitForTimeout(2000);
        
        const pageContent = await page.textContent('body');
        const hasStatsContent = pageContent.includes('统计') || 
                               pageContent.includes('报表') || 
                               pageContent.includes('Dashboard') ||
                               pageContent.includes('数据') ||
                               pageContent.includes('图表');
        
        if (hasStatsContent) {
          console.log(`✅ 发现统计报表相关内容`);
          
          // 截图记录
          await page.screenshot({ path: `test-results/07-stats-${path.replace(/\//g, '_')}.png` });
          
          // 查找图表元素
          const charts = await page.$$('canvas, .chart, .graph, svg');
          const tables = await page.$$('table, .data-table');
          const cards = await page.$$('.card, .stat-card, .dashboard-card');
          
          console.log(`  - 图表元素: ${charts.length} 个`);
          console.log(`  - 数据表格: ${tables.length} 个`);
          console.log(`  - 统计卡片: ${cards.length} 个`);
          
          // 查找导出功能
          const exportButtons = await page.$$('button:has-text("导出"), button:has-text("Export"), .export-btn');
          console.log(`  - 导出按钮: ${exportButtons.length} 个`);
        }
        
      } catch (error) {
        console.log(`无法访问 ${path}: ${error.message}`);
      }
    }
  });

  test('08. API端点和数据接口测试', async ({ page }) => {
    console.log('🔍 测试: API端点和数据接口...');
    
    const apiPaths = ['/api', '/api/status', '/api/health', '/admin/api'];
    
    for (const path of apiPaths) {
      try {
        console.log(`尝试访问API: ${path}`);
        const response = await page.goto(path);
        
        console.log(`API ${path} 响应状态: ${response.status()}`);
        
        if (response.ok()) {
          const contentType = response.headers()['content-type'];
          console.log(`  - Content-Type: ${contentType}`);
          
          if (contentType && contentType.includes('application/json')) {
            const jsonData = await response.json();
            console.log(`  - JSON响应:`, JSON.stringify(jsonData, null, 2).substring(0, 200));
          } else {
            const textData = await response.text();
            console.log(`  - 文本响应:`, textData.substring(0, 200));
          }
        }
        
      } catch (error) {
        console.log(`API ${path} 访问失败: ${error.message}`);
      }
    }
  });

  test('09. 响应式设计和移动端适配测试', async ({ page }) => {
    console.log('🔍 测试: 响应式设计和移动端适配...');
    
    // 测试不同屏幕尺寸
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 1024, height: 768, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];
    
    for (const viewport of viewports) {
      console.log(`测试 ${viewport.name} 视口 (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto('/');
      await page.waitForTimeout(2000);
      
      // 截图记录不同尺寸下的页面
      await page.screenshot({ path: `test-results/09-responsive-${viewport.name.toLowerCase()}.png` });
      
      // 检查布局元素
      const sidebar = await page.$('.sidebar, .nav-sidebar');
      const navbar = await page.$('.navbar, .nav, .header');
      const content = await page.$('.content, .main-content, main');
      
      console.log(`  - 侧边栏: ${sidebar ? '存在' : '不存在'}`);
      console.log(`  - 导航栏: ${navbar ? '存在' : '不存在'}`);
      console.log(`  - 主内容区: ${content ? '存在' : '不存在'}`);
    }
  });

  test('10. 页面性能和加载速度测试', async ({ page }) => {
    console.log('🔍 测试: 页面性能和加载速度...');
    
    // 开始性能监控
    await page.goto('about:blank');
    
    const startTime = Date.now();
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    const endTime = Date.now();
    
    const loadTime = endTime - startTime;
    console.log(`页面加载时间: ${loadTime}ms`);
    
    // 检查页面资源
    const images = await page.$$('img');
    const scripts = await page.$$('script[src]');
    const styles = await page.$$('link[rel="stylesheet"]');
    
    console.log(`  - 图片资源: ${images.length} 个`);
    console.log(`  - JavaScript文件: ${scripts.length} 个`);
    console.log(`  - CSS文件: ${styles.length} 个`);
    
    // 检查是否有404错误
    let errorCount = 0;
    page.on('response', response => {
      if (response.status() >= 400) {
        console.log(`❌ 资源加载错误: ${response.url()} - ${response.status()}`);
        errorCount++;
      }
    });
    
    await page.reload();
    await page.waitForTimeout(3000);
    
    console.log(`总计资源错误: ${errorCount} 个`);
  });

  test('11. 安全性和权限控制测试', async ({ page }) => {
    console.log('🔍 测试: 安全性和权限控制...');
    
    // 测试未认证访问
    const protectedPaths = ['/admin', '/user', '/tenant', '/config', '/stats'];
    
    for (const path of protectedPaths) {
      try {
        await page.goto(path);
        await page.waitForTimeout(1000);
        
        const currentUrl = page.url();
        const isRedirected = currentUrl !== `http://localhost:4000${path}`;
        
        console.log(`访问 ${path}:`);
        console.log(`  - 当前URL: ${currentUrl}`);
        console.log(`  - 是否重定向: ${isRedirected ? '是' : '否'}`);
        
        if (isRedirected) {
          console.log(`  ✅ 路径 ${path} 有访问控制`);
        } else {
          console.log(`  ⚠️  路径 ${path} 可能缺少访问控制`);
        }
        
      } catch (error) {
        console.log(`访问 ${path} 时出错: ${error.message}`);
      }
    }
    
    // 检查HTTPS重定向
    console.log('检查安全头和HTTPS设置...');
    const response = await page.goto('/');
    const headers = response.headers();
    
    console.log('响应头安全检查:');
    console.log(`  - X-Frame-Options: ${headers['x-frame-options'] || '未设置'}`);
    console.log(`  - X-Content-Type-Options: ${headers['x-content-type-options'] || '未设置'}`);
    console.log(`  - Content-Security-Policy: ${headers['content-security-policy'] || '未设置'}`);
  });

  test('12. 综合功能流程测试', async ({ page }) => {
    console.log('🔍 测试: 综合功能流程...');
    
    await page.waitForTimeout(2000);
    
    // 模拟完整的用户操作流程
    console.log('执行综合操作流程测试...');
    
    // 1. 尝试访问主要功能页面
    const mainPages = ['/', '/admin', '/user', '/tenant'];
    
    for (const mainPage of mainPages) {
      try {
        console.log(`步骤: 访问 ${mainPage}`);
        await page.goto(mainPage);
        await page.waitForTimeout(1000);
        
        const title = await page.title();
        const url = page.url();
        
        console.log(`  - 页面标题: ${title}`);
        console.log(`  - 当前URL: ${url}`);
        
        // 尝试与页面互动
        const clickableElements = await page.$$('button, a, input[type="submit"]');
        console.log(`  - 可点击元素: ${clickableElements.length} 个`);
        
        // 截图记录
        await page.screenshot({ path: `test-results/12-workflow-${mainPage.replace(/\//g, '_') || 'home'}.png` });
        
      } catch (error) {
        console.log(`访问 ${mainPage} 时出错: ${error.message}`);
      }
    }
    
    // 2. 测试表单提交（如果存在）
    const forms = await page.$$('form');
    console.log(`发现 ${forms.length} 个表单`);
    
    if (forms.length > 0) {
      console.log('测试表单功能...');
      // 这里可以添加更详细的表单测试逻辑
    }
  });

});