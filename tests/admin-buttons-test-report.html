
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理按钮测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .error { background-color: #ffe6e6; }
    </style>
</head>
<body>
    <h1>后台管理按钮功能测试报告</h1>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <p><strong>总测试数:</strong> 9</p>
        <p><strong>通过:</strong> <span class="pass">4</span></p>
        <p><strong>失败:</strong> <span class="fail">5</span></p>
        <p><strong>测试时间:</strong> 19914ms</p>
        <p><strong>执行时间:</strong> 2025-08-27T06:56:57.189Z</p>
    </div>

    <h2>详细结果</h2>
    <table>
        <thead>
            <tr>
                <th>测试名称</th>
                <th>状态</th>
                <th>错误信息</th>
                <th>时间戳</th>
            </tr>
        </thead>
        <tbody>
            
                <tr class="error">
                    <td>JS函数-全选租户函数</td>
                    <td class="fail">FAIL</td>
                    <td>函数不存在</td>
                    <td>2025-08-27T06:57:06.708Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-切换全选函数</td>
                    <td class="fail">FAIL</td>
                    <td>函数不存在</td>
                    <td>2025-08-27T06:57:06.718Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-获取选中租户ID函数</td>
                    <td class="fail">FAIL</td>
                    <td>函数不存在</td>
                    <td>2025-08-27T06:57:06.719Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-批量更新状态函数</td>
                    <td class="fail">FAIL</td>
                    <td>函数不存在</td>
                    <td>2025-08-27T06:57:06.721Z</td>
                </tr>
            
                <tr class="error">
                    <td>JS函数-导出租户函数</td>
                    <td class="fail">FAIL</td>
                    <td>函数不存在</td>
                    <td>2025-08-27T06:57:06.722Z</td>
                </tr>
            
                <tr class="">
                    <td>控制台错误检查-http://localhost:4001</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:57:09.317Z</td>
                </tr>
            
                <tr class="">
                    <td>控制台错误检查-http://localhost:4001/tenants</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:57:11.915Z</td>
                </tr>
            
                <tr class="">
                    <td>控制台错误检查-http://localhost:4001/tenants/create</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:57:14.498Z</td>
                </tr>
            
                <tr class="">
                    <td>控制台错误检查-http://localhost:4001/tenants/subscriptions</td>
                    <td class="pass">PASS</td>
                    <td>-</td>
                    <td>2025-08-27T06:57:17.086Z</td>
                </tr>
            
        </tbody>
    </table>
</body>
</html>