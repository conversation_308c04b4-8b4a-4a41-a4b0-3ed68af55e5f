/**
 * Playwright全局清理
 * 在所有测试完成后执行的清理操作
 */

const fs = require('fs');
const path = require('path');

async function globalTeardown(config) {
  console.log('🧹 开始全局测试清理...');
  
  // 读取测试会话信息
  const sessionInfoPath = path.join(process.cwd(), 'test-results', 'test-session-info.json');
  let sessionInfo = {};
  
  if (fs.existsSync(sessionInfoPath)) {
    sessionInfo = JSON.parse(fs.readFileSync(sessionInfoPath, 'utf8'));
  }
  
  // 更新会话信息
  sessionInfo.endTime = new Date().toISOString();
  sessionInfo.duration = sessionInfo.startTime ? 
    new Date(sessionInfo.endTime) - new Date(sessionInfo.startTime) : 0;
  
  fs.writeFileSync(sessionInfoPath, JSON.stringify(sessionInfo, null, 2));
  
  // 生成测试总结
  const summaryPath = path.join(process.cwd(), 'test-results', 'test-summary.md');
  const summary = `# 后台管理中心功能审查测试总结

## 测试信息
- **开始时间**: ${sessionInfo.startTime}
- **结束时间**: ${sessionInfo.endTime}
- **测试时长**: ${Math.round(sessionInfo.duration / 1000)}秒
- **测试类型**: 功能审查和缺失分析
- **基础URL**: ${sessionInfo.baseUrl}

## 测试目标
1. 审查现有后台管理功能的完整性
2. 识别功能缺失和问题
3. 基于Context7最佳实践提供改进建议
4. 为后续开发提供详细的功能清单

## 报告文件
- 详细审查报告: \`test-results/admin-functionality-audit-report.json\`
- Playwright HTML报告: \`test-results/playwright-report/index.html\`
- 测试结果JSON: \`test-results/test-results.json\`

## 下一步行动
请查看详细的审查报告，根据发现的功能缺失制定开发计划。
`;

  fs.writeFileSync(summaryPath, summary);
  
  console.log('📊 测试总结已生成:', summaryPath);
  console.log('✅ 全局清理完成');
}

module.exports = globalTeardown;
