// 智慧养鹅SAAS平台测试配置文件
module.exports = {
  // 服务器配置
  servers: {
    backend: {
      url: 'http://localhost:3001',
      healthEndpoint: '/api/health'
    },
    saasAdmin: {
      url: 'http://localhost:3002',
      healthEndpoint: '/'
    },
    miniprogram: {
      url: 'http://localhost:3000',
      healthEndpoint: '/health'
    }
  },

  // 测试用户配置
  testUsers: {
    superAdmin: {
      username: 'super_admin',
      password: 'admin123',
      role: 'super_admin'
    },
    tenantAdmin: {
      username: 'tenant_admin',
      password: 'tenant123',
      role: 'tenant_admin'
    },
    normalUser: {
      username: 'test_user',
      password: 'user123',
      role: 'user'
    }
  },

  // 超时配置
  timeouts: {
    api: 10000,        // API请求超时 10秒
    page: 30000,       // 页面加载超时 30秒
    element: 5000,     // 元素查找超时 5秒
    navigation: 15000  // 页面导航超时 15秒
  },

  // Playwright配置
  playwright: {
    headless: false,
    viewport: {
      width: 1280,
      height: 720
    },
    browsers: ['chromium'],
    retries: 2,
    workers: 1
  },

  // 测试数据配置
  testData: {
    tenant: {
      name: '测试租户',
      code: 'test_tenant',
      contact: '测试联系人',
      phone: '13800138000',
      email: '<EMAIL>'
    },
    flock: {
      name: '测试鹅群',
      breed: '白鹅',
      count: 100,
      birthDate: '2024-01-01'
    }
  },

  // 测试环境配置
  environment: {
    database: {
      host: 'localhost',
      port: 3306,
      database: 'smart_goose_test',
      username: 'test_user',
      password: 'test_password'
    },
    redis: {
      host: 'localhost',
      port: 6379,
      database: 1
    }
  },

  // 报告配置
  reporting: {
    outputDir: './test-results',
    formats: ['json', 'html', 'junit'],
    screenshots: true,
    videos: false
  },

  // 测试分类配置
  categories: {
    unit: {
      pattern: 'tests/unit/**/*.test.js',
      timeout: 5000
    },
    integration: {
      pattern: 'tests/integration/**/*.test.js',
      timeout: 15000
    },
    e2e: {
      pattern: 'tests/e2e/**/*.spec.js',
      timeout: 60000
    }
  },

  // 健康检查配置
  healthCheck: {
    enabled: true,
    endpoints: [
      'http://localhost:3001/api/health',
      'http://localhost:3002/'
    ],
    timeout: 5000,
    retries: 3
  }
};
