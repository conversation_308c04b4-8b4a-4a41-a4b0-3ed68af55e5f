/**
 * Playwright全局设置
 * 在所有测试开始前执行的初始化操作
 */

const fs = require('fs');
const path = require('path');

async function globalSetup(config) {
  console.log('🚀 开始全局测试设置...');
  
  // 确保测试结果目录存在
  const testResultsDir = path.join(process.cwd(), 'test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
    console.log('📁 创建测试结果目录:', testResultsDir);
  }
  
  // 清理旧的测试报告
  const reportFiles = [
    'test-results/admin-functionality-audit-report.json',
    'test-results/test-results.json'
  ];
  
  reportFiles.forEach(file => {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
      console.log('🗑️ 清理旧报告:', file);
    }
  });
  
  // 检查后台服务是否运行
  try {
    const response = await fetch('http://localhost:4001/api/health');
    if (response.ok) {
      console.log('✅ 后台服务运行正常');
    } else {
      console.warn('⚠️ 后台服务响应异常');
    }
  } catch (error) {
    console.warn('⚠️ 无法连接到后台服务，测试可能会失败');
  }
  
  // 创建测试开始时间戳
  const startTime = new Date().toISOString();
  fs.writeFileSync(
    path.join(testResultsDir, 'test-session-info.json'),
    JSON.stringify({
      startTime,
      testType: 'admin-functionality-audit',
      baseUrl: 'http://localhost:4001'
    }, null, 2)
  );
  
  console.log('✅ 全局设置完成');
}

module.exports = globalSetup;
