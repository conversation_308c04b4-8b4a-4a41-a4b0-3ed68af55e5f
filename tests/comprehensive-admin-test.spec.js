const { test, expect } = require('@playwright/test');

/**
 * 智慧养鹅后台管理中心全面测试套件
 * 测试范围：核心功能模块、页面可用性、交互功能、数据操作
 */

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:4000',
  apiURL: 'http://localhost:4000',
  timeout: 30000,
  credentials: {
    admin: { username: 'admin', password: 'admin123' },
    demo: { username: 'demo', password: 'demo123' },
    manager: { username: 'manager', password: 'manager123' }
  }
};

// 测试结果收集器
let testResults = {
  passed: 0,
  failed: 0,
  issues: [],
  coverage: {
    pages: [],
    apis: [],
    features: []
  }
};

test.describe('智慧养鹅后台管理中心 - 全面功能测试', () => {
  
  test.beforeAll(async () => {
    console.log('🚀 开始智慧养鹅后台管理中心全面测试');
    console.log(`📍 管理后台地址: ${TEST_CONFIG.baseURL}`);
    console.log(`🔗 API服务地址: ${TEST_CONFIG.apiURL}`);
  });

  test.afterAll(async () => {
    console.log('\n📊 测试结果汇总:');
    console.log(`✅ 通过: ${testResults.passed}`);
    console.log(`❌ 失败: ${testResults.failed}`);
    console.log(`📄 页面覆盖: ${testResults.coverage.pages.length}`);
    console.log(`🔌 API覆盖: ${testResults.coverage.apis.length}`);
    
    if (testResults.issues.length > 0) {
      console.log('\n🐛 发现的问题:');
      testResults.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
  });

  // 1. 系统健康检查
  test('系统健康检查', async ({ page }) => {
    try {
      // 检查API服务健康状态
      const apiResponse = await page.request.get(`${TEST_CONFIG.apiURL}/api/health`);
      expect(apiResponse.status()).toBe(200);
      
      const healthData = await apiResponse.json();
      expect(healthData.success).toBe(true);
      expect(healthData.service).toBe('Smart Goose API');
      
      testResults.coverage.apis.push('/api/health');
      testResults.passed++;
      
      console.log('✅ API服务健康检查通过');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`API健康检查失败: ${error.message}`);
      throw error;
    }
  });

  // 2. 管理后台首页访问测试
  test('管理后台首页访问', async ({ page }) => {
    try {
      await page.goto(TEST_CONFIG.baseURL);
      
      // 检查是否重定向到登录页面
      await expect(page).toHaveURL(/.*\/auth\/login/);
      
      // 检查登录页面元素
      await expect(page.locator('h3')).toContainText('智慧养鹅SAAS');
      await expect(page.locator('input[name="username"]')).toBeVisible();
      await expect(page.locator('input[name="password"]')).toBeVisible();
      await expect(page.locator('button[type="submit"]')).toBeVisible();
      
      testResults.coverage.pages.push('/auth/login');
      testResults.passed++;
      
      console.log('✅ 管理后台首页访问正常，正确重定向到登录页面');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`首页访问失败: ${error.message}`);
      throw error;
    }
  });

  // 3. 用户认证模块测试
  test('用户登录功能测试', async ({ page }) => {
    try {
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      
      // 测试无效登录
      await page.fill('input[name="username"]', 'invalid');
      await page.fill('input[name="password"]', 'invalid');
      await page.click('button[type="submit"]');
      
      // 等待错误消息
      await page.waitForTimeout(1000);
      
      // 测试有效登录
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      
      // 等待重定向到仪表板
      await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
      
      // 验证仪表板页面元素
      await expect(page.locator('h1, h2, .page-title')).toBeVisible();
      
      testResults.coverage.pages.push('/dashboard');
      testResults.coverage.features.push('用户登录');
      testResults.passed++;
      
      console.log('✅ 用户登录功能测试通过');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`用户登录测试失败: ${error.message}`);
      throw error;
    }
  });

  // 4. 仪表板数据加载测试
  test('仪表板数据统计测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);
      
      // 检查仪表板统计数据
      await page.waitForTimeout(2000); // 等待数据加载
      
      // 查找统计卡片或数据显示元素
      const statsElements = await page.locator('.card, .stat-card, .dashboard-stat, .metric-card').count();
      expect(statsElements).toBeGreaterThan(0);
      
      testResults.coverage.features.push('仪表板统计');
      testResults.passed++;
      
      console.log('✅ 仪表板数据统计测试通过');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`仪表板数据测试失败: ${error.message}`);
      throw error;
    }
  });

  // 5. 导航菜单测试
  test('导航菜单功能测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);
      
      // 测试主要导航链接
      const navigationLinks = [
        { text: '用户管理', url: '/users' },
        { text: '健康管理', url: '/health' },
        { text: '生产管理', url: '/production' },
        { text: '系统设置', url: '/settings' }
      ];
      
      for (const link of navigationLinks) {
        try {
          // 查找并点击导航链接
          const navLink = page.locator(`a:has-text("${link.text}"), .nav-link:has-text("${link.text}"), .menu-item:has-text("${link.text}")`).first();
          
          if (await navLink.count() > 0) {
            await navLink.click();
            await page.waitForTimeout(1000);
            
            // 检查URL是否包含预期路径
            const currentURL = page.url();
            if (currentURL.includes(link.url)) {
              testResults.coverage.pages.push(link.url);
              console.log(`✅ 导航到 ${link.text} 成功`);
            }
          }
        } catch (navError) {
          console.log(`⚠️ 导航链接 ${link.text} 可能不存在或不可点击`);
        }
      }
      
      testResults.coverage.features.push('导航菜单');
      testResults.passed++;
      
      console.log('✅ 导航菜单功能测试完成');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`导航菜单测试失败: ${error.message}`);
      throw error;
    }
  });

  // 6. 用户管理模块测试
  test('用户管理模块测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);
      
      // 尝试访问用户管理页面
      await page.goto(`${TEST_CONFIG.baseURL}/users`);
      await page.waitForTimeout(2000);
      
      // 检查页面是否正常加载
      const pageTitle = await page.locator('h1, h2, .page-title').first();
      if (await pageTitle.count() > 0) {
        testResults.coverage.pages.push('/users');
        testResults.coverage.features.push('用户管理');
        console.log('✅ 用户管理页面访问成功');
      }
      
      testResults.passed++;
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`用户管理模块测试失败: ${error.message}`);
      console.log(`⚠️ 用户管理模块可能需要开发或修复: ${error.message}`);
    }
  });

  // 7. 响应式设计测试
  test('响应式设计测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);
      
      // 测试不同屏幕尺寸
      const viewports = [
        { width: 1920, height: 1080, name: '桌面大屏' },
        { width: 1366, height: 768, name: '桌面标准' },
        { width: 768, height: 1024, name: '平板' },
        { width: 375, height: 667, name: '手机' }
      ];
      
      for (const viewport of viewports) {
        await page.setViewportSize({ width: viewport.width, height: viewport.height });
        await page.waitForTimeout(500);
        
        // 检查页面是否仍然可用
        const isVisible = await page.locator('body').isVisible();
        expect(isVisible).toBe(true);
        
        console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}) 显示正常`);
      }
      
      testResults.coverage.features.push('响应式设计');
      testResults.passed++;
      
      console.log('✅ 响应式设计测试通过');
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`响应式设计测试失败: ${error.message}`);
      throw error;
    }
  });

  // 8. 退出登录测试
  test('退出登录功能测试', async ({ page }) => {
    try {
      // 先登录
      await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
      await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
      await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/dashboard/);

      // 查找用户下拉菜单按钮
      const userDropdown = page.locator('#navbarDropdown, .dropdown-toggle').first();

      if (await userDropdown.count() > 0) {
        // 点击用户头像展开下拉菜单
        await userDropdown.click();
        await page.waitForTimeout(500);

        // 查找退出登录链接
        const logoutLink = page.locator('#logoutBtn, a:has-text("退出登录")').first();

        if (await logoutLink.count() > 0) {
          await logoutLink.click();
          await page.waitForTimeout(2000);

          // 验证已重定向到登录页面
          await expect(page.locator('input[name="username"]')).toBeVisible();

          testResults.coverage.features.push('退出登录');
          console.log('✅ 退出登录功能正常');
        } else {
          console.log('⚠️ 未找到退出登录按钮，尝试直接访问退出URL');
          await page.goto(`${TEST_CONFIG.baseURL}/auth/logout`);
          await page.waitForTimeout(1000);
        }
      } else {
        // 尝试直接访问退出URL
        await page.goto(`${TEST_CONFIG.baseURL}/auth/logout`);
        await page.waitForTimeout(1000);
      }

      testResults.passed++;
    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`退出登录测试失败: ${error.message}`);
      throw error;
    }
  });

  // ===== 全面模块测试开始 =====

  // 通用登录函数
  async function loginAsAdmin(page) {
    await page.goto(`${TEST_CONFIG.baseURL}/auth/login`);
    await page.fill('input[name="username"]', TEST_CONFIG.credentials.admin.username);
    await page.fill('input[name="password"]', TEST_CONFIG.credentials.admin.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/.*\/dashboard/, { timeout: 10000 });
  }

  // 通用按钮测试函数
  async function testButton(page, selector, testName, moduleName) {
    try {
      console.log(`[${moduleName}] 测试: ${testName}`);

      const button = page.locator(selector);
      const count = await button.count();

      if (count === 0) {
        throw new Error('按钮不存在');
      }

      if (count > 1) {
        console.log(`  ⚠️  发现${count}个匹配的元素，使用第一个`);
        await button.first().click();
      } else {
        await button.click();
      }

      await page.waitForTimeout(2000);

      testResults.passed++;
      testResults.coverage.features.push(`${moduleName}-${testName}`);
      console.log(`  ✅ ${testName} - 测试通过`);

    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`[${moduleName}] ${testName}: ${error.message}`);
      console.error(`  ❌ ${testName} - 测试失败:`, error.message);
    }
  }

  // 通用页面访问测试函数
  async function testPageAccess(page, url, pageName, moduleName) {
    try {
      console.log(`[${moduleName}] 访问页面: ${pageName}`);
      await page.goto(url);
      await page.waitForLoadState('networkidle');

      const title = await page.title();
      if (title.includes('错误') || title.includes('Error')) {
        throw new Error('页面加载错误');
      }

      testResults.passed++;
      testResults.coverage.pages.push(url);
      console.log(`  ✅ ${pageName} - 页面访问成功`);

    } catch (error) {
      testResults.failed++;
      testResults.issues.push(`[${moduleName}] 访问${pageName}: ${error.message}`);
      console.error(`  ❌ ${pageName} - 页面访问失败:`, error.message);
    }
  }

  // 1. 租户管理模块全面测试
  test('租户管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    // 测试租户管理页面访问
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/tenants`, '租户管理主页', '租户管理');

    // 测试基础按钮（不需要选中租户的按钮）
    const basicButtons = [
      { selector: '.btn-group a[href="/tenants/create"]', name: '创建租户按钮' },
      { selector: '.btn-group button[onclick*="exportTenants"]', name: '导出数据按钮' },
      { selector: '.btn-group a[href="/tenants/subscriptions"]', name: '订阅管理按钮' },
      { selector: '.btn-group a[href="/tenants/usage"]', name: '使用统计按钮' }
    ];

    for (const button of basicButtons) {
      await testButton(page, button.selector, button.name, '租户管理');
      // 返回到租户页面
      await page.goto(`${TEST_CONFIG.baseURL}/tenants`);
      await page.waitForLoadState('networkidle');
    }

    // 测试需要选中租户的批量操作按钮
    await page.goto(`${TEST_CONFIG.baseURL}/tenants`);
    await page.waitForLoadState('networkidle');

    // 先检查是否有租户数据
    const tenantCheckboxes = page.locator('.tenant-checkbox');
    const checkboxCount = await tenantCheckboxes.count();

    if (checkboxCount > 0) {
      // 选中第一个租户
      await tenantCheckboxes.first().check();
      await page.waitForTimeout(500);

      // 现在测试批量操作按钮（应该已启用）
      const batchButtons = [
        { selector: '.btn-group button[onclick*="selectAllTenants"]', name: '全选按钮' },
        { selector: '.btn-group button[onclick*="batchUpdateTenantStatus"]', name: '批量操作按钮' }
      ];

      for (const button of batchButtons) {
        await testButton(page, button.selector, button.name, '租户管理');
        await page.goto(`${TEST_CONFIG.baseURL}/tenants`);
        await page.waitForLoadState('networkidle');
        // 重新选中租户以保持批量操作按钮启用状态
        if (await tenantCheckboxes.count() > 0) {
          await tenantCheckboxes.first().check();
          await page.waitForTimeout(500);
        }
      }
    } else {
      console.log('⚠️ 没有租户数据，跳过批量操作按钮测试');
      testResults.issues.push('[租户管理] 没有租户数据，无法测试批量操作功能');
    }

    // 测试租户相关子页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/tenants/create`, '创建租户页面', '租户管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/tenants/subscriptions`, '订阅管理页面', '租户管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/tenants/usage`, '使用统计页面', '租户管理');
  });

  // 2. 用户管理模块全面测试
  test('用户管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    // 测试用户管理页面访问
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/users`, '用户管理主页', '用户管理');

    // 测试用户管理页面的所有按钮
    const userButtons = [
      { selector: 'a[href="/users/create"], .btn:has-text("添加用户")', name: '添加用户按钮' },
      { selector: 'button:has-text("搜索"), .btn-search', name: '搜索按钮' },
      { selector: 'button:has-text("导出"), .btn-export', name: '导出按钮' },
      { selector: '.btn-edit, a:has-text("编辑")', name: '编辑按钮' },
      { selector: '.btn-delete, button:has-text("删除")', name: '删除按钮' }
    ];

    for (const button of userButtons) {
      await testButton(page, button.selector, button.name, '用户管理');
      await page.goto(`${TEST_CONFIG.baseURL}/users`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 3. 鹅群管理模块全面测试
  test('鹅群管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/flocks`, '鹅群管理主页', '鹅群管理');

    // 测试基础功能按钮
    const basicButtons = [
      { selector: 'button[data-bs-target="#addFlockModal"], .btn:has-text("新增鹅群")', name: '新增鹅群按钮' },
      { selector: '#searchBtn, button:has-text("搜索")', name: '搜索按钮' },
      { selector: '#resetBtn, button:has-text("重置")', name: '重置按钮' },
      { selector: '#exportExcel, a:has-text("Excel")', name: '导出Excel按钮' },
      { selector: '#exportPdf, a:has-text("PDF")', name: '导出PDF按钮' }
    ];

    for (const button of basicButtons) {
      await testButton(page, button.selector, button.name, '鹅群管理');
      await page.goto(`${TEST_CONFIG.baseURL}/flocks`);
      await page.waitForLoadState('networkidle');
    }

    // 注意：编辑、删除、查看按钮需要有数据才会显示，这里先测试基础功能
    console.log('⚠️ 编辑、删除、查看按钮需要有鹅群数据才能测试');
  });

  // 4. 生产管理模块全面测试
  test('生产管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/production`, '生产管理主页', '生产管理');

    const productionButtons = [
      { selector: 'button[data-bs-target="#feedRecordModal"], .btn:has-text("饲料投喂记录")', name: '饲料投喂记录按钮' },
      { selector: 'button[data-bs-target="#eggProductionModal"], .btn:has-text("产蛋记录")', name: '产蛋记录按钮' },
      { selector: '#exportExcel, a:has-text("Excel")', name: '导出Excel按钮' },
      { selector: '#exportPdf, a:has-text("PDF")', name: '导出PDF按钮' }
    ];

    for (const button of productionButtons) {
      await testButton(page, button.selector, button.name, '生产管理');
      await page.goto(`${TEST_CONFIG.baseURL}/production`);
      await page.waitForLoadState('networkidle');
    }

    // 注意：编辑、删除按钮需要有生产记录数据才会显示
    console.log('⚠️ 编辑、删除按钮需要有生产记录数据才能测试');
  });

  // 5. 健康管理模块全面测试
  test('健康管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/health`, '健康管理主页', '健康管理');

    const healthButtons = [
      { selector: 'button[data-bs-target="#vaccinationModal"], .btn:has-text("疫苗接种")', name: '疫苗接种按钮' },
      { selector: 'button[data-bs-target="#treatmentModal"], .btn:has-text("疾病治疗")', name: '疾病治疗按钮' },
      { selector: 'button[data-bs-target="#inspectionModal"], .btn:has-text("健康检查")', name: '健康检查按钮' },
      { selector: '#exportExcel, a:has-text("Excel")', name: '导出Excel按钮' },
      { selector: '#exportPdf, a:has-text("PDF")', name: '导出PDF按钮' }
    ];

    for (const button of healthButtons) {
      await testButton(page, button.selector, button.name, '健康管理');
      await page.goto(`${TEST_CONFIG.baseURL}/health`);
      await page.waitForLoadState('networkidle');
    }

    // 注意：编辑、删除按钮需要有健康记录数据才会显示
    console.log('⚠️ 编辑、删除按钮需要有健康记录数据才能测试');
  });

  // 6. 财务数据汇总模块全面测试
  test('财务数据汇总模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/finance`, '财务数据汇总主页', '财务数据汇总');

    const financeButtons = [
      { selector: '#timeRangeFilter', name: '时间范围过滤器' },
      { selector: 'button:has-text("导出汇总")', name: '导出汇总按钮' },
      { selector: 'button:has-text("刷新")', name: '刷新按钮' },
      { selector: 'button:has-text("刷新租户数据")', name: '刷新租户数据按钮' }
    ];

    for (const button of financeButtons) {
      await testButton(page, button.selector, button.name, '财务数据汇总');
      await page.goto(`${TEST_CONFIG.baseURL}/finance`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 7. 库存数据汇总模块全面测试
  test('库存数据汇总模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/inventory`, '库存数据汇总主页', '库存数据汇总');

    const inventoryButtons = [
      { selector: '#timeRangeFilter', name: '时间范围过滤器' },
      { selector: 'button:has-text("导出汇总")', name: '导出汇总按钮' },
      { selector: 'button:has-text("刷新")', name: '刷新按钮' }
    ];

    for (const button of inventoryButtons) {
      await testButton(page, button.selector, button.name, '库存数据汇总');
      await page.goto(`${TEST_CONFIG.baseURL}/inventory`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 8. 商城管理模块全面测试
  test('商城管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    // 测试商城概览页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall`, '商城概览主页', '商城管理');

    const mallOverviewButtons = [
      { selector: 'a[href="/mall/products"]', name: '商品管理入口' },
      { selector: 'a[href="/mall/orders"]', name: '订单管理入口' },
      { selector: 'a[href="/mall/categories"]', name: '分类管理入口' }
    ];

    for (const button of mallOverviewButtons) {
      await testButton(page, button.selector, button.name, '商城概览');
      await page.goto(`${TEST_CONFIG.baseURL}/mall`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 9. 商品管理模块测试
  test('商品管理模块测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/products`, '商品管理主页', '商品管理');

    const productButtons = [
      { selector: 'a[href="/mall/products/create"], .btn:has-text("添加商品")', name: '添加商品按钮' },
      { selector: 'button:has-text("导出商品")', name: '导出商品按钮' }
    ];

    for (const button of productButtons) {
      await testButton(page, button.selector, button.name, '商品管理');
      await page.goto(`${TEST_CONFIG.baseURL}/mall/products`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 10. 促销活动管理测试
  test('促销活动管理测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/promotions`, '促销活动管理主页', '促销活动管理');

    const promotionButtons = [
      { selector: 'button[data-bs-target="#createPromotionModal"]', name: '创建活动按钮' },
      { selector: 'button:has-text("导出数据")', name: '导出数据按钮' }
    ];

    for (const button of promotionButtons) {
      await testButton(page, button.selector, button.name, '促销活动管理');
      await page.goto(`${TEST_CONFIG.baseURL}/mall/promotions`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 11. 优惠券管理测试
  test('优惠券管理测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/coupons`, '优惠券管理主页', '优惠券管理');

    const couponButtons = [
      { selector: 'button[data-bs-target="#createCouponModal"]', name: '创建优惠券按钮' },
      { selector: 'button:has-text("批量生成")', name: '批量生成按钮' },
      { selector: 'button:has-text("导出数据")', name: '导出数据按钮' }
    ];

    for (const button of couponButtons) {
      await testButton(page, button.selector, button.name, '优惠券管理');
      await page.goto(`${TEST_CONFIG.baseURL}/mall/coupons`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 12. 评价管理测试
  test('评价管理测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/reviews`, '评价管理主页', '评价管理');

    const reviewButtons = [
      { selector: 'button:has-text("批量审核")', name: '批量审核按钮' },
      { selector: 'button:has-text("导出数据")', name: '导出数据按钮' }
    ];

    for (const button of reviewButtons) {
      await testButton(page, button.selector, button.name, '评价管理');
      await page.goto(`${TEST_CONFIG.baseURL}/mall/reviews`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 13. 销售分析测试
  test('销售分析测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/analytics`, '销售分析主页', '销售分析');

    const analyticsButtons = [
      { selector: '#timeRangeFilter', name: '时间范围过滤器' },
      { selector: 'button:has-text("导出报告")', name: '导出报告按钮' },
      { selector: 'button:has-text("刷新数据")', name: '刷新数据按钮' }
    ];

    for (const button of analyticsButtons) {
      await testButton(page, button.selector, button.name, '销售分析');
      await page.goto(`${TEST_CONFIG.baseURL}/mall/analytics`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 14. 统计报告测试
  test('统计报告测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/reports`, '统计报告主页', '统计报告');

    const reportButtons = [
      { selector: '#reportPeriod', name: '报告周期选择器' },
      { selector: 'button:has-text("导出报告")', name: '导出报告按钮' },
      { selector: 'button:has-text("刷新数据")', name: '刷新数据按钮' },
      { selector: 'a[href="/reports/platform"]', name: '平台统计入口' },
      { selector: 'a[href="/reports/revenue"]', name: '收入统计入口' },
      { selector: 'a[href="/reports/usage"]', name: '使用统计入口' },
      { selector: 'a[href="/reports/users"]', name: '用户分析入口' }
    ];

    for (const button of reportButtons) {
      await testButton(page, button.selector, button.name, '统计报告');
      await page.goto(`${TEST_CONFIG.baseURL}/reports`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 15. 知识库管理测试
  test('知识库管理测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/knowledge`, '知识库管理主页', '知识库管理');

    const knowledgeButtons = [
      { selector: 'a[href="/knowledge/create"]', name: '创建文章按钮' },
      { selector: 'a[href="/knowledge/categories"]', name: '分类管理按钮' }
    ];

    for (const button of knowledgeButtons) {
      await testButton(page, button.selector, button.name, '知识库管理');
      await page.goto(`${TEST_CONFIG.baseURL}/knowledge`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 16. 鹅价管理测试
  test('鹅价管理测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/goose-prices`, '鹅价管理主页', '鹅价管理');

    const priceButtons = [
      { selector: 'a[href="/goose-prices/create"]', name: '添加价格记录按钮' },
      { selector: 'a[href="/goose-prices/trends"]', name: '价格趋势按钮' }
    ];

    for (const button of priceButtons) {
      await testButton(page, button.selector, button.name, '鹅价管理');
      await page.goto(`${TEST_CONFIG.baseURL}/goose-prices`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 17. 系统管理子模块测试
  test('系统管理子模块测试', async ({ page }) => {
    await loginAsAdmin(page);

    // 测试系统备份页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/system/backup`, '系统备份主页', '数据备份');

    const backupButtons = [
      { selector: 'button:has-text("保存配置")', name: '保存配置按钮' },
      { selector: 'button[onclick="createBackup()"]', name: '立即备份按钮' }
    ];

    for (const button of backupButtons) {
      await testButton(page, button.selector, button.name, '系统备份');
      await page.goto(`${TEST_CONFIG.baseURL}/system/backup`);
      await page.waitForLoadState('networkidle');
    }

    // 测试系统日志页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/system/logs`, '系统日志主页', '系统日志');

    const logButtons = [
      { selector: 'button[onclick="refreshLogs()"]', name: '刷新日志按钮' },
      { selector: 'button[onclick="exportLogs()"]', name: '导出日志按钮' },
      { selector: 'button[onclick="applyFilters()"]', name: '应用过滤按钮' }
    ];

    for (const button of logButtons) {
      await testButton(page, button.selector, button.name, '系统日志');
      await page.goto(`${TEST_CONFIG.baseURL}/system/logs`);
      await page.waitForLoadState('networkidle');
    }

    // 测试系统维护页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/system/maintenance`, '系统维护主页', '系统状态');

    const maintenanceButtons = [
      { selector: 'button[onclick*="MaintenanceMode"]', name: '维护模式切换按钮' }
    ];

    for (const button of maintenanceButtons) {
      await testButton(page, button.selector, button.name, '系统维护');
      await page.goto(`${TEST_CONFIG.baseURL}/system/maintenance`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 18. AI配置管理测试
  test('AI配置管理测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/ai-config`, 'AI配置管理主页', 'AI配置管理');

    const aiConfigButtons = [
      { selector: 'button[onclick="openAddConfigModal()"]', name: '添加配置按钮' },
      { selector: 'button[onclick="refreshStats()"]', name: '刷新统计按钮' }
    ];

    for (const button of aiConfigButtons) {
      await testButton(page, button.selector, button.name, 'AI配置管理');
      await page.goto(`${TEST_CONFIG.baseURL}/ai-config`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 19. 监控管理测试
  test('监控管理测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/monitoring`, '监控管理主页', '性能监控');

    const monitoringButtons = [
      { selector: 'button:has-text("刷新数据")', name: '刷新数据按钮' },
      { selector: 'button:has-text("导出报告")', name: '导出报告按钮' }
    ];

    for (const button of monitoringButtons) {
      await testButton(page, button.selector, button.name, '监控管理');
      await page.goto(`${TEST_CONFIG.baseURL}/monitoring`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 20. 日志管理测试
  test('日志管理测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/logs`, '日志管理主页', '系统日志');

    const logManagementButtons = [
      { selector: 'button:has-text("刷新")', name: '刷新按钮' },
      { selector: 'button:has-text("导出")', name: '导出按钮' },
      { selector: 'button:has-text("清理日志")', name: '清理日志按钮' }
    ];

    for (const button of logManagementButtons) {
      await testButton(page, button.selector, button.name, '日志管理');
      await page.goto(`${TEST_CONFIG.baseURL}/logs`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 8. 报表系统模块全面测试
  test('报表系统模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/reports`, '报表中心主页', '报表系统');

    const reportButtons = [
      { selector: '.btn:has-text("生成报表")', name: '生成报表按钮' },
      { selector: '.btn:has-text("导出报表"), .btn-export', name: '导出报表按钮' },
      { selector: '.btn:has-text("打印"), .btn-print', name: '打印报表按钮' },
      { selector: '.btn:has-text("定时报表")', name: '定时报表设置按钮' },
      { selector: '.btn:has-text("筛选"), .btn-filter', name: '报表筛选按钮' }
    ];

    for (const button of reportButtons) {
      await testButton(page, button.selector, button.name, '报表系统');
      await page.goto(`${TEST_CONFIG.baseURL}/reports`);
      await page.waitForLoadState('networkidle');
    }

    // 测试报表子页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/reports/platform`, '平台报表页面', '报表系统');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/reports/revenue`, '收入报表页面', '报表系统');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/reports/usage`, '使用报表页面', '报表系统');
  });

  // 9. 系统管理模块全面测试
  test('系统管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/system`, '系统设置主页', '系统管理');

    const systemButtons = [
      { selector: '.btn:has-text("保存设置"), button[type="submit"]', name: '保存设置按钮' },
      { selector: '.btn:has-text("重置"), .btn-reset', name: '重置设置按钮' },
      { selector: '.btn:has-text("查看日志"), a[href*="/system/logs"]', name: '查看日志按钮' },
      { selector: '.btn:has-text("清理日志")', name: '清理日志按钮' },
      { selector: '.btn:has-text("创建备份"), a[href*="/system/backup"]', name: '创建备份按钮' },
      { selector: '.btn:has-text("系统维护")', name: '系统维护按钮' }
    ];

    for (const button of systemButtons) {
      await testButton(page, button.selector, button.name, '系统管理');
      await page.goto(`${TEST_CONFIG.baseURL}/system`);
      await page.waitForLoadState('networkidle');
    }

    // 测试系统管理子页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/system/logs`, '系统日志页面', '系统管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/system/backup`, '数据备份页面', '系统管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/system/maintenance`, '系统维护页面', '系统管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/system/monitoring`, '系统监控页面', '系统管理');
  });

  // 10. 知识库管理模块全面测试
  test('知识库管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/knowledge`, '知识库管理主页', '知识库管理');

    const knowledgeButtons = [
      { selector: 'a[href="/knowledge/create"], .btn:has-text("创建文章")', name: '创建文章按钮' },
      { selector: 'a[href="/knowledge/categories"], .btn:has-text("分类管理")', name: '分类管理按钮' },
      { selector: '.btn-edit, a:has-text("编辑")', name: '编辑文章按钮' },
      { selector: '.btn-delete, button:has-text("删除")', name: '删除文章按钮' },
      { selector: '.btn:has-text("发布")', name: '发布文章按钮' }
    ];

    for (const button of knowledgeButtons) {
      await testButton(page, button.selector, button.name, '知识库管理');
      await page.goto(`${TEST_CONFIG.baseURL}/knowledge`);
      await page.waitForLoadState('networkidle');
    }

    // 测试知识库子页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/knowledge/create`, '创建文章页面', '知识库管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/knowledge/categories`, '分类管理页面', '知识库管理');
  });

  // 11. 公告管理模块全面测试
  test('公告管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/announcements`, '公告管理主页', '公告管理');

    const announcementButtons = [
      { selector: 'a[href="/announcements/create"], .btn:has-text("创建公告")', name: '创建公告按钮' },
      { selector: '.btn-edit, a:has-text("编辑")', name: '编辑公告按钮' },
      { selector: '.btn-delete, button:has-text("删除")', name: '删除公告按钮' },
      { selector: '.btn:has-text("发布")', name: '发布公告按钮' },
      { selector: '.btn:has-text("撤回")', name: '撤回公告按钮' }
    ];

    for (const button of announcementButtons) {
      await testButton(page, button.selector, button.name, '公告管理');
      await page.goto(`${TEST_CONFIG.baseURL}/announcements`);
      await page.waitForLoadState('networkidle');
    }

    // 测试公告子页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/announcements/create`, '创建公告页面', '公告管理');
  });

  // 12. API管理模块全面测试
  test('API管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/api-management`, 'API管理主页', 'API管理');

    const apiButtons = [
      { selector: '.btn:has-text("API文档")', name: 'API文档按钮' },
      { selector: '.btn:has-text("测试API")', name: '测试API按钮' },
      { selector: '.btn:has-text("重置密钥")', name: '重置API密钥按钮' },
      { selector: '.btn:has-text("查看统计"), a[href*="/api-management/statistics"]', name: '查看统计按钮' },
      { selector: '.btn:has-text("导出日志")', name: '导出API日志按钮' }
    ];

    for (const button of apiButtons) {
      await testButton(page, button.selector, button.name, 'API管理');
      await page.goto(`${TEST_CONFIG.baseURL}/api-management`);
      await page.waitForLoadState('networkidle');
    }

    // 测试API管理子页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/api-management/monitor`, 'API监控页面', 'API管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/api-management/statistics`, 'API统计页面', 'API管理');
  });

  // 13. 平台用户管理模块全面测试
  test('平台用户管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/platform-users`, '平台用户管理主页', '平台用户管理');

    const platformUserButtons = [
      { selector: '.btn:has-text("添加用户"), a[href*="/platform-users/create"]', name: '添加平台用户按钮' },
      { selector: '.btn-edit, a:has-text("编辑")', name: '编辑用户按钮' },
      { selector: '.btn-delete, button:has-text("删除")', name: '删除用户按钮' },
      { selector: '.btn:has-text("权限管理")', name: '权限管理按钮' }
    ];

    for (const button of platformUserButtons) {
      await testButton(page, button.selector, button.name, '平台用户管理');
      await page.goto(`${TEST_CONFIG.baseURL}/platform-users`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 14. AI配置模块全面测试
  test('AI配置模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/ai-config`, 'AI配置主页', 'AI配置');

    const aiConfigButtons = [
      { selector: '.btn:has-text("保存配置"), button[type="submit"]', name: '保存AI配置按钮' },
      { selector: '.btn:has-text("测试连接")', name: '测试AI连接按钮' },
      { selector: '.btn:has-text("重置"), .btn-reset', name: '重置配置按钮' }
    ];

    for (const button of aiConfigButtons) {
      await testButton(page, button.selector, button.name, 'AI配置');
      await page.goto(`${TEST_CONFIG.baseURL}/ai-config`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 15. 商城管理模块全面测试
  test('商城管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall`, '商城管理主页', '商城管理');

    const mallButtons = [
      { selector: '.btn:has-text("商品管理"), a[href*="/mall/products"]', name: '商品管理按钮' },
      { selector: '.btn:has-text("分类管理"), a[href*="/mall/categories"]', name: '分类管理按钮' },
      { selector: '.btn:has-text("订单管理"), a[href*="/mall/orders"]', name: '订单管理按钮' },
      { selector: '.btn:has-text("库存管理"), a[href*="/mall/inventory"]', name: '商城库存管理按钮' }
    ];

    for (const button of mallButtons) {
      await testButton(page, button.selector, button.name, '商城管理');
      await page.goto(`${TEST_CONFIG.baseURL}/mall`);
      await page.waitForLoadState('networkidle');
    }

    // 测试商城子页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/products`, '商品管理页面', '商城管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/categories`, '分类管理页面', '商城管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/orders`, '订单管理页面', '商城管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/mall/inventory`, '商城库存页面', '商城管理');
  });

  // 16. 鹅价管理模块全面测试
  test('鹅价管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/goose-prices`, '鹅价管理主页', '鹅价管理');

    const priceButtons = [
      { selector: '.btn:has-text("添加价格"), a[href*="/goose-prices/create"]', name: '添加价格按钮' },
      { selector: '.btn-edit, a:has-text("编辑")', name: '编辑价格按钮' },
      { selector: '.btn-delete, button:has-text("删除")', name: '删除价格按钮' },
      { selector: '.btn:has-text("查看趋势"), a[href*="/goose-prices/trends"]', name: '查看趋势按钮' },
      { selector: '.btn:has-text("导出"), .btn-export', name: '导出价格数据按钮' }
    ];

    for (const button of priceButtons) {
      await testButton(page, button.selector, button.name, '鹅价管理');
      await page.goto(`${TEST_CONFIG.baseURL}/goose-prices`);
      await page.waitForLoadState('networkidle');
    }

    // 测试鹅价子页面
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/goose-prices/create`, '添加价格页面', '鹅价管理');
    await testPageAccess(page, `${TEST_CONFIG.baseURL}/goose-prices/trends`, '价格趋势页面', '鹅价管理');
  });

  // 17. 租户统计模块全面测试
  test('租户统计模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/tenant-stats`, '租户统计主页', '租户统计');

    const statsButtons = [
      { selector: '.btn:has-text("刷新统计")', name: '刷新统计按钮' },
      { selector: '.btn:has-text("导出"), .btn-export', name: '导出统计数据按钮' },
      { selector: '.btn:has-text("生成报告")', name: '生成统计报告按钮' }
    ];

    for (const button of statsButtons) {
      await testButton(page, button.selector, button.name, '租户统计');
      await page.goto(`${TEST_CONFIG.baseURL}/tenant-stats`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 18. 价格管理模块全面测试
  test('价格管理模块全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    await testPageAccess(page, `${TEST_CONFIG.baseURL}/pricing`, '价格管理主页', '价格管理');

    const pricingButtons = [
      { selector: '.btn:has-text("添加方案"), a[href*="/pricing/create"]', name: '添加价格方案按钮' },
      { selector: '.btn-edit, a:has-text("编辑")', name: '编辑方案按钮' },
      { selector: '.btn-delete, button:has-text("删除")', name: '删除方案按钮' },
      { selector: '.btn:has-text("激活"), .btn:has-text("停用")', name: '激活/停用方案按钮' }
    ];

    for (const button of pricingButtons) {
      await testButton(page, button.selector, button.name, '价格管理');
      await page.goto(`${TEST_CONFIG.baseURL}/pricing`);
      await page.waitForLoadState('networkidle');
    }
  });

  // 19. 综合导航测试
  test('系统导航菜单全面测试', async ({ page }) => {
    await loginAsAdmin(page);

    // 测试主要导航链接
    const navLinks = [
      { selector: 'a[href="/dashboard"]', name: '仪表板导航', module: '导航' },
      { selector: 'a[href="/users"]', name: '用户管理导航', module: '导航' },
      { selector: 'a[href="/tenants"]', name: '租户管理导航', module: '导航' },
      { selector: 'a[href="/flocks"]', name: '鹅群管理导航', module: '导航' },
      { selector: 'a[href="/production"]', name: '生产管理导航', module: '导航' },
      { selector: 'a[href="/health"]', name: '健康管理导航', module: '导航' },
      { selector: 'a[href="/finance"]', name: '财务管理导航', module: '导航' },
      { selector: 'a[href="/inventory"]', name: '库存管理导航', module: '导航' },
      { selector: 'a[href="/reports"]', name: '报表系统导航', module: '导航' },
      { selector: 'a[href="/system"]', name: '系统管理导航', module: '导航' }
    ];

    for (const link of navLinks) {
      await testButton(page, link.selector, link.name, link.module);
      await page.waitForTimeout(1000);
    }
  });

});
