{"name": "smart-goose-saas-platform", "version": "2.0.0", "description": "智慧养鹅SaaS平台 - 完整多租户架构", "architecture": "multi-tenant", "last_updated": "2025-08-27", "components": {"platform_database": {"version": "2.0.0", "database": "smart_goose_saas_platform", "tables": ["platform_admins", "tenants", "subscription_plans", "tenant_users", "system_config", "platform_logs", "usage_stats", "payments", "notifications", "api_keys"], "status": "active"}, "tenant_management_service": {"version": "2.0.0", "file": "backend/services/tenant-management.js", "features": ["multi_tenant_database_isolation", "subscription_management", "api_key_management", "usage_tracking", "billing_integration"], "status": "active"}, "admin_backend": {"version": "2.0.0", "port": 4000, "routes": "backend/saas-admin/routes/complete-admin.js", "features": ["tenant_crud", "user_management", "financial_overview", "system_settings", "dashboard_analytics"], "status": "active"}, "main_backend": {"version": "1.5.0", "port": 3000, "file": "backend/app.js", "status": "active"}, "wechat_miniprogram": {"version": "1.5.0", "framework": "wechat_miniprogram", "status": "active"}}, "database_schema": {"platform_db": "smart_goose_saas_platform", "tenant_db_pattern": "tenant_{tenant_code}_db", "isolation_level": "database_per_tenant"}, "api_endpoints": {"admin_api": "http://localhost:4000", "tenant_api": "http://localhost:3000", "documentation": "pending"}, "deployment": {"environment": "development", "ready_for_production": false, "setup_script": "backend/saas-complete-database-setup.js"}, "dependencies": {"node_version": ">=16.0.0", "mysql_version": ">=8.0.0", "required_npm_packages": ["express", "mysql2", "bcryptjs", "express-session", "ejs", "cors"]}, "features": {"multi_tenancy": true, "subscription_billing": true, "api_management": true, "usage_analytics": true, "role_based_access": true, "database_isolation": true}, "migration_notes": {"from_version": "1.0.0", "breaking_changes": ["Database schema completely restructured for multi-tenancy", "Old single-tenant tables deprecated", "New tenant management service required", "Admin backend routes consolidated"], "migration_required": true}}