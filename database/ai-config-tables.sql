-- AI配置管理相关表结构
-- 创建时间: 2025-08-27

-- 1. AI配置表
CREATE TABLE IF NOT EXISTS `ai_configs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `provider` varchar(50) NOT NULL COMMENT 'AI服务提供商 (zhipu, siliconflow, openai, qianwen)',
  `name` varchar(100) NOT NULL COMMENT '服务商显示名称',
  `api_key` text NOT NULL COMMENT 'API密钥（加密存储）',
  `base_url` varchar(255) NOT NULL COMMENT 'API基础URL',
  `models` json NOT NULL COMMENT '支持的模型配置',
  `max_tokens` int NOT NULL DEFAULT 4096 COMMENT '最大token数',
  `temperature` decimal(3,2) NOT NULL DEFAULT 0.70 COMMENT '温度参数',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认服务商',
  `priority` int NOT NULL DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
  `config` json DEFAULT NULL COMMENT '其他配置参数',
  `created_by` int DEFAULT NULL COMMENT '创建者ID',
  `updated_by` int DEFAULT NULL COMMENT '更新者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_priority` (`priority`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI配置表';

-- 2. AI使用统计表
CREATE TABLE IF NOT EXISTS `ai_usage_stats` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `flock_id` int DEFAULT NULL COMMENT '鹅群ID',
  `provider` varchar(50) NOT NULL COMMENT 'AI服务提供商',
  `model` varchar(100) NOT NULL COMMENT '使用的模型',
  `scenario` varchar(100) NOT NULL COMMENT '使用场景 (AI_INVENTORY_COUNTING, IMAGE_RECOGNITION等)',
  `feature` varchar(100) DEFAULT NULL COMMENT '功能模块 (ai_inventory, health_diagnosis等)',
  `service_type` varchar(50) DEFAULT NULL COMMENT '服务类型',
  `usage_count` int NOT NULL DEFAULT 1 COMMENT '使用次数',
  `request_tokens` int NOT NULL DEFAULT 0 COMMENT '请求token数',
  `response_tokens` int NOT NULL DEFAULT 0 COMMENT '响应token数',
  `total_tokens` int NOT NULL DEFAULT 0 COMMENT '总token数',
  `cost` decimal(10,6) NOT NULL DEFAULT 0.000000 COMMENT '成本（元）',
  `response_time` int DEFAULT NULL COMMENT '响应时间（毫秒）',
  `success` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否成功',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `request_data` json DEFAULT NULL COMMENT '请求数据（脱敏后）',
  `response_data` json DEFAULT NULL COMMENT '响应数据（脱敏后）',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '请求IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_scenario` (`scenario`),
  KEY `idx_success` (`success`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_created` (`user_id`, `created_at`),
  KEY `idx_provider_created` (`provider`, `created_at`),
  KEY `idx_flock_id` (`flock_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI使用统计表';

-- 插入默认AI配置（智谱AI和硅基流动）
INSERT INTO `ai_configs` (`provider`, `name`, `api_key`, `base_url`, `models`, `max_tokens`, `temperature`, `enabled`, `is_default`, `priority`, `config`, `created_by`) VALUES
('zhipu', '智谱AI默认配置', 'dummy_encrypted_key_zhipu', 'https://open.bigmodel.cn/api/paas/v4/', '["glm-4", "glm-4v", "glm-3-turbo"]', 4096, 0.70, 1, 1, 100, '{"description": "智谱AI默认配置，需要配置真实API密钥"}', 1),
('siliconflow', '硅基流动默认配置', 'dummy_encrypted_key_siliconflow', 'https://api.siliconflow.cn/v1/', '["qwen/Qwen2-72B-Instruct", "deepseek-ai/DeepSeek-V2.5", "meta-llama/Meta-Llama-3.1-405B-Instruct"]', 4096, 0.70, 1, 0, 90, '{"description": "硅基流动默认配置，需要配置真实API密钥"}', 1)
ON DUPLICATE KEY UPDATE
`updated_at` = CURRENT_TIMESTAMP;

-- 检查表是否创建成功
SHOW TABLES LIKE 'ai_%';

-- 显示表结构
DESCRIBE ai_configs;
DESCRIBE ai_usage_stats;